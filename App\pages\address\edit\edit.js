const app = getApp();
var api = require('../../../utils/api.js');
var sender = require('../../../utils/sender.js');

Page({
  data: {
    isEdit: false,
    addressId: null,
    userId: null,
    address: {
      recipient: '',
      phone: '',
      province: '',
      city: '',
      county: '',
      town: '',
      detail: '',
      isDefault: false
    }
  },

  onLoad: function (options) {
    // 获取当前用户ID
    const userInfo = wx.getStorageSync('UserInfo');
    this.setData({ userId: userInfo.id });
    if (options.id) {
      // 编辑模式
      this.setData({
        isEdit: true,
        addressId: options.id
      });
      
      this.loadAddressDetail(options.id);
    }
  },
  
  // 加载地址详情
  loadAddressDetail: function (id) {
    wx.showLoading({ title: '加载中...' });
    sender.requestUrl({
      url: api.api_address_detail,
      method: 'GET',
      params: { id: id }
    }, (data) => {
      wx.hideLoading();
      this.setData({ address: data });
    });
  },
  
  // 输入收件人
  inputName: function (e) {
    this.setData({ 'address.recipient': e.detail.value });
  },
  
  // 输入手机号
  inputPhone: function (e) {
    this.setData({ 'address.phone': e.detail.value });
  },
  
  // 选择地区
  regionChange: function (e) {
    this.setData({
      'address.province': e.detail.value[0],
      'address.city': e.detail.value[1],
      'address.county': e.detail.value[2]
    });
  },
  
  // 输入详细地址
  inputDetailAddress: function (e) {
    this.setData({ 'address.detail': e.detail.value });
  },
  
  // 切换默认地址
  switchDefault: function (e) {
    this.setData({
      'address.isDefault': e.detail.value
    });
  },
  
  // 保存地址
  saveAddress: function () {
    const address = this.data.address;
    // 表单验证
    if (!address.recipient) {
      wx.showToast({ title: '请输入收货人姓名', icon: 'none' }); return;
    }
    if (!address.phone) {
      wx.showToast({ title: '请输入手机号码', icon: 'none' }); return;
    }
    if (!/^1\d{10}$/.test(address.phone)) {
      wx.showToast({ title: '手机号码格式不正确', icon: 'none' }); return;
    }
    if (!address.province || !address.city || !address.county) {
      wx.showToast({ title: '请选择所在地区', icon: 'none' }); return;
    }
    if (!address.detail) {
      wx.showToast({ title: '请输入详细地址', icon: 'none' }); return;
    }
    // 调用接口保存
    const url = this.data.isEdit ? api.api_address_update : api.api_address_add;
    const method = this.data.isEdit ? 'PUT' : 'POST';
    wx.showLoading({ title: '保存中...' });
    sender.requestUrl({ url: url, method: method, data: { ...address, userId: this.data.userId, id: this.data.isEdit ? this.data.addressId : undefined } }, () => {
      wx.hideLoading();
      wx.showToast({ title: this.data.isEdit ? '修改成功' : '添加成功', icon: 'success' });
      wx.navigateBack();
    });
  }
}) 