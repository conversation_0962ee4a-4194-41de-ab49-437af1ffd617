<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.DictDao">

    <resultMap type="com.xh.system.domain.entity.Dict" id="DictMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="nameEn" column="name_en" jdbcType="VARCHAR"/>
        <result property="nameCn" column="name_cn" jdbcType="VARCHAR"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
        <result property="cssClass" column="css_class" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="detail">
        id, name_en, name_cn,note,css_class, create_time, update_time, parent_id, sort
    </sql>

    <select id="queryById" resultMap="DictMap">
        select
          <include refid="detail"></include>
        from sys_dict
        where id = #{id}
    </select>

    <select id="queryByList" resultMap="DictMap">
        select
          <include refid="detail"></include>
        from sys_dict
        <where>
            <if test="nameEn != null and nameEn != ''">
                and name_en like CONCAT('%',#{nameEn},'%')
            </if>
            <if test="nameCn != null and nameCn != ''">
                and name_cn like CONCAT('%',#{nameCn},'%')
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
        </where>
    </select>

    <select id="queryDictByNameEn" resultMap="DictMap">
        select * from sys_dict where parent_id = (select id from sys_dict where name_en = #{nameEn} and parent_id = 0 limit 0,1)
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="nameEn !=null and '' != nameEn">
                name_en,
            </if>
            <if test="nameCn !=null and '' != nameCn">
                name_cn,
            </if>
            <if test="cssClass !=null and '' != cssClass">
                css_class,
            </if>
            <if test="note !=null and '' != note">
                note,
            </if>
            <if test="parentId !=null">
                parent_id,
            </if>
            <if test="sort !=null">
                sort,
            </if>
            <if test="note !=null and '' != note">
                note,
            </if>
            create_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="nameEn !=null and '' != nameEn">
                #{nameEn},
            </if>
            <if test="nameCn !=null and '' != nameCn">
                #{nameCn},
            </if>
            <if test="cssClass !=null and '' != cssClass">
                #{cssClass},
            </if>
            <if test="note !=null and '' != note">
                #{note},
            </if>
            <if test="parentId !=null">
                #{parentId},
            </if>
            <if test="sort !=null">
                #{sort},
            </if>
            <if test="note !=null and '' != note">
                #{note},
            </if>
                now(),
        </trim>
    </insert>

    <update id="update">
        update sys_dict
        <set>
            <if test="nameEn != null and nameEn != ''">
                name_en = #{nameEn},
            </if>
            <if test="nameCn != null and nameCn != ''">
                name_cn = #{nameCn},
            </if>
            <if test="cssClass !=null and '' != cssClass">
                css_class = #{cssClass},
            </if>
            <if test="note !=null and '' != note">
                note = #{note},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="note !=null and '' != note">
                note = #{note},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from sys_dict where id = #{id}
    </delete>
</mapper>