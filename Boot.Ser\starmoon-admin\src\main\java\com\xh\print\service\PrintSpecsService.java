package com.xh.print.service;

import com.github.pagehelper.PageInfo;
import com.xh.print.domain.entity.PrintAttr;
import com.xh.print.domain.entity.PrintSku;
import com.xh.print.domain.form.PrintSettingsCreateForm;
import com.xh.print.domain.form.PrintSkuEditForm;

import java.util.List;

public interface PrintSpecsService {

    PageInfo<PrintSku> queryByPage();

    List<PrintSku> queryByList();

    List<PrintAttr> queryAttrByList();

    PrintSku queryById(Long id);

    boolean generate(PrintSettingsCreateForm form);

    boolean update(PrintSkuEditForm form);

    boolean update(PrintSku printSku);

}
