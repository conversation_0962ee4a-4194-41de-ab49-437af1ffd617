package com.xh.system.service;

import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.DictCreateDTO;
import com.xh.system.domain.dto.DictDTO;
import com.xh.system.domain.dto.DictEditDTO;
import com.xh.system.domain.entity.Dict;

import java.util.List;

/**
 * (Dict)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 13:11:11
 */
public interface DictService {
    Dict queryById(Long id);

    List<Dict> queryByList(String nameEn, String nameCn, Integer status, Long parentId);
    PageInfo<DictDTO> queryByPage(String nameEn, String nameCn, Integer status, Long parentId);

    Long insert(DictCreateDTO dto);

    boolean update(DictEditDTO dto);

    boolean deleteById(Long id);

    List<Dict> queryDictByNameEn(String nameEn);

}
