import request from '@/utils/request'

// 分页查询系统设置
export function listSettings(params) {
  return request({ url: '/manage-api/v1/settings/page', method: 'get', params })
}

// 通过ID查询系统设置
export function getSettings(id) {
  return request({ url: '/manage-api/v1/settings', method: 'get', params: { id } })
}

// 新增系统设置
export function addSettings(data) {
  return request({ url: '/manage-api/v1/settings', method: 'post', data })
}

// 编辑系统设置
export function editSettings(data) {
  return request({ url: '/manage-api/v1/settings', method: 'put', data })
}

// 删除系统设置
export function deleteSettings(id) {
  return request({ url: '/manage-api/v1/settings', method: 'delete', params: { id } })
} 