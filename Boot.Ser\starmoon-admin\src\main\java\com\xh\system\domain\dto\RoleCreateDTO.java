package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class RoleCreateDTO {

    @Schema(description = "角色名")
    @NotBlank(message = "角色名不能为空")
    private String roleName;

    @Schema(description = "角色代码")
    @NotBlank(message = "角色码不能为空")
    private String roleCode;

    @Schema(description = "权限范围")
    private Integer dataScope;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "权限ID（菜单id）")
    private Long[] permissions;
}
