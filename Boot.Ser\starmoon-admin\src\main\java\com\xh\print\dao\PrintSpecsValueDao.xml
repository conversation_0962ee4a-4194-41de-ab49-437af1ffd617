<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.print.dao.PrintSpecsValueDao">

    <resultMap type="com.xh.print.domain.entity.PrintSpecsValue" id="PrintSpecsValueMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="specsNameId" column="specs_name_id" jdbcType="INTEGER"/>
        <result property="specsValue" column="specs_value" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PrintSpecsValueMap">
        select id,
               specs_name_id,
               specs_value,
               description
        from print_specs_value
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="PrintSpecsValueMap">
        select
        id, specs_name_id, specs_value, description
        from print_specs_value
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="specsNameId != null">
                and specs_name_id = #{specsNameId}
            </if>
            <if test="specsValue != null and specsValue != ''">
                and specs_value = #{specsValue}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from print_specs_value
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="specsNameId != null">
                and specs_name_id = #{specsNameId}
            </if>
            <if test="specsValue != null and specsValue != ''">
                and specs_value = #{specsValue}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into print_specs_value(specs_name_id, specs_value, description)
        values (#{specsNameId}, #{specsValue}, #{description})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into print_specs_value(specs_name_id, specs_value, description)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.specsNameId}, #{entity.specsValue}, #{entity.description})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into print_specs_value(specs_name_id, specs_value, description)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.specsNameId}, #{entity.specsValue}, #{entity.description})
        </foreach>
        on duplicate key update
        specs_name_id = values(specs_name_id),
        specs_value = values(specs_value),
        description = values(description)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update print_specs_value
        <set>
            <if test="specsNameId != null">
                specs_name_id = #{specsNameId},
            </if>
            <if test="specsValue != null and specsValue != ''">
                specs_value = #{specsValue},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from print_specs_value
        where id = #{id}
    </delete>
    <delete id="delete">
        delete
        from print_specs_value
    </delete>

</mapper>

