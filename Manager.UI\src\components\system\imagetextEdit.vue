<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="45%" class="imagetext-edit-dialog">
    <el-form :model="imagetextModel" :rules="rules" ref="formRef" label-width="100px" class="imagetext-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="标题" prop="title">
            <el-input v-model="imagetextModel.title" maxlength="100" show-word-limit placeholder="请输入标题" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="imagetextModel.type" placeholder="请选择类型" style="width: 100%;">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
						</el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="图片" prop="imageUrl">
            <el-upload class="avatar-uploader" :show-file-list="false" :action="uploadUrl" :headers="headers" :on-success="uploadSuccess" list-type="picture-card">
              <img v-if="imageShow" :src="imageUrl" style="width: 100%;height: 100%;" />
              <el-button v-else size="small" type="primary">点击上传</el-button>
            </el-upload>

          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跳转链接" prop="link">
            <el-input v-model="imagetextModel.link" placeholder="请输入跳转链接" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="imagetextModel.sort" :min="0" :max="9999" style="width: 100%;" />
          </el-form-item>
        </el-col>
      
        <el-col :span="24">
          <el-form-item label="扩展数据" prop="extendData">
            <el-input type="textarea" v-model="imagetextModel.extendData" placeholder="请输入扩展数据" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容" prop="content">
            <wang-editor ref="editorOne" v-model="imagetextModel.content" @change="change" style="min-height: 200px;width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addImagetext, editImagetext } from '@/api/system/imagetext'
import wangEditor from '@/components/wangEditor/index.vue'
import mitt from '@/utils/mitt'

export default {
  name: 'imagetextEdit',
  components: { wangEditor },
  props: ['typeList'],
  data() {
    return {
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      headers: {
        'Authorization': JSON.parse(localStorage.getItem("token")).access_token
      },
      imagetextModel: {
        imageUrl: false
      },
      imageShow: false,
      imageUrl: '',
      dialog: {
        show: false,
        title: '',
        imagePreview: false,
        imagePreviewUrl: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ]
      },
      uploadData: {}
    }
  },
  watch: {
    imagetextModel: {
      handler(oldVa, newVal) {
        console.log(oldVa)
        console.log(newVal)
      },
      deep: true
    }

  },
  methods: {
    change(res) {
      this.imagetextModel.content = res
    },
    uploadSuccess(res) {
      if (res.code == 0) {
        this.imageShow = true
        this.imageUrl = this.imgServer + res.data
        console.log(this.imageUrl)
        this.imagetextModel.imageUrl = res.data
      } else {
        this.$message.error(res.message)
      }
    },
    
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.imagetextModel.id ? editImagetext : addImagetext
        api(this.imagetextModel).then(() => {
          this.$emit('search')
          this.dialog.show = false
        })
      })
    },

  },
  mounted() {
    mitt.on('openImagetextEdit', (data) => {
      if (data.imageUrl) {
        this.imageShow = true
        this.imageUrl = this.imgServer + data.imageUrl
      }
      setTimeout(() => {
        if (data.content) {
          this.$refs.editorOne.setHtml(data.content)
        }
      }, 10)
      this.imagetextModel = data
      this.dialog.show = true
      this.dialog.title = '编辑图文信息'
    })

    mitt.on('openImagetextAdd', () => {
      this.imagetextModel = {}
      this.dialog.show = true
      this.dialog.title = '新增图文信息'
    })
  },
  beforeDestroy() {
    mitt.off('openImagetextEdit')
    mitt.off('openImagetextAdd')
    this.imageShow = false
    this.imageUrl = ''
  }
}
</script>

<style scoped>
.imagetext-edit-dialog>>>.el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}

.imagetext-edit-form {
  padding: 0 10px;
}

.avatar-uploader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 60px;
}

.avatar-uploader .avatar {
  width: 120px;
  height: 80px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.avatar-uploader-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafbfc;
  color: #bbb;
  cursor: pointer;
  transition: border-color 0.2s;
}

.avatar-uploader-placeholder:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 32px;
}

.avatar-uploader-text {
  font-size: 12px;
  margin-top: 4px;
  color: #888;
}

.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}

.avatar-preview-wrapper {
  position: relative;
  display: inline-block;
}

.avatar-preview-icon {
  position: absolute;
  right: 36px;
  top: 8px;
  font-size: 20px;
  color: #409EFF;
  background: #fff;
  border-radius: 50%;
  padding: 2px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 2;
}

.avatar-delete-icon {
  position: absolute;
  right: 8px;
  top: 8px;
  font-size: 20px;
  color: #F56C6C;
  background: #fff;
  border-radius: 50%;
  padding: 2px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 2;
  display: none;
}

.avatar-preview-wrapper:hover .avatar-delete-icon {
  display: block;
}

.avatar-preview-wrapper:hover .avatar-preview-icon {
  color: #66b1ff;
}

.avatar-preview-wrapper .avatar {
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.avatar-preview-wrapper .avatar:hover {
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
}
</style> 