package com.xh.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.system.dao.AddressDao;
import com.xh.system.domain.dto.AddressCreateDTO;
import com.xh.system.domain.dto.AddressEditDTO;
import com.xh.system.domain.entity.Address;
import com.xh.system.service.AddressService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AddressServiceImpl implements AddressService {

    @Autowired
    private AddressDao addressDao;

    @Override
    public Address queryById(Long id) {
        return addressDao.queryById(id);
    }

    @Override
    public PageInfo<Address> queryByPage(Integer pageNum, Integer pageSize, Long userId) {
        PageHelper.startPage(pageNum, pageSize);
        List<Address> list = addressDao.queryByList(userId);
        return new PageInfo<>(list);
    }

    @Override
    public Long insert(AddressCreateDTO dto) {
        Address entity = new Address();
        BeanUtils.copyProperties(dto, entity);
        addressDao.insert(entity);
        return entity.getId();
    }

    @Override
    public boolean update(AddressEditDTO dto) {
        Address entity = new Address();
        BeanUtils.copyProperties(dto, entity);
        return addressDao.update(entity) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return addressDao.deleteById(id) > 0;
    }
} 