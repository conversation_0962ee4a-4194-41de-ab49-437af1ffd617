package com.xh.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class RoleVO {

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "数据权限")
    private String dataScope;

    @Schema(description = "状态")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "权限ID（菜单id）")
    private Long[] permissions;
}
