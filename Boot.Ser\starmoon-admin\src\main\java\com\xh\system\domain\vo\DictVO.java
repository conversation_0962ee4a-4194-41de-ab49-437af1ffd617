package com.xh.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DictVO {

    @Schema(description = "字典ID")
    private Long id;

    @Schema(description = "英文名(代码)")
    private String nameEn;

    @Schema(description = "中文名")
    private String nameCn;

    @Schema(description = "样式")
    private String cssClass;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String note;

    private List<DictVO> children;
}
