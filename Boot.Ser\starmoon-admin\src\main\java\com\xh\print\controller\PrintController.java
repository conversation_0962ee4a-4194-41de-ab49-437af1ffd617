package com.xh.print.controller;

import com.github.pagehelper.PageInfo;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.print.domain.entity.PrintSku;
import com.xh.print.domain.form.PrintSettingsCreateForm;
import com.xh.print.domain.form.PrintSkuEditForm;
import com.xh.print.service.PrintSpecsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 商品交易单元(PrintSku)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-07 15:33:55
 */
@Tag(name = "管理端")
@RestController
@RequestMapping("manage-api/v1/print/sku")
public class PrintController {
 
    @Autowired
    private PrintSpecsService printSpecsService;

    @GetMapping("page")
    public ResponseEntity<PageInfo<PrintSku>> queryByPage() {
        return ResponseEntity.success(printSpecsService.queryByPage());
    }
  
    @GetMapping
    public ResponseEntity<PrintSku> queryById(Long id) {
        return ResponseEntity.success(printSpecsService.queryById(id));
    }

    @PostMapping
    public ResponseEntity<Boolean> generate(@Valid @RequestBody PrintSettingsCreateForm form) {
        return ResponseEntity.success(printSpecsService.generate(form));
    }

    @PutMapping
    public ResponseEntity<Boolean> update(@Valid @RequestBody PrintSkuEditForm form) {
        return ResponseEntity.success(printSpecsService.update(form));
    }

}

