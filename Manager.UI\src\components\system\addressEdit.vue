<template>
  <el-dialog width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
    <el-form :model="addressModel" :rules="rules" ref="form" label-width="120px">
      <el-form-item label="收件人" prop="recipient">
        <el-input v-model="addressModel.recipient" placeholder="收件人"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="addressModel.phone" placeholder="手机号"></el-input>
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-input v-model="addressModel.province" placeholder="省份"></el-input>
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input v-model="addressModel.city" placeholder="城市"></el-input>
      </el-form-item>
      <el-form-item label="区县" prop="county">
        <el-input v-model="addressModel.county" placeholder="区县"></el-input>
      </el-form-item>
      <el-form-item label="乡镇" prop="town">
        <el-input v-model="addressModel.town" placeholder="乡镇"></el-input>
      </el-form-item>
      <el-form-item label="详细地址" prop="detail">
        <el-input v-model="addressModel.detail" type="textarea" :rows="3" placeholder="详细地址"></el-input>
      </el-form-item>
      <el-form-item label="默认地址" prop="isDefault">
        <el-switch v-model="addressModel.isDefault"></el-switch>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input-number v-model="addressModel.userId" :min="1" placeholder="用户ID"></el-input-number>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import mitt from '@/utils/mitt'
import { getAddress, addAddress, editAddress } from '@/api/system/address'
export default {
  name: 'AddressEdit',
  data() {
    return {
      loading: false,
      addressModel: {
        id: null,
        recipient: '',
        phone: '',
        province: '',
        city: '',
        county: '',
        town: '',
        detail: '',
        isDefault: false,
        userId: null
      },
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        recipient: [{ required: true, message: '请输入收件人', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        province: [{ required: true, message: '请输入省份', trigger: 'blur' }],
        city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
        county: [{ required: true, message: '请输入区县', trigger: 'blur' }],
        detail: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        userId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    mitt.on('openAddressAdd', () => {
      this.addressModel = { id: null, recipient: '', phone: '', province: '', city: '', county: '', town: '', detail: '', isDefault: false, userId: null }
      this.dialog.title = '新增地址'
      this.dialog.show = true
    })
    mitt.on('openAddressEdit', (data) => {
      this.addressModel = { ...data }
      this.dialog.title = '编辑地址'
      this.dialog.show = true
    })
  },
  beforeUnmount() {
    mitt.off('openAddressAdd')
    mitt.off('openAddressEdit')
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const apiFn = this.addressModel.id ? editAddress : addAddress
          apiFn(this.addressModel)
            .then(() => {
              this.$message.success('保存成功')
              this.dialog.show = false
              mitt.emit('searchAddress')
            })
            .catch(err => {
              this.$message.error(err.data?.errorMessage || '保存失败')
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style> 