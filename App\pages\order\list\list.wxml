<view class="container">
  <custom-nav title="我的订单" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 订单筛选Tab -->
  <view class="order-tabs">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}" 
      wx:for="{{tabs}}" 
      wx:key="index" 
      bindtap="switchTab" 
      data-index="{{index}}">
      {{item}}
      <view class="tab-line" wx:if="{{currentTab === index}}"></view>
    </view>
  </view>
  
  <!-- 订单列表 -->
  <scroll-view scroll-y="true" class="order-scroll">
    <!-- 订单列表 -->
    <view class="order-list" wx:if="{{orderList.length > 0}}">
      <view class="order-item" wx:for="{{orderList}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <view class="order-header">
          <text class="order-id">订单编号：{{item.id}}</text>
          <text class="order-status">{{item.statusText}}</text>
        </view>
        
        <!-- 产品列表 -->
        <view class="product-list" wx:if="{{item.detailList && item.detailList.length > 0}}">
          <view class="product-item" wx:for="{{item.detailList}}" wx:for-item="product" wx:key="id">
            <!-- 根据文件类型显示对应图标 -->
            <view class="file-icon-wrapper">
              <image wx:if="{{product.fileType === 'pdf'}}" class="file-icon" src="/images/icon/pdf_icon.png" mode="aspectFit"></image>
              <image wx:elif="{{product.fileType === 'doc' || product.fileType === 'docx'}}" class="file-icon" src="/images/icon/word_icon.png" mode="aspectFit"></image>
              <image wx:elif="{{product.fileType === 'xls' || product.fileType === 'xlsx'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
              <image wx:elif="{{product.fileType === 'ppt' || product.fileType === 'pptx'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
              <image wx:elif="{{product.fileType === 'txt'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
              <image wx:elif="{{product.fileType === 'jpg' || product.fileType === 'jpeg' || product.fileType === 'png' || product.fileType === 'gif'}}" class="file-icon" src="/images/icon/image_icon.png" mode="aspectFit"></image>
              <image wx:else class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
            </view>
            <view class="product-info">
              <view class="product-name">{{product.fileName}}</view>
              <view class="product-specs">{{product.specs}} 第{{product.pageNum}}-{{product.pageSize}}页/{{product.quantity}}份</view>
            </view>
            <view class="product-price">¥{{product.unitPrice}}</view>
          </view>
        </view>
        
        <view class="order-footer">
          <view class="order-time">{{item.createTimeFormatted}}</view>
          <view class="order-total">
            <text class="total-price">¥{{item.payAmount}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 无订单提示 -->
    <view class="empty-order" wx:if="{{orderList.length === 0 && !loading}}">
      <view class="empty-icon">📋</view>
      <text class="empty-text">暂无相关订单</text>
    </view>
    
    <!-- 加载中 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多了 -->
    <view class="no-more" wx:if="{{!hasMore && orderList.length > 0}}">
      <text>没有更多订单了</text>
    </view>
  </scroll-view>
</view> 