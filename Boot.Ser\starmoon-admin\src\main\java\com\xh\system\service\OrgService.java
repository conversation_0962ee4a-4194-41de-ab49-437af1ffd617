package com.xh.system.service;


import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.OrgCreateDTO;
import com.xh.system.domain.dto.OrgDTO;
import com.xh.system.domain.dto.OrgEditDTO;
import com.xh.system.domain.entity.Org;

import java.util.List;

/**
 * (UserGroup)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 09:45:36
 */
public interface OrgService {

    Org queryById(Long id);

    List<Org> queryByList(String orgName, Integer status);

    PageInfo<OrgDTO> queryByPage(String orgName, Integer status);

    Long insert(OrgCreateDTO dto);

    boolean update(OrgEditDTO dto);

    boolean deleteById(Long id);

}
