package com.xh.common.wechat.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MemberDetail implements Serializable {

    private static final long serialVersionUID = 202310101658001L;


    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "微信用户openid")
    private String openid;

    @Schema(description = "微信用户统一ID")
    private String unionid;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像")
    private String avatarUrl;

    @Schema(description = "生日")
    private Date birthday;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "证件类型")
    private String certificateType;

    @Schema(description = "证件号码")
    private String idCardNumber;

    @Schema(description = "角色")
    private String role;
}
