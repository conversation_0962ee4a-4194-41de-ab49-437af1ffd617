const API_HOST = 'http://127.0.0.1:8081'

/** 资源服务器地址 */
const CDN_IMAGE = API_HOST + '/common-api/v1/file/'

/** 登录URL */
const api_login = API_HOST + '/users-api/v1/auth/token'

/** 用户注册相关接口 */
const api_send_sms_code = API_HOST + '/users-api/v1/auth/send-sms-code'
const api_register = API_HOST + '/users-api/v1/auth/register'

/** 临时文件 */
const api_file_upload = API_HOST + '/users-api/v1/temp-file'
const api_temp_file = API_HOST + '/users-api/v1/temp-file'
const api_temp_file_list = API_HOST + '/users-api/v1/temp-file/list'
/** 图文接口 */
const api_imagetext_list = API_HOST + '/users-api/v1/imagetext/page'
const api_imagetext_detail = API_HOST + '/users-api/v1/imagetext'

/** 打印相关接口 */
const api_print_specs = API_HOST + '/users-api/v1/print/specs'
const api_print_attr = API_HOST + '/users-api/v1/print/attr'

/** 订单相关接口 */
const api_order_pre_order = API_HOST + '/users-api/v1/order/pre-order'
const api_order_detail = API_HOST + '/users-api/v1/order/detail'
const api_order_submit = API_HOST + '/users-api/v1/order/submit'
const api_order_list = API_HOST + '/users-api/v1/order/page'
const api_order_cancel = API_HOST + '/users-api/v1/order/cancel'
const api_order_pay = API_HOST + '/users-api/v1/order/pay'
const api_order_update_address = API_HOST + '/users-api/v1/order/updateAddress'

const api_address_page = API_HOST + '/users-api/v1/address/page'
const api_address_detail = API_HOST + '/users-api/v1/address'
const api_address_add = API_HOST + '/users-api/v1/address'
const api_address_update = API_HOST + '/users-api/v1/address'
const api_address_delete = API_HOST + '/users-api/v1/address'

module.exports = {
  CDN_IMAGE: CDN_IMAGE,
  API_HOST: API_HOST,
  api_login: api_login,
  api_send_sms_code: api_send_sms_code,
  api_register: api_register,
  api_temp_file_list: api_temp_file_list,
  api_temp_file: api_temp_file,
  api_file_upload: api_file_upload,
  api_imagetext_list: api_imagetext_list,
  api_imagetext_detail: api_imagetext_detail,
  api_print_specs: api_print_specs,
  api_print_attr: api_print_attr,
  api_order_pre_order: api_order_pre_order,
  api_order_detail: api_order_detail,
  api_order_submit: api_order_submit,
  api_order_list: api_order_list,
  api_order_cancel: api_order_cancel,
  api_order_pay: api_order_pay,
  api_order_update_address: api_order_update_address,
  api_address_page: api_address_page,
  api_address_detail: api_address_detail,
  api_address_add: api_address_add,
  api_address_update: api_address_update,
  api_address_delete: api_address_delete
}