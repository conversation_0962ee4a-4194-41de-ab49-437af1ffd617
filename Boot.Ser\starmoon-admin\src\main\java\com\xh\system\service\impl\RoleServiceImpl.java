package com.xh.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.xh.system.dao.RoleDao;
import com.xh.system.dao.RoleMenuDao;
import com.xh.system.domain.dto.RoleCreateDTO;
import com.xh.system.domain.dto.RoleDTO;
import com.xh.system.domain.dto.RoleEditDTO;
import com.xh.system.domain.entity.Menu;
import com.xh.system.domain.entity.Role;
import com.xh.system.service.MenuService;
import com.xh.system.service.RoleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 角色信息表(Role)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 09:48:27
 */
@Service
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private RoleMenuDao roleMenuDao;

    @Autowired
    private MenuService menuService;

    @Override
    public RoleDTO queryById(Long id) {
        Role role = roleDao.queryById(id);
        if(role == null){
            return null;
        }
        List<Menu> menuList = menuService.queryByList(null,null,id);
        Long[] menuIds = menuList.stream().map(Menu::getId).toArray(Long[]::new);
        RoleDTO dto = new RoleDTO();
        BeanUtils.copyProperties(role,dto);
        dto.setPermissions(menuIds);
        return dto;
    }

    @Override
    public List<Role> queryByList(String roleName, Integer status) {
        return roleDao.queryByList(roleName,status);
    }

    @Override
    public PageInfo<Role> queryByPage(String roleName, Integer status) {
        List<Role> roleList = roleDao.queryByList(roleName, status);
        PageInfo<Role> pageInfo = new PageInfo<>(roleList);
        return pageInfo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(RoleCreateDTO dto) {
        Role role = new Role();
        BeanUtils.copyProperties(dto,role);

        roleDao.insert(role);

        if(dto.getPermissions() != null && dto.getPermissions().length>0){
            roleMenuDao.insertBatch(role.getId(),dto.getPermissions());
        }
        return role.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(RoleEditDTO dto) {
        Role role = new Role();
        BeanUtils.copyProperties(dto,role);

        if(dto.getPermissions() != null && dto.getPermissions().length>0){
            roleMenuDao.deleteById(role.getId());
            roleMenuDao.insertBatch(role.getId(),dto.getPermissions());
        }

        return roleDao.update(role) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        roleMenuDao.deleteById(id);
        return roleDao.deleteById(id) > 0;
    }

}
