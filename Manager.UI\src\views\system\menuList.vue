<template>
	<div class="page-container">
		<div class="page-content">
			<menu-edit :statusList="statusList" :typeList="typeList" @search="search"></menu-edit>
			<div class="card card--search">
				<div class="search-container">
					<div class="search-form">
						<el-input v-model="searchModel.menuName" placeholder="菜单名" clearable style="width: 200px;" />
					</div>
					<div class="search-buttons">
						<el-button type="primary" @click="search" class="search-btn">搜索</el-button>
						<el-button type="primary" @click="add(0)" class="add-btn">添加</el-button>
					</div>
				</div>
			</div>
			<div class="card card--table">
				<div class="table-col">
					<el-table 
						:data="menuList" 
						row-key="id" 
						style="width: 100%;" 
						class="data-table"
						fit
						border>
						<el-table-column prop="id" label="ID" align="center"/>
						<el-table-column prop="menuName" align="center" label="菜单名称" width="180" />
						<el-table-column prop="path" align="center" label="路径名" width="180" />
						<el-table-column prop="permission" align="center" label="权限" />
						<el-table-column prop="status" align="center" :formatter="formatStatus" label="状态" />
						<el-table-column prop="menuType" align="center" :formatter="formatType" label="类型" />
						<el-table-column align="center" width="220" label="操作">
							<template #default="scope">
								<el-button type="primary" link size="small" @click="edit(scope.row.id)" class="action-btn">编辑</el-button>
								<el-button type="danger" link size="small" @click="deleted(scope.row.id)" class="action-btn">删除</el-button>
								<el-button v-show="scope.row.menuType == 'menu'" type="success" link size="small" @click="add(scope.row.id)" class="action-btn">添加子级</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="pagination-col">
					<el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick"
						@next-click="nextClick" :total="total"></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { listMenu, deleteMenu, getMenu } from "@/api/system/menu"
import { listDictByNameEn } from "@/api/system/dict"
import mitt from "@/utils/mitt";
import menuEdit from "@/components/system/menuEdit.vue"
export default {
	components: { menuEdit },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10
			},
			menuList: [],
			statusList: [],
			typeList: [],
			total: 0
		}
	},
	methods: {
		search() {
			listMenu(this.searchModel)
				.then(res => {
					this.menuList = res.data.data.list
					this.total = res.data.data.total
				}).catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		edit(id) {
			getMenu(id)
				.then(res => {
					mitt.emit('openMenuEdit', res.data.data)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		add(id) {
			mitt.emit('openMenuAdd', id)
		},
		deleted(id) {
			this.$confirm('删除用户, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteMenu(id)
					.then(res => {
						this.search()
						this.$message.success("操作成功")
					}).catch((res) => {
						this.$message.error(err.data.errorMessage)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		formatStatus(row, column, cellValue, index) {
			let result = ''
			for (let item of this.statusList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
				}
			}
			return result
		},
		formatType(row, column, cellValue, index) {
			let result = ''
			for (let item of this.typeList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
				}
			}
			return result
		},
		async init() {
			mitt.off('openMenuEdit')
			mitt.off('openMenuAdd')
			try {
				const [menu_res, status_res, type_res] = await Promise.all([
					listMenu(this.searchModel),
					listDictByNameEn('menu_status'),
					listDictByNameEn('menu_type')
				])
				this.menuList = menu_res.data.data.list
				this.total = menu_res.data.data.total
				this.statusList = status_res.data.data
				this.typeList = type_res.data.data
			} catch (err) {
				this.$message.error(err.data.errorMessage)
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
/* 组件特定样式可以添加在这里 */
</style> 