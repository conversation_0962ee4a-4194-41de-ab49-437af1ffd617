package com.xh.common.utils;


import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * <AUTHOR>
 */
@Slf4j
public class SecurityUtils {

    public static Authentication currentAuthentication(){
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 获取用户身份
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T currentPrincipal(Class<T> clazz){
        return (T)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    /**
     * 获取用户认证客户端
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T currentCredentials(Class<T> clazz){
        return (T)SecurityContextHolder.getContext().getAuthentication().getCredentials();
    }

}
