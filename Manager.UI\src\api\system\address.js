import request from '@/utils/request'

// 分页查询地址列表
export function listAddress(params) {
  return request({
    url: '/manage-api/v1/address/page',
    method: 'get',
    params: params
  })
}

// 获取地址详情
export function getAddress(id) {
  return request({
    url: '/manage-api/v1/address',
    method: 'get',
    params: { id: id }
  })
}

// 添加地址
export function addAddress(data) {
  return request({
    url: '/manage-api/v1/address',
    method: 'post',
    data: data
  })
}

// 编辑地址
export function editAddress(data) {
  return request({
    url: '/manage-api/v1/address',
    method: 'put',
    data: data
  })
}

// 删除地址
export function deleteAddress(id) {
  return request({
    url: '/manage-api/v1/address',
    method: 'delete',
    params: { id: id }
  })
} 