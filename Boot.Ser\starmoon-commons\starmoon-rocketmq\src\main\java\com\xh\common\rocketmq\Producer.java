package com.xh.common.rocketmq;

import org.apache.rocketmq.client.producer.SendCallback;

/**
 * 异步，同步，保证不丢数据
 * <AUTHOR>
 */
public interface Producer {

    /**
     * 发送同步消息
     *
     * @param topic   消息Topic
     * @param message 消息实体
     */
    void syncSend(String topic, String message);

    /**
     * 发送异步消息
     *
     * @param topic   消息Topic
     * @param message 消息实体
     */
    void asyncSend(String topic, String message, SendCallback sendCallback);


    /**
     * 发送异步消息
     *
     * @param topic   消息Topic
     * @param message 消息实体
     * @Param delayLevel 延迟级别(1为1秒，2为5秒，以此类推到18级7200秒) 1,5,10,30,60,120,180,240,300,600,1200,1800,2400,3000,3600,4800,6000,7200
     */
    void asyncSend(String topic, String message, SendCallback sendCallback, int delayLevel);
}
