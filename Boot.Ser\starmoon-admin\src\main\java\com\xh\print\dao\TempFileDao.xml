<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.print.dao.TempFileDao">

    <resultMap type="com.xh.print.domain.entity.TempFile" id="TempFileMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
        <result property="pageNum" column="page_num" jdbcType="INTEGER"/>
        <result property="pageSize" column="page_size" jdbcType="INTEGER"/>
        <result property="quantity" column="quantity" jdbcType="INTEGER"/>
        <result property="specs" column="specs" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="openid" column="openid" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="unit_price" jdbcType="NUMERIC"/>
    </resultMap>

    <select id="queryById" resultMap="TempFileMap">
        SELECT 
        tf.*,
        IFNULL(ps.amount,0) unit_price
        FROM 
        temp_file tf LEFT JOIN print_sku ps ON tf.sku_id = ps.id
        WHERE tf.id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="TempFileMap">
        SELECT
        tf.*,
        IFNULL(ps.amount,0) unit_price
        FROM
        temp_file tf LEFT JOIN print_sku ps ON tf.sku_id = ps.id
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
        </where>

    </select>

    <select id="queryByIdForUsers" resultMap="TempFileMap">
        SELECT 
        tf.*,
        IFNULL(ps.amount,0) unit_price
        FROM 
        temp_file tf LEFT JOIN print_sku ps ON tf.sku_id = ps.id
        WHERE tf.id = #{id} AND tf.openid = #{openid}
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO temp_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null  and '' != fileName">
                file_name,
            </if>
            <if test="filePath != null  and '' != filePath">
                file_path,
            </if>
            <if test="type != null  and '' != type">
                type,
            </if>
            <if test="skuId != null ">
                sku_id,
            </if>
            <if test="pageNum != null ">
                page_num,
            </if>
            <if test="pageSize != null ">
                page_size,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="specs != null  and '' != specs">
                specs,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="openid != null  and '' != openid">
                openid,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null  and '' != fileName">
                #{fileName},
            </if>
            <if test="filePath != null  and '' != filePath">
                #{filePath},
            </if>
            <if test="type != null  and '' != type">
                #{type},
            </if>
            <if test="skuId != null ">
                #{skuId},
            </if>
            <if test="pageNum != null ">
                #{pageNum},
            </if>
            <if test="pageSize != null ">
                #{pageSize},
            </if>
            <if test="quantity != null ">
                #{quantity},
            </if>
            <if test="specs != null  and '' != specs">
                #{specs},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="openid != null  and '' != openid">
                #{openid},
            </if>
        </trim>
    </insert>

    <update id="update">
        update temp_file
        <set>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="filePath != null and filePath != ''">
                file_path = #{filePath},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId},
            </if>
            <if test="pageNum != null">
                page_num = #{pageNum},
            </if>
            <if test="pageSize != null">
                page_size = #{pageSize},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="specs != null and specs != ''">
                specs = #{specs},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="openid != null and openid != ''">
                openid = #{openid},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete
        from temp_file
        where id = #{id}
    </delete>

    <delete id="deleteByIdForUsers">
        delete
        from temp_file
        where id = #{id} and openid = #{openid}
    </delete>

</mapper>

