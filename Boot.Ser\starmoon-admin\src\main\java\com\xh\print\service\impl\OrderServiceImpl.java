package com.xh.print.service.impl;

import com.xh.common.utils.NumberGenerateUtils;
import com.xh.common.utils.SecurityUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.wechat.user.MemberDetail;
import com.xh.print.dao.OrderDao;
import com.xh.print.dao.OrderDetailDao;
import com.xh.print.dao.TempFileDao;
import com.xh.print.domain.dto.OrderDTO;
import com.xh.print.domain.entity.Order;
import com.xh.print.domain.entity.OrderDetail;
import com.xh.print.domain.entity.TempFile;
import com.xh.print.domain.form.OrderSubmitForm;
import com.xh.print.domain.form.PrintFile;
import com.xh.print.domain.search.OrderDetailSearch;
import com.xh.print.domain.search.OrderSearch;
import com.xh.print.service.OrderDetailService;
import com.xh.print.service.OrderService;
import com.xh.print.service.TempFileService;
import com.xh.system.domain.entity.Address;
import com.xh.system.service.AddressService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.util.*;

/**
 * 订单(Order)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:27
 */
@Service
public class OrderServiceImpl implements OrderService {

    private NumberGenerateUtils numberGenerateUtils = new NumberGenerateUtils(0);

    @Autowired
    private OrderDao orderDao;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private TempFileService tempFileService;

    @Autowired
    private TempFileDao tempFileDao;

    @Autowired
    private AddressService addressService;

    @Override
    public OrderDTO queryDetailById(Long id) {
        Order order = orderDao.queryById(id);
        if (order != null) {
            OrderDTO orderDTO = new OrderDTO();
            BeanUtils.copyProperties(order, orderDTO);
            OrderDetailSearch orderDetailSearch = new OrderDetailSearch();
            orderDetailSearch.setOrderId(id);
            List<OrderDetail> orderDetailList = orderDetailService.queryByList(orderDetailSearch);
            orderDTO.setDetailList(orderDetailList);
            return orderDTO;
        }
        return null;
    }

    @Override
    public List<OrderDTO> queryByList(OrderSearch search) {
        // 获取当前用户信息
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        
        // 设置用户ID进行过滤
        search.setUserId(memberDetail.getOpenid());
        
        // 查询订单列表
        List<Order> orders = orderDao.queryByList(search);
        
        // 转换为OrderDTO并包含订单详情
        List<OrderDTO> orderDTOs = new ArrayList<>();
        for (Order order : orders) {
            OrderDTO orderDTO = new OrderDTO();
            BeanUtils.copyProperties(order, orderDTO);
            
            // 查询订单详情
            OrderDetailSearch orderDetailSearch = new OrderDetailSearch();
            orderDetailSearch.setOrderId(order.getId());
            List<OrderDetail> orderDetailList = orderDetailService.queryByList(orderDetailSearch);
            orderDTO.setDetailList(orderDetailList);
            
            orderDTOs.add(orderDTO);
        }
        
        return orderDTOs;
    }

    @Override
    public Long uploadFile(PrintFile printFile) {
        // 这个方法可能需要具体的业务逻辑，暂时返回0
        return 0L;
    }

    @Override
    public boolean update(Order order) {
        return orderDao.update(order) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return orderDao.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public Map<String, Object> submitOrder(OrderSubmitForm form) {
        // 1. 验证临时文件ID列表
        if (form.getTempFileIds() == null || form.getTempFileIds().isEmpty()) {
            throw new ServiceException("请选择要打印的文件");
        }

        // 2. 验证收货地址
        if (form.getAddressId() == null) {
            throw new ServiceException("请选择收货地址");
        }

        // 查询地址信息
        Address address = addressService.queryById(form.getAddressId());
        if (address == null) {
            throw new ServiceException("收货地址不存在");
        }

        // 3. 查询临时文件列表
        List<TempFile> tempFiles = new ArrayList<>();
        for (Long tempFileId : form.getTempFileIds()) {
            TempFile tempFile = tempFileDao.queryById(tempFileId);
            if (tempFile == null) {
                throw new ServiceException("文件不存在: " + tempFileId);
            }
            tempFiles.add(tempFile);
        }

        // 4. 服务端再次验证文件存在性和参数有效性
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        for (TempFile tempFile : tempFiles) {
            if (!memberDetail.getOpenid().equals(tempFile.getOpenid())) {
                throw new ServiceException("无权访问文件: " + tempFile.getId());
            }
            if (tempFile.getSkuId() == null || tempFile.getSkuId() == 0) {
                throw new ServiceException("文件打印参数未设置: " + tempFile.getFileName());
            }
        }

        // 5. 创建订单
        Order order = new Order();
        order.setId(Long.parseLong(numberGenerateUtils.next()));
        order.setUserId(memberDetail.getOpenid());
        order.setStatus(0); // 待支付
        order.setCreateTime(new Date());

        // 将地址信息转换为JSON字符串存储
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String addressJson = objectMapper.writeValueAsString(address);
            order.setAddress(addressJson);
        } catch (JsonProcessingException e) {
            throw new ServiceException("地址信息序列化失败");
        }

        // 6. 计算订单金额
        BigDecimal productAmount = BigDecimal.ZERO; // 产品总金额
        for (TempFile tempFile : tempFiles) {
            if (tempFile.getUnitPrice() != null) {
                int pages = tempFile.getPageSize() - tempFile.getPageNum() + 1;
                int quantity = tempFile.getQuantity() != null ? tempFile.getQuantity() : 1;
                BigDecimal itemAmount = BigDecimal.valueOf(tempFile.getUnitPrice())
                        .multiply(BigDecimal.valueOf(pages))
                        .multiply(BigDecimal.valueOf(quantity));
                productAmount = productAmount.add(itemAmount);
            }
        }

        BigDecimal shippingAmount = BigDecimal.ZERO; // 暂时不收运费
        BigDecimal totalAmount = productAmount.add(shippingAmount);
        BigDecimal discountAmount = BigDecimal.ZERO; // 优惠金额（目前为0）
        BigDecimal orderPayAmount = totalAmount.subtract(discountAmount);

        order.setProductAmount(productAmount.doubleValue());
        order.setShippingAmount(shippingAmount.doubleValue());
        order.setTotalAmount(totalAmount.doubleValue());
        order.setPayAmount(orderPayAmount.doubleValue());

        // 保存订单
        orderDao.insert(order);

        // 7. 创建订单详情
        for (TempFile tempFile : tempFiles) {
            BigDecimal unitPrice = BigDecimal.valueOf(tempFile.getUnitPrice() != null ? tempFile.getUnitPrice() : 0.0);
            int pages = tempFile.getPageSize() - tempFile.getPageNum() + 1;
            int quantity = tempFile.getQuantity() != null ? tempFile.getQuantity() : 1;
            BigDecimal itemPayAmount = unitPrice.multiply(BigDecimal.valueOf(pages)).multiply(BigDecimal.valueOf(quantity));

            OrderDetail orderDetail = OrderDetail.builder()
                    .orderId(order.getId())
                    .fileName(tempFile.getFileName())
                    .filePath(tempFile.getFilePath())
                    .fileType(tempFile.getType()) // 设置文件类型
                    .quantity(tempFile.getQuantity() != null ? tempFile.getQuantity() : 1)
                    .pageNum(tempFile.getPageNum())
                    .pageSize(tempFile.getPageSize())
                    .specs(tempFile.getSpecs())
                    .skuId(tempFile.getSkuId())
                    .unitPrice(unitPrice.doubleValue()) // 单价
                    .payAmount(itemPayAmount.doubleValue()) // 支付金额 = 应付金额（单价 × 页数 × 数量）
                    .createTime(new Date())
                    .build();

            // 调试日志 - 确认文件类型是否正确设置
            System.out.println("创建订单详情 - 文件名: " + tempFile.getFileName() + ", 文件类型: " + tempFile.getType());

            orderDetailDao.insert(orderDetail);
        }

        // 8. 删除临时文件
        for (TempFile tempFile : tempFiles) {
            tempFileDao.deleteById(tempFile.getId());
        }

        // 9. 生成支付参数并返回订单信息
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("orderId", order.getId());
        result.put("totalAmount", order.getTotalAmount());
        result.put("payAmount", order.getPayAmount());
        
        // 生成微信小程序支付参数
        Map<String, Object> payParams = new HashMap<>();
        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = "order_" + order.getId() + "_" + timestamp;
        
        // 微信小程序支付所需参数
        payParams.put("timeStamp", String.valueOf(timestamp));
        payParams.put("nonceStr", nonceStr);
        payParams.put("package", "prepay_id=test_prepay_id_" + order.getId());
        payParams.put("signType", "MD5");
        payParams.put("paySign", generatePaySign(order.getId(), timestamp, nonceStr));
        
        result.put("payParams", payParams);
        result.put("needPay", order.getPayAmount() > 0); // 是否需要支付

        return result;
    }
    
    /**
     * 生成支付签名（模拟）
     */
    private String generatePaySign(Long orderId, long timestamp, String nonceStr) {
        // TODO: 这里应该根据微信支付的签名规则生成真实签名
        // 目前返回模拟签名
        return "mock_pay_sign_" + orderId + "_" + timestamp;
    }

    @Override
    public boolean cancelOrder(Long orderId) {
        // 查询订单
        Order order = orderDao.queryById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        // 检查订单状态，只有待付款的订单才能取消
        if (order.getStatus() != 0) {
            throw new ServiceException("订单状态不允许取消");
        }
        
        // 更新订单状态为已取消（状态4）
        order.setStatus(4);
        order.setUpdateTime(new Date());
        
        return orderDao.update(order) > 0;
    }

    @Override
    public Map<String, Object> payOrder(Long orderId) {
        // 查询订单信息
        Order order = orderDao.queryById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        // 验证订单状态
        if (order.getStatus() != 0) {
            throw new ServiceException("订单状态不支持支付");
        }
        
        // 生成支付参数
        Map<String, Object> payParams = new HashMap<>();
        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = "order_" + order.getId() + "_" + timestamp;
        
        payParams.put("timeStamp", String.valueOf(timestamp));
        payParams.put("nonceStr", nonceStr);
        payParams.put("package", "prepay_id=test_prepay_id_" + order.getId());
        payParams.put("signType", "MD5");
        payParams.put("paySign", generatePaySign(order.getId(), timestamp, nonceStr));
        
        return payParams;
    }

    @Override
    public boolean updateOrderAddress(Long orderId, Long addressId) {
        // 获取当前用户信息
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        
        // 查询订单信息
        Order order = orderDao.queryById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        // 验证订单归属
        if (!memberDetail.getOpenid().equals(order.getUserId())) {
            throw new ServiceException("无权限修改此订单");
        }
        
        // 验证订单状态，只有待支付状态可以修改地址
        if (order.getStatus() != 0) {
            throw new ServiceException("当前订单状态不允许修改地址");
        }
        
        // 查询新地址信息
        Address address = addressService.queryById(addressId);
        if (address == null) {
            throw new ServiceException("收货地址不存在");
        }
        
        // 验证地址归属 - 安全地比较userId
        Long memberUserId = memberDetail.getId();
        Integer addressUserId = address.getUserId();
        
        if (memberUserId == null || addressUserId == null) {
            throw new ServiceException("用户信息异常");
        }
        
        if (!memberUserId.equals(addressUserId.longValue())) {
            throw new ServiceException("无权限使用此地址");
        }
        
        // 将地址信息转换为JSON字符串
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String addressJson = objectMapper.writeValueAsString(address);
            order.setAddress(addressJson);
            
            // 更新订单
            int result = orderDao.update(order);
            return result > 0;
        } catch (JsonProcessingException e) {
            throw new ServiceException("地址信息序列化失败: " + e.getMessage());
        } catch (Exception e) {
            throw new ServiceException("更新订单地址失败: " + e.getMessage());
        }
    }
}
