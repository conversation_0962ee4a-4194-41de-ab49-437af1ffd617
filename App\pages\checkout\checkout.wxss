.container {
  padding-bottom: 120rpx;
  background: #f5f5f5;
}

/* 地址部分 */
.address-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.address-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.address-info {
  flex: 1;
}

.recipient-phone {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.recipient {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-left: 20rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.address-arrow {
  font-size: 32rpx;
  color: #999;
}

.no-address {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 商品部分 */
.product-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.product-list {
  margin-top: 20rpx;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-info {
  flex: 1;
  margin-right: 20rpx;
}

.product-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-specs {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.product-pages {
  font-size: 24rpx;
  color: #999;
}

.product-quantity {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.quantity-controls {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #ddd;
  font-size: 32rpx;
  color: #666;
  background: #fff;
}

.quantity-btn.minus {
  border-radius: 8rpx 0 0 8rpx;
}

.quantity-btn.plus {
  border-radius: 0 8rpx 8rpx 0;
}

.quantity-text {
  width: 80rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border: 1rpx solid #ddd;
  border-left: none;
  border-right: none;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.product-price {
  font-size: 30rpx;
  color: #ff4757;
  font-weight: bold;
}

/* 金额部分 */
.amount-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
}

.amount-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #f5f5f5;
  margin-top: 10rpx;
}

.total-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.total-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4757;
}

/* 底部提交区域 */
.bottom-fixed-empty {
  height: 120rpx;
}

.bottom-submit {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: #fff;
  border-top: 1rpx solid #f5f5f5;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  z-index: 999;
}

.submit-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.submit-text {
  font-size: 28rpx;
  color: #666;
}

.submit-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4757;
}

.submit-btn {
  background: #ff4757;
  color: #fff;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 地址选择弹窗 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
}

.address-list {
  flex: 1;
  padding: 20rpx 30rpx;
}

.address-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.selected-icon {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
}

.add-address-option {
  text-align: center;
  padding: 40rpx 0;
  color: #ff4757;
  font-size: 28rpx;
} 