package com.xh.print.dao;

import com.xh.print.domain.entity.PrintSpecsName;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 规格名表(PrintSpecsName)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-07 15:34:14
 */
public interface PrintSpecsNameDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PrintSpecsName queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param printSpecsName 查询条件
     * @param pageable       分页对象
     * @return 对象列表
     */
    List<PrintSpecsName> queryAllByLimit(PrintSpecsName printSpecsName, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param printSpecsName 查询条件
     * @return 总行数
     */
    long count(PrintSpecsName printSpecsName);

    /**
     * 新增数据
     *
     * @param printSpecsName 实例对象
     * @return 影响行数
     */
    int insert(PrintSpecsName printSpecsName);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PrintSpecsName> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PrintSpecsName> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PrintSpecsName> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PrintSpecsName> entities);

    /**
     * 修改数据
     *
     * @param printSpecsName 实例对象
     * @return 影响行数
     */
    int update(PrintSpecsName printSpecsName);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    int delete();
}

