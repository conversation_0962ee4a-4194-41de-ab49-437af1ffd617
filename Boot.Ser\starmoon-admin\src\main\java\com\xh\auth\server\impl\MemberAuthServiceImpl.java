package com.xh.auth.server.impl;

import com.xh.auth.dao.MemberDetailDao;
import com.xh.auth.domain.MemberAccessToken;
import com.xh.auth.domain.RealNameForm;
import com.xh.auth.server.MemberAuthService;
import com.xh.common.security.token.AccessToken;
import com.xh.common.security.token.TokenService;
import com.xh.common.sms.SmsService;
import com.xh.common.utils.HttpClientUtils;
import com.xh.common.utils.SecurityUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import com.xh.common.wechat.api.WeChatApi;
import com.xh.common.wechat.user.MemberDetail;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MemberAuthServiceImpl implements MemberAuthService {

    @Autowired
    private WeChatApi weChatApi;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private MemberDetailDao memberDetailDao;

    @Autowired
    private SmsService smsService;

    @Override
    public MemberAccessToken auth(String code) {

        Map<String, Object> logined = weChatApi.login(code);

        String openid = (String) logined.get("openid");
        String sessionKey = (String) logined.get("session_key");
        String unionid = (String) logined.get("unionid");

        MemberDetail memberDetail = memberDetailDao.queryByOpenid(openid);
        if(memberDetail == null){
            memberDetail = new MemberDetail();
            memberDetail.setOpenid(openid);
            memberDetail.setUnionid(unionid);
            memberDetailDao.insert(memberDetail);
        }

        SimpleGrantedAuthority authority = new SimpleGrantedAuthority("ROLE_"+memberDetail.getRole());

        Authentication authentication = new UsernamePasswordAuthenticationToken(memberDetail, null, List.of(authority));

        AccessToken accessToken = new AccessToken();
        tokenService.storeAccessToken(accessToken,authentication);

        return MemberAccessToken.builder().accessToken(accessToken).memberDetail(memberDetail).sessionKey(sessionKey).build();
    }

    @Override
    public boolean realName(RealNameForm form) {
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);

        if(!smsService.verify(form.getCodeKey(), form.getCode())){
            throw new ServiceException("验证码无效");
        }
        HttpServletRequest request = HttpClientUtils.getHttpServletRequest();

        MemberDetail info = memberDetailDao.queryByOpenid(memberDetail.getOpenid());
        if(info == null){
            String authorization = request.getHeader(AccessToken.AUTH);

            AccessToken accessByToken = tokenService.getAccessByToken(authorization);

            tokenService.destroy(accessByToken.getAccessToken(),null);

            throw new ServiceException(StatusCode.UNAUTHORIZED);
        }

        info.setUserName(form.getUserName());
        info.setPhone(form.getPhone());

        return memberDetailDao.update(info) > 0;
    }

    @Override
    public String sendSmsCode(String phone) {
        // 使用SmsService的smsCode方法发送验证码
        return smsService.smsCode(phone);
    }

    @Override
    public MemberAccessToken register(String code, String phone, String smsCode, String codeKey) {
        // 验证短信验证码
        if (!smsService.verify(codeKey, smsCode)) {
            throw new ServiceException("验证码无效或已过期");
        }

        // 获取微信用户信息
        Map<String, Object> logined = weChatApi.login(code);
        String openid = (String) logined.get("openid");
        String sessionKey = (String) logined.get("session_key");
        String unionid = (String) logined.get("unionid");

        // 检查用户是否已存在
        MemberDetail existingMember = memberDetailDao.queryByOpenid(openid);
        if (existingMember != null) {
            // 用户已存在，更新手机号
            existingMember.setPhone(phone);
            memberDetailDao.update(existingMember);
        } else {
            // 创建新用户
            existingMember = new MemberDetail();
            existingMember.setOpenid(openid);
            existingMember.setUnionid(unionid);
            existingMember.setPhone(phone);
            existingMember.setRole("USER"); // 设置默认角色
            memberDetailDao.insert(existingMember);
        }

        // 生成访问令牌
        SimpleGrantedAuthority authority = new SimpleGrantedAuthority("ROLE_" + existingMember.getRole());
        Authentication authentication = new UsernamePasswordAuthenticationToken(existingMember, null, List.of(authority));

        AccessToken accessToken = new AccessToken();
        tokenService.storeAccessToken(accessToken, authentication);

        return MemberAccessToken.builder()
                .accessToken(accessToken)
                .memberDetail(existingMember)
                .sessionKey(sessionKey)
                .build();
    }


}
