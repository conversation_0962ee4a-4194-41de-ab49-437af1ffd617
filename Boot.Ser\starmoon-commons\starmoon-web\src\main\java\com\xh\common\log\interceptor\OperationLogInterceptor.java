package com.xh.common.log.interceptor;

import com.alibaba.fastjson.JSON;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;

@Slf4j
@Aspect
@Component
public class OperationLogInterceptor {
    @Autowired
    private ApplicationContext applicationContext;

    @Around(value = "@annotation(operationLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        Object[] args = joinPoint.getArgs();

        HttpServletRequest request = HttpClientUtils.getHttpServletRequest();
        String ipAddress = HttpClientUtils.getIpAddress(request);
        String requestMethod = request.getMethod();
        String requestURI = request.getRequestURI();
        String title = operationLog.title();
        String[] tags = operationLog.tags();
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("method",requestMethod);
        requestMap.put("uri",requestURI);
        requestMap.put("args",JSON.toJSONString(args));

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        Object proceed = joinPoint.proceed();


        Map<String, String> log = new HashMap<>();
        log.put("title",title);
        log.put("tags",JSON.toJSONString(tags));
        log.put("request",JSON.toJSONString(requestMap));
        log.put("response",JSON.toJSONString(proceed));
        log.put("ipAddress",ipAddress);

        return proceed;
    }



}
