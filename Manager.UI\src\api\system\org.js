import request from '@/utils/request'

// 分页查询组织列表
export const listOrg = (data) =>
	request({
		url: '/manage-api/v1/org/page',
		method: 'get',
		params: data
	})

// 获取组织详情
export const getOrg = (id) =>
	request({
		url: '/manage-api/v1/org',
		method: 'get',
		params: { id: id }
	})

// 添加组织
export const addOrg = (data) =>
	request({
		url: '/manage-api/v1/org',
		method: 'post',
		data: data
	})

// 编辑组织
export const editOrg = (data) =>
	request({
		url: '/manage-api/v1/org',
		method: 'put',
		data: data
	})

// 删除组织
export const deleteOrg = (id) =>
	request({
		url: '/manage-api/v1/org',
		method: 'delete',
		params: { id: id }
	})

// 获取组织树形结构（用于下拉选择）
export const getOrgTree = () =>
	request({
		url: '/manage-api/v1/org/page',
		method: 'get',
		params: { pageNum: 1, pageSize: 1000 } // 获取大量数据用于构建树形结构
	})