package com.xh.print.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单明细(OrderDetail)实体类
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:51
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetail implements Serializable {
    private static final long serialVersionUID = -73020189816203424L;

    private Long id;
    /**
     * 订单编号
     */
    private Long orderId;

    private Long skuId;

    private String filePath;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 单价
     */
    private Double unitPrice;
    /**
     * 支付金额
     */
    private Double payAmount;
    /**
     * 购买数量
     */
    private Integer quantity;
    /**
     * 打印起始页
     */
    private Integer pageNum;
    /**
     * 打印结束页
     */
    private Integer pageSize;
    /**
     * 产品规格
     */
    private String specs;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 产品图片
     */
    private String image;

}

