package com.xh.common.monitor;

import lombok.Data;

@Data
public class MemoryInfo {
    public final static String UNIT_MB = "MB";
    public final static String UNIT_GB = "GB";
    //初始内存
    private double init;
    //已用内存
    private double used;
    //可用内存
    private double committed;
    //最大内存
    private double max;
    //使用率
    private double usage;
    //内存单位
    private String unit;

    public MemoryInfo(long init,long used,long committed,long max,String unit){
        this.unit = unit;
        double b = 1024 * 1024;
        if(UNIT_GB.equals(unit)){
            b *= 1024;
        }

        this.init = init/b;
        this.used = used/b;
        this.committed = committed/b;
        if(max != -1){
            this.max = max/b;
        }
        this.usage = this.used/this.committed*100;
    }
}
