package com.xh.system.controller.app;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.common.utils.SecurityUtils;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.common.wechat.user.MemberDetail;
import com.xh.system.domain.dto.AddressCreateDTO;
import com.xh.system.domain.dto.AddressEditDTO;
import com.xh.system.domain.entity.Address;
import com.xh.system.service.AddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户端")
@RestController
@RequestMapping("users-api/v1/address")
public class MemberAddressController {

    @Autowired
    private AddressService addressService;

    @Parameters({
            @Parameter(name = "pageNum", description = "页码"),
            @Parameter(name = "pageSize", description = "每页数量"),
            @Parameter(name = "userId", description = "用户ID", required = true)
    })
    @Operation(summary = "通过分页查询地址")
    @GetMapping("page")
    public ResponseEntity<PageInfo<Address>> queryByPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize
    ) {
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        return ResponseEntity.success(addressService.queryByPage(pageNum, pageSize, memberDetail.getId()));
    }

    @Parameter(name = "id", description = "地址ID", required = true)
    @Operation(summary = "通过ID查询地址")
    @GetMapping
    public ResponseEntity<Address> queryById(@RequestParam("id") Long id) {
        return ResponseEntity.success(addressService.queryById(id));
    }

    @Operation(summary = "新增地址")
    @PostMapping
    public ResponseEntity<Long> insert(@Valid @RequestBody AddressCreateDTO dto) {
        return ResponseEntity.success(addressService.insert(dto));
    }

    @Operation(summary = "修改地址")
    @PutMapping
    public ResponseEntity<Boolean> update(@Valid @RequestBody AddressEditDTO dto) {
        return ResponseEntity.success(addressService.update(dto));
    }

    @Operation(summary = "删除地址")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(@RequestParam("id") Long id) {
        return ResponseEntity.success(addressService.deleteById(id));
    }
} 