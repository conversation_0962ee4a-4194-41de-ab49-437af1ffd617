var api = require('../../utils/api.js')
var sender = require('../../utils/sender.js')
const app = getApp();
var pageNum = 1;
var pageSize = 10;

Page({
  data: {
    CDN_IMAGE: api.CDN_IMAGE,
    banners: [],
    printList:[]
  },

  onLoad: function(options) {
   
  },
  
  onShow: function() {
    var loginStatus = wx.getStorageSync("LoginStatus")
    var that = this;
    if (loginStatus) {
      this.getTempFileList()
      this.getBannerList()
    } else {
      app.userInfoReadyCallback = res => {
        this.getTempFileList()
        this.getBannerList()
      }

      app.loginFailCallback = res => {
        // 登录失败，引导用户注册
        wx.showModal({
          title: '需要注册',
          content: '您还未注册，请先完成手机号注册',
          showCancel: true,
          cancelText: '稍后',
          confirmText: '去注册',
          success: function(modalRes) {
            if (modalRes.confirm) {
              wx.navigateTo({
                url: '/pages/register/register'
              });
            }
          }
        });
      }
    }

  },
  getTempFileList(){
    var that = this
    sender.requestUrl({
      url: api.api_temp_file_list,
      method: 'GET'
    }, function(data) {
      that.setData({
        printList: data
      });
    });
  },
  getBannerList(){
    var that = this
    sender.requestUrl({
      url: api.api_imagetext_list,
      method: 'GET',
      params: {
        pageNum: pageNum,
        pageSize: pageSize,
        type: 'banner'
      }
    }, function(data) {
      if (data && data.list) {
        that.setData({
          banners: data.list
        });
      }
    });
  },
  
  // 跳转到打印教程
  toImagetext(res) {
    console.log(res.currentTarget.dataset.type)
    sender.requestUrl({
      url: api.api_imagetext_list,
      method: 'GET',
      params: {
        type: res.currentTarget.dataset.type,
        pageNum: 1,
        pageSize: 1
      }
    }, function(data) {
      if (data && data.list && data.list.length > 0) {
        wx.navigateTo({
          url: '/pages/imagetext/imagetext?id=' + data.list[0].id
        });
      } else {
        wx.showToast({
          title: '暂无教程',
          icon: 'none'
        });
      }
    });
  },
  
  // 跳转到上传打印页面
  toUploadPrint() {
    if(this.data.printList.length == 0){
      wx.navigateTo({
        url: '/pages/print/upload/upload'
      })
    }else{
      wx.navigateTo({
        url: '/pages/print/list/list'
      });
    }
    
  }
})