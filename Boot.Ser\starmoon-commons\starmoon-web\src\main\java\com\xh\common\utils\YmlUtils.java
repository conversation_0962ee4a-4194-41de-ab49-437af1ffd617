package com.xh.common.utils;

import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

public class YmlUtils {


    public static byte[] getYamlBytes(String path) throws IOException {
        InputStream stream = YmlUtils.class.getResourceAsStream("/"+path);
        return FileUtils.fileToStream(stream);
    }

    public static Properties getProjectPropertiesByYml(String path){
        Properties properties = YmlUtils.yamlToProperties(YmlUtils.class.getResourceAsStream("/"+path));
        if(properties == null){
            return new Properties();
        }
        String active = properties.getProperty("spring.profiles.active");
        if(active != null){
            Properties activeProperties = YmlUtils.yamlToProperties(YmlUtils.class.getResourceAsStream("/application-" + active + ".yml"));
            if(activeProperties != null){
                properties.putAll(activeProperties);
            }
        }
        return properties;
    }

    public static Properties yamlToProperties(InputStream inputStream){
        if(inputStream == null){
            return null;
        }
        Yaml yaml = new Yaml();
        Map map = yaml.loadAs(inputStream, Map.class);
        List<YmlNode> nodes = getYmlNode(map);
        Properties properties = createProperties(nodes);
        return properties;
    }

    private static Properties createProperties(List<YmlNode> nodes) {
        Properties properties = new Properties();
        nodes.forEach(item ->{
            if(item.getChild() != null){
                setChild(item);
            }
        });
        List<YmlNode> propertiesList = getPropertiesList(nodes);
        propertiesList.forEach(item ->{
            properties.put(item.getKey(),item.getValue());
        });
        return properties;
    }

    private static List<YmlNode> getPropertiesList(List<YmlNode> nodes) {
        List<YmlNode> list = new ArrayList<>();
        nodes.forEach(item ->{
            if(item.getChild() != null){
                List<YmlNode> properties = getPropertiesList(item.getChild());
                list.addAll(properties);
            }else {
                list.add(item);
            }
        });
        return list;
    }

    private static void setChild(YmlNode parentNode) {
        parentNode.getChild().forEach(item ->{
            StringBuffer sb = new StringBuffer();
            String parentKey = parentNode.getKey();
            String key = item.getKey();
            sb.append(parentKey).append(".").append(key);
            item.setKey(sb.toString());

            if(item.getChild() != null){
                setChild(item);
            }
        });
    }

    public static List<YmlNode> getYmlNode(Map map){
       List<YmlNode> ymlNodeList = new ArrayList<>();
        map.forEach((k,v)->{
            YmlNode ymlNode = new YmlNode();
            ymlNode.setKey(String.valueOf(k));
            if(v instanceof Map){
                ymlNode.setChild(getYmlNode((Map) v));
            }else {
                ymlNode.setValue(String.valueOf(v));
            }
            ymlNodeList.add(ymlNode);
        });
       return ymlNodeList;
    }

}
 class YmlNode{

    private String key;
    private String value;
    private List<YmlNode> child;

     public String getKey() {
         return key;
     }

     public void setKey(String key) {
         this.key = key;
     }

     public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<YmlNode> getChild() {
        return child;
    }

    public void setChild(List<YmlNode> child) {
        this.child = child;
    }
}
