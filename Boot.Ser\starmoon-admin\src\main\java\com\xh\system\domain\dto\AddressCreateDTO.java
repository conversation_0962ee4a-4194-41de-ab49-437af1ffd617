package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 新增地址参数DTO
 */
@Data
public class AddressCreateDTO implements Serializable {

    @Schema(description = "收件人")
    @NotBlank(message = "收件人不能为空")
    private String recipient;

    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @Schema(description = "省份")
    @NotBlank(message = "省份不能为空")
    private String province;

    @Schema(description = "城市")
    @NotBlank(message = "城市不能为空")
    private String city;

    @Schema(description = "区县")
    @NotBlank(message = "区县不能为空")
    private String county;

    @Schema(description = "乡镇")
    private String town;

    @Schema(description = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String detail;

    @Schema(description = "默认地址")
    private Boolean isDefault = false;

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;
} 