package com.xh.print.service.impl;


import com.xh.common.utils.SecurityUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.wechat.user.MemberDetail;
import com.xh.file.service.FileService;
import com.xh.print.dao.TempFileDao;
import com.xh.print.domain.dto.TempFileDTO;
import com.xh.print.domain.entity.PrintSku;
import com.xh.print.domain.entity.TempFile;
import com.xh.print.domain.form.TempFileEditForm;
import com.xh.print.service.PrintSpecsService;
import com.xh.print.service.TempFileService;
import com.xh.print.utils.DocumentUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

/**
 * (TempFile)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21 15:47:08
 */
@Service
public class TempFileServiceImpl implements TempFileService {

    @Autowired
    private TempFileDao tempFileDao;

    @Autowired
    private FileService fileService;

    @Autowired
    private PrintSpecsService printSpecsService;

    @Override
    public TempFile queryById(Long id) {
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        return tempFileDao.queryByIdForUsers(id,memberDetail.getOpenid());
    }

    @Override
    public TempFileDTO queryDetailById(Long id) {
        TempFile tempFile = queryById(id);
        if(tempFile == null){
            return null;
        }

        TempFileDTO tempFileDTO = new TempFileDTO();
        BeanUtils.copyProperties(tempFile,tempFileDTO);
        File fileByPath = fileService.getFileByPath(tempFile.getFilePath());

        FileInputStream is = null;

        try {
            is = new FileInputStream(fileByPath);
            if("doc".equals(tempFile.getType()) || "docx".equals(tempFile.getType())){
                int pageCount = DocumentUtils.getDocPageCount(is);
                tempFileDTO.setTotalPage(pageCount);
            }else if("pdf".equals(tempFile.getType())){
                int pageCount = DocumentUtils.getPdfPageCountFromStream(is);
                tempFileDTO.setTotalPage(pageCount);
            }else if("ppt".equals(tempFile.getType())){
                int pptPageCount = DocumentUtils.getPptPageCount(is);
                tempFileDTO.setTotalPage(pptPageCount);
            }else if("pptx".equals(tempFile.getType())){
                int pptxPageCount = DocumentUtils.getPptxPageCount(is);
                tempFileDTO.setTotalPage(pptxPageCount);
            }else if(
                    "png".equals(tempFile.getType()) ||
                    "jpg".equals(tempFile.getType()) ||
                    "jpeg".equals(tempFile.getType()) ||
                    "gif".equals(tempFile.getType()) ||
                    "bmp".equals(tempFile.getType()) ||
                    "webp".equals(tempFile.getType())
            ){
                tempFileDTO.setTotalPage(1);
            }else {
                throw new ServiceException("不支持的文件类型");
            }
            is.close();
        }catch (ServiceException e){
            throw new ServiceException(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                if(is == null){
                    throw new ServiceException("文件不存在,请重新上传");
                }else {
                    is.close();
                }

            }catch (IOException e){

            }
        }

        if(tempFileDTO.getPageNum() == null){
            tempFileDTO.setPageNum(1);
        }
        if(tempFileDTO.getPageSize() == null){
            tempFileDTO.setPageSize(tempFileDTO.getTotalPage());
        }

        return tempFileDTO;
    }


    @Override
    public List<TempFile> queryByList() {
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        List<TempFile> tempFiles = tempFileDao.queryByList(memberDetail.getOpenid());
        
        // 检查每个文件是否存在
        for (TempFile tempFile : tempFiles) {
            boolean fileExists = checkFileExists(tempFile.getFilePath());
            tempFile.setFileExists(fileExists);
        }
        
        return tempFiles;
    }

    /**
     * 检查文件是否存在
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    private boolean checkFileExists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }
        try {
            File file = fileService.getFileByPath(filePath);
            return file != null && file.exists();
        } catch (Exception e) {
            // 如果检查过程中出现异常，认为文件不存在
            return false;
        }
    }

    @Override
    public Long insert(MultipartFile file,String fileName) {
        String originalFilename = file.getOriginalFilename();
        String contentType = file.getContentType();
        String filePath = fileService.upload(file);
        String[] split = contentType.split("/");
        TempFile tempFile = new TempFile();
        if (fileName != null) {
            tempFile.setFileName(fileName);
        }else {
            tempFile.setFileName(originalFilename);
        }

        tempFile.setFilePath(filePath);
        tempFile.setType(split[1]);
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        tempFile.setOpenid(memberDetail.getOpenid());

        return insert(tempFile);
    }



    @Override
    public Long insert(TempFile tempFile) {
        tempFileDao.insert(tempFile);
        return tempFile.getId();
    }

    @Override
    public boolean update(TempFileEditForm form) {
        TempFile tempFile = new TempFile();
        BeanUtils.copyProperties(form,tempFile);
        PrintSku printSku = printSpecsService.queryById(tempFile.getSkuId());
        tempFile.setSpecs(printSku.getSpecs());
        return update(tempFile);
    }

    @Override
    public boolean update(TempFile tempFile) {
        return tempFileDao.update(tempFile) > 0;
    }


    @Override
    public boolean deleteById(Long id) {
        MemberDetail memberDetail = SecurityUtils.currentPrincipal(MemberDetail.class);
        return tempFileDao.deleteByIdForUsers(id,memberDetail.getOpenid()) > 0;
    }
}
