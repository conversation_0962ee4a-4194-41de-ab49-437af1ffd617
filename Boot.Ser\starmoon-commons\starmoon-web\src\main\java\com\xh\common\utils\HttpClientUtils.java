package com.xh.common.utils;

import com.alibaba.fastjson.JSON;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

@Slf4j
public class HttpClientUtils {


    public static String post(String url,Map<String, Object> params,  Map<String,Object> body,Map<String,String> headers) {
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            // 添加请求参数
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                uriBuilder.addParameter(entry.getKey(), String.valueOf(entry.getValue()));
            }

            URI build = uriBuilder.build();

            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(build);
            //设置请求和传输超时时间
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
            httpPost.setConfig(requestConfig);
            httpPost.setHeader("Content-Type", "application/json");
            if(headers != null){
                headers.forEach((key,value)->{
                    httpPost.setHeader(key,value);
                });
            }
            if(body!=null){
                StringEntity s = new StringEntity(JSON.toJSONString(body), StandardCharsets.UTF_8);
                s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,"application/json"));
                //设置参数到请求对象中
                httpPost.setEntity(s);
            }

            HttpResponse response = httpClient.execute(httpPost);
            String httpEntityContent = getHttpEntityContent(response);
            httpPost.abort();
            return httpEntityContent;
        }catch (Exception e){
            log.error("httpclient请求异常被捕获",e);
            return null;
        }
    }

    /**
     * 封装HTTP POST方法
     *
     * @param （如JSON串）
     * @return
     */
    public static String post(String url, String body,Map<String,String> headers) {
        try {
            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            //设置请求和传输超时时间
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
            httpPost.setConfig(requestConfig);
            httpPost.setHeader("Content-Type", "application/json");
            if(headers != null){
                headers.forEach((key,value)->{
                    httpPost.setHeader(key,value);
                });
            }
            if(body!=null){
                StringEntity s = new StringEntity(body, StandardCharsets.UTF_8);
                s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,"application/json"));
                //设置参数到请求对象中
                httpPost.setEntity(s);
            }

            HttpResponse response = httpClient.execute(httpPost);
            String httpEntityContent = getHttpEntityContent(response);
            httpPost.abort();
            return httpEntityContent;
        }catch (Exception e){
            log.error("httpclient请求异常被捕获",e);
            return null;
        }
    }



    public static String put(String url,String body, Map<String, String> headers) {
        try {
            HttpClient httpClient = HttpClients.createDefault();
            HttpPut httpPut = new HttpPut(url);
            //设置请求和传输超时时间
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
            httpPut.setConfig(requestConfig);
            httpPut.setHeader(HTTP.CONTENT_TYPE,"application/json");
            if(headers != null){
                headers.forEach((key,value) ->{
                    httpPut.setHeader(key,value);
                });
            }
            if(body!=null){
                StringEntity s = new StringEntity(body, StandardCharsets.UTF_8);
                s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,"application/json"));
                //设置参数到请求对象中
                httpPut.setEntity(s);
            }
            HttpResponse response = httpClient.execute(httpPut);
            String httpEntityContent = getHttpEntityContent(response);
            httpPut.abort();
            return httpEntityContent;
        }catch (Exception e){
            log.error("httpclient请求异常被捕获",e);
            return null;
        }
    }

    public static String delete(String url,Map<String, Object> params, Map<String, String> headers) {
        try {
            HttpClient httpClient = HttpClients.createDefault();
            HttpDelete httpDelete = new HttpDelete(url);
            //设置请求和传输超时时间
            //设置请求和传输超时时间
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
            httpDelete.setConfig(requestConfig);
            if(headers != null){
                headers.forEach((key,value) ->{
                    httpDelete.addHeader(key,value);
                });
            }
            if(params != null){
                StringBuffer sb = new StringBuffer();
                params.forEach((key,value) ->{
                    sb.append("&"+key+"="+value);
                });
                if(sb.length()>0){
                    String substring = sb.substring(1);
                    url+="?"+substring;
                }
            }
            httpDelete.setURI(URI.create(url));
            HttpResponse response = httpClient.execute(httpDelete);
            String httpEntityContent = getHttpEntityContent(response);
            httpDelete.abort();
            return httpEntityContent;
        }catch (Exception e){
            log.error("httpclient请求异常被捕获",e);
            return null;
        }
    }

    /**
     * 封装HTTP GET方法
     *
     * @param
     * @return
     * @throws IOException
     */
    public static String get(String url,Map<String, Object> params, Map<String, String> headers){
        try {
            HttpClient httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet();
            //设置请求和传输超时时间
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
            httpGet.setConfig(requestConfig);
            if(headers != null){
                headers.forEach((key,value) ->{
                    httpGet.addHeader(key,value);
                });
            }
            if(params != null){
                StringBuffer sb = new StringBuffer();
                params.forEach((key,value) ->{
                    sb.append("&"+key+"="+value);
                });
                if(sb.length()>0){
                    String substring = sb.substring(1);
                    url+="?"+substring;
                }
            }
            httpGet.setURI(URI.create(url));
            HttpResponse response = httpClient.execute(httpGet);
            String httpEntityContent = getHttpEntityContent(response);
            httpGet.abort();
            return httpEntityContent;
        }catch (Exception e){
            log.error("httpclient请求异常被捕获",e);
            return null;
        }

    }


    /**
     * 获得响应HTTP实体内容
     *
     * @param response
     * @return
     * @throws IOException
     * @throws UnsupportedEncodingException
     */
    private static String getHttpEntityContent(HttpResponse response) throws IOException, UnsupportedEncodingException {
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            InputStream is = entity.getContent();
            BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            String line = br.readLine();
            StringBuilder sb = new StringBuilder();
            while (line != null) {
                sb.append(line + "\n");
                line = br.readLine();
            }
            return sb.toString();
        }
        return null;
    }

    public static HttpClient wrapClient() {
        HttpClients.createDefault();
        try {
            SSLContext ctx = SSLContext.getInstance("TLS");
            X509TrustManager trustManager = new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                @Override
                public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                }
                @Override
                public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                }
            };
            ctx.init(null, new TrustManager[]{trustManager}, null);
            SSLConnectionSocketFactory ssf = new SSLConnectionSocketFactory(ctx, NoopHostnameVerifier.INSTANCE);
            return HttpClients.custom()
                    .setSSLSocketFactory(ssf)
                    .build();
        } catch (Exception e) {
            return HttpClients.createDefault();
        }
    }

    public static HttpServletRequest getHttpServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    public static HttpServletResponse getHttpServletResponse() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
    }
    //获取HttpServletRequest

    public static String getIpAddress(HttpServletRequest request){
        String ipAddress = request.getHeader("x-forwarded-for");

        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }

        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }

        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)){
            ipAddress = request.getRemoteAddr();
        }

        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if(ipAddress!=null && ipAddress.length()>15){
            if(ipAddress.indexOf(",")>0){
                ipAddress = ipAddress.substring(0,ipAddress.indexOf(","));
            }
        }

        return ipAddress;
    }
}