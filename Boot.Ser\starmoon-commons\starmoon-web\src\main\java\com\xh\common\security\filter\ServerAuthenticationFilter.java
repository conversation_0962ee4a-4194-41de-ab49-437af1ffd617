package com.xh.common.security.filter;

import com.xh.common.security.token.AccessToken;
import com.xh.common.security.token.TokenService;
import com.xh.common.utils.HttpClientUtils;
import com.xh.common.wechat.user.MemberDetail;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
@Slf4j
public class ServerAuthenticationFilter extends OncePerRequestFilter {
    private TokenService tokenService;

    public ServerAuthenticationFilter(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String ipAddress = HttpClientUtils.getIpAddress(request);
        String requestURI = request.getRequestURI();
        String[] urls = requestURI.split("/");
        log.info("----------ip：{}",ipAddress);
        log.info("----------urls：{}", Arrays.toString(urls));
        String authorization = request.getHeader(AccessToken.AUTH);
        if(authorization != null && authorization.trim().length()>0){
            if(SecurityContextHolder.getContext().getAuthentication() != null){
                SecurityContextHolder.clearContext();
            }
            Authentication authentication = tokenService.getAuthenticationByToken(authorization);
            if(authentication != null){
                if(urls.length > 1 && "users-api".equals(urls[1])){
                    if(authentication.getPrincipal() instanceof MemberDetail){
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                    }
                }else {
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            }
        }
        filterChain.doFilter(request,response);
    }
}
