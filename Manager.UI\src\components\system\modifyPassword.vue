<template>
	<el-dialog width="25%" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="userForm" :model="userModel" label-width="80px">
			<el-row>
				<el-col>
					<el-form-item label="旧密码" prop="oldPassword">
						<el-input  type="password" v-model="userModel.oldPassword" placeholder="旧密码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="新密码" prop="newPassword">
						<el-input type="password" v-model="userModel.newPassword" placeholder="新密码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="确认密码" prop="reNewPassword">
						<el-input  type="password" v-model="userModel.reNewPassword" placeholder="确认新密码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit('userForm')">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { updateCurrentUserPassword } from "@/api/system/user";
import mitt from "@/utils/mitt";
export default {
	data() {
		var checkPassword = (rule, value, callback) => { 
			var reg = /^[\da-zA-Z~!@#$%^&*]{6,18}$/
			if(value == undefined || value == null || value == ''){
				return callback(new Error('请输入密码'));
			}
			if(!reg.test(value)){
				console.log("aa")
				return callback(new Error('密码长度为6-18位'))
			}
			callback()
		}
		var checkRePassword = (rule, value, callback) => {
			if(value == undefined || value == null || value == ''){
				return callback(new Error('请确认密码'));
			}
			if(value != this.userModel.newPassword){
				return callback(new Error('和新密码不符'))
			}
			callback()
		}
		return {
			loading: false,
			userModel: {},
			dialog:{
			  show: false
      },
			rules: {
				oldPassword: [{
					required: true,
					trigger: 'blur',
					validator: checkPassword
				}],
				newPassword: [{
					required: true,
					trigger: 'blur',
					validator: checkPassword
				}],
				reNewPassword: [{
					required: true,
					trigger: 'blur',
					validator: checkRePassword
				}]
			}
		}
	},
	methods: {
		onSubmit(formName){
			this.$refs[formName].validate((valid) => {
			  if (valid) {
			    updateCurrentUserPassword(this.userModel)
			    .then(res =>{
			    	this.$message.success("操作成功")
					if(this.userModel.uuid != 1){
						//跳转到登录页面
						sessionStorage.clear()
						setTimeout(function(){
							window.location.reload()
						},500)
					}else{
						this.dialog.show = false
					}
			    })
			    .catch(err =>{
			    	this.$message.error(res.data.message)
			    })
			  } else {
			    return false
			  }
			})
		}
	},
	mounted(){
		mitt.on('openUserPwdModify', () => {
			this.userModel = {
				uuid:0
			}
			this.dialog.show = true
			this.dialog.title = "修改密码"
		})
		mitt.on('openUserPwdModifyByUserName', (userName) => {
			this.userModel = {
				uuid:1,
				userName:userName
			}
			this.dialog.show = true
			this.dialog.title = "修改密码"
		})
	}
}
</script> 