<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.AddressDao">

    <resultMap id="AddressMap" type="com.xh.system.domain.entity.Address">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="recipient" column="recipient" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="county" column="county" jdbcType="VARCHAR"/>
        <result property="town" column="town" jdbcType="VARCHAR"/>
        <result property="detail" column="detail" jdbcType="VARCHAR"/>
        <result property="isDefault" column="is_default" jdbcType="BIT"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="detail">
        id, recipient, phone, province, city, county, town, detail, `is_default`, user_id, create_time, update_time
    </sql>

    <select id="queryById" resultMap="AddressMap">
        select <include refid="detail" />
        from t_address
        where id = #{id}
    </select>

    <select id="queryByList" resultMap="AddressMap">
        select <include refid="detail" />
        from t_address
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
        </where>
        order by create_time desc
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recipient != null">
                recipient,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="town != null">
                town,
            </if>
            <if test="detail != null">
                detail,
            </if>
            <if test="isDefault != null">
                `is_default`,
            </if>
            <if test="userId != null">
                user_id,
            </if>
                create_time
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recipient != null">
                #{recipient},
            </if>
            <if test="phone != null">
                #{phone},
            </if>
            <if test="province != null">
                #{province},
            </if>
            <if test="city != null">
                #{city},
            </if>
            <if test="county != null">
                #{county},
            </if>
            <if test="town != null">
                #{town},
            </if>
            <if test="detail != null">
                #{detail},
            </if>
            <if test="isDefault != null">
                #{isDefault},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
                now()
        </trim>
    </insert>

    <update id="update">
        update t_address
        <set>
            <if test="recipient != null">
                recipient = #{recipient},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="county != null">
                county = #{county},
            </if>
            <if test="town != null">
                town = #{town},
            </if>
            <if test="detail != null">
                detail = #{detail},
            </if>
            <if test="isDefault != null">
                `is_default` = #{isDefault},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from t_address where id = #{id}
    </delete>

</mapper> 