package com.xh.common.wechat.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.common.utils.HttpClientUtils;
import com.xh.common.web.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class WeChatApi {

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.appsecret}")
    private String secret;

    public Map<String,Object> login(String code) {

        for(int i=3; i>=0; i--){
            String resStr = HttpClientUtils.get("https://api.weixin.qq.com/sns/jscode2session?" + String.format("appid=%s&secret=%s&js_code=%s", appid, secret, code), null, null);
            JSONObject jsonObject = JSONObject.parseObject(resStr);

            if(jsonObject.containsKey("openid")){
                log.info("{}：微信登录成功",code);
                return new HashMap<>(jsonObject);
            }else {
                log.error("{}：微信登录失败,进入重试：{}",code,jsonObject.getString("errmsg"));
            }

            if(i == 0){
                if(!jsonObject.containsKey("openid")){
                    throw new ServiceException("微信登录失败："+jsonObject.getString("errmsg"));
                }
            }
        }

        throw new ServiceException("微信登录失败：未知错误");
    }

    public  Map<String,Object> getAccessToken() {
        String resStr = HttpClientUtils.get("https://api.weixin.qq.com/cgi-bin/token?" + String.format("grant_type=client_credential&appid=%s&secret=%s", appid, secret), null, null);
        JSONObject jsonObject = JSONObject.parseObject(resStr);
        if(!jsonObject.containsKey("access_token")){
            throw new ServiceException("获取微信access_token失败："+jsonObject.getString("errmsg"));
        }
        return new HashMap<>(jsonObject);
    }

    public Map<String,Object> getPhone(String accessToken, String code) {
        Map<String,String> body = new HashMap<>();
        body.put("code",code);
        String resStr = HttpClientUtils.post("https://api.weixin.qq.com/wxa/business/getuserphonenumber?" + String.format("access_token=%s", accessToken), JSON.toJSONString(body), null);
        JSONObject jsonObject = JSONObject.parseObject(resStr);
        if(!jsonObject.containsKey("phone_info")){
            throw new ServiceException("获取微信手机号失败："+jsonObject.getString("errmsg"));
        }
        return new HashMap<>(JSONObject.parseObject(jsonObject.getString("phone_info")));
    }


    public String getUserInfo(String accessToken, String openid) {
        return HttpClientUtils.get("https://api.weixin.qq.com/cgi-bin/user/info?" + String.format("access_token=%s&openid=%s", accessToken,openid),null,null);
    }

    public String getUserList(String accessToken) {
        return HttpClientUtils.get("https://api.weixin.qq.com/cgi-bin/user/get?" + String.format("access_token=%s", accessToken), null,null);
    }

    public String getTags(String accessToken) {
        return HttpClientUtils.get("https://api.weixin.qq.com/cgi-bin/tags/get?" + String.format("access_token=%s", accessToken),null,null);
    }

    public void sendTemplateMessage(String accessToken, String message) {
        HttpClientUtils.post("https://api.weixin.qq.com/cgi-bin/message/template/send?" + String.format("access_token=%s", accessToken),message,null);
    }
}
