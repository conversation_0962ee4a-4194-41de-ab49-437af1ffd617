<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.ImagetextReadstatusDao">

    <resultMap type="com.xh.system.domain.entity.ImagetextReadstatus" id="ImagetextReadstatusMap">
        <result property="imagetextId" column="imagetext_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="readTime" column="read_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ImagetextReadstatusMap">
        select
imagetext_id, user_id, status, read_time
        from sys_imagetext_readstatus
        where imagetext_id = #{imagetextId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ImagetextReadstatusMap">
        select
imagetext_id, user_id, status, read_time
        from sys_imagetext_readstatus
        <where>
            <if test="imagetextId != null">
                and imagetext_id = #{imagetextId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="readTime != null">
                and read_time = #{readTime}
            </if>
        </where>

    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from sys_imagetext_readstatus
        <where>
            <if test="imagetextId != null">
                and imagetext_id = #{imagetextId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="readTime != null">
                and read_time = #{readTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="imagetextId" useGeneratedKeys="true">
        insert into sys_imagetext_readstatus(status, read_time)
        values (#{status}, #{readTime})
    </insert>

    <insert id="insertBatch" keyProperty="imagetextId" useGeneratedKeys="true">
        insert into sys_imagetext_readstatus(status, read_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.status}, #{entity.readTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="imagetextId" useGeneratedKeys="true">
        insert into sys_imagetext_readstatus(status, read_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.status}, #{entity.readTime})
        </foreach>
        on duplicate key update
status = values(status),
read_time = values(read_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_imagetext_readstatus
        <set>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="readTime != null">
                read_time = #{readTime},
            </if>
        </set>
        where imagetext_id = #{imagetextId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_imagetext_readstatus where imagetext_id = #{imagetextId}
    </delete>

</mapper>

