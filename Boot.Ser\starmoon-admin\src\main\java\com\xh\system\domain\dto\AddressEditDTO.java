package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 编辑地址参数DTO
 */
@Data
public class AddressEditDTO implements Serializable {

    @Schema(description = "主键ID")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "收件人")
    private String recipient;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区县")
    private String county;

    @Schema(description = "乡镇")
    private String town;

    @Schema(description = "详细地址")
    private String detail;

    @Schema(description = "默认地址")
    private Boolean isDefault;

    @Schema(description = "用户ID")
    private Integer userId;
} 