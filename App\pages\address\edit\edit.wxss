.container {
  padding: 0 0 30rpx 0;
  background-color: #f7f7f7;
  min-height: 100vh;
}

.form-card {
  background-color: #fff;
  padding: 0 30rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.form-item {
  padding: 30rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 180rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  flex: 1;
  height: 60rpx;
  font-size: 30rpx;
  color: #333;
}

.form-textarea {
  flex: 1;
  height: 100rpx;
  font-size: 30rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.picker-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  color: #333;
}

.placeholder {
  color: #999;
}

.arrow {
  font-size: 32rpx;
  color: #ccc;
  transition: transform 0.3s;
}

.switch-item {
  justify-content: space-between;
}

.btn-container {
  padding: 60rpx 30rpx;
}

.save-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(to right, #ff9933, #ff6633);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  box-shadow: 0 6rpx 10rpx rgba(255, 102, 51, 0.2);
  transition: all 0.3s;
}

.save-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
} 