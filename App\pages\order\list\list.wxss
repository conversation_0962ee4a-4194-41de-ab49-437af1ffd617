.container {
  padding: 0;
  background-color: #f7f7f7;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 订单筛选Tab */
.order-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab-item.active {
  color: #ff6633;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 60rpx;
  height: 6rpx;
  background-color: #ff6633;
  border-radius: 3rpx;
  transform: translateX(-50%);
  transition: all 0.3s;
}

/* 滚动区域 */
.order-scroll {
  flex: 1;
  height: calc(100vh - 180rpx);
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

/* 订单项目 */
.order-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx 28rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.order-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-id {
  font-size: 26rpx;
  color: #666;
  max-width: 420rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-status {
  font-size: 26rpx;
  color: #ff6633;
  font-weight: 500;
  padding: 8rpx 20rpx;
  background-color: rgba(255, 102, 51, 0.1);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 102, 51, 0.2);
}

/* 产品列表 */
.product-list {
  margin-bottom: 24rpx;
}

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.product-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.file-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.file-icon {
  width: 50rpx;
  height: 50rpx;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.product-price {
  font-size: 28rpx;
  color: #ff6633;
  font-weight: 600;
  margin-left: 20rpx;
  flex-shrink: 0;
}

/* 订单底部 */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-total {
  display: flex;
  align-items: center;
}

.total-price {
  font-size: 34rpx;
  color: #ff6633;
  font-weight: 600;
}

/* 空订单提示 */
.empty-order {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载中 */
.loading {
  text-align: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #999;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 24rpx;
  color: #ccc;
} 