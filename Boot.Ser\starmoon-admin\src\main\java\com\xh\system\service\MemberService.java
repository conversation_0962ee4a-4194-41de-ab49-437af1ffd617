package com.xh.system.service;

import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.MemberEditDTO;
import com.xh.system.domain.entity.Member;

/**
 * 客户端用户表(Member)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-21 20:29:36
 */
public interface MemberService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Member queryById(Long id);

    /**
     * 通过分页查询
     * @return 查询结果
     */
    PageInfo<Member> queryByPage();

    /**
     * 修改数据
     *
     * @return 实例对象
     */
    boolean update(MemberEditDTO dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

}
