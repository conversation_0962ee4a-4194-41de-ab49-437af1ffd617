package com.xh.system.dao;

import com.xh.system.domain.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息表(User)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 09:50:27
 */
public interface UserDao {

    User queryById(Long id);

    List<User> queryByList(@Param("keyword") String keyword, @Param("userName") String userName, @Param("status") Integer status, @Param("orgId") Long orgId,@Param("phone") String phone);

    int insert(User user);

    int update(User user);

    int deleteById(Long id);
}

