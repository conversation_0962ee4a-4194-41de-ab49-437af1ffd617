package com.xh.common.utils;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class MapUtils {
    // 地球赤道半径
    private static double EARTH_RADIUS = 6378.137;


    /**
     * 经纬度获取距离，单位为米
     **/
    public static double getDistance(double lng1,double lat1,double lng2 ,double lat2) {
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double a = radLat1 - radLat2;
        double b = Math.toRadians(lng1) - Math.toRadians(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000d) / 10000d;
        s = s * 1000;
        return s;
    }


    /**
     * 获取数列的笛卡尔积
     */
    public static String[][] cartesianProduct(String[][] params){
        int len = params.length;
        if(len <= 1) {
            return params;
        }

        int total = params[0].length;

        for(int i=1;i<params.length;i++){
            total = total * params[i].length;
        }

        int[] init = new int[len];
        int[][] result = new int[total][0];

        for(int index=0;index<total;index++){
            result[index] = Arrays.copyOf(init,len);
            init[len-1] ++;
            for(int i = len-1;i>=0; i--){
                if(init[i] == params[i].length){
                    if(i == 0){
                        init[i] = 0;
                    }else {
                        init[i-1]++;
                    }
                    for(int j=i;j< len;j++){
                        init[j] = 0;
                    }
                }
            }
        }

        String[][] res = new String[total][0];
        for(int index=0;index<total;index++){
            String[] str = new String[len];
            for(int i=0;i<len;i++){
                str[i] =  params[i][result[index][i]];
            }
            res[index] = str;
        }

        return res;
    }

}
