<template>
    <div class="page-content">
      <address-edit @searchAddress="search"></address-edit>
      <div class="card card--search">
        <div class="search-container">
          <div class="search-buttons">
            <el-button type="primary" @click="add" class="add-btn">添加地址</el-button>
          </div>
        </div>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table
            stripe
            :data="addressList"
            style="width: 100%;"
            class="data-table"
            fit
          >
            <el-table-column prop="id" label="ID" align="center" />
            <el-table-column prop="recipient" label="收件人" align="center" />
            <el-table-column prop="phone" label="手机号" align="center" />
            <el-table-column prop="province" label="省" align="center" />
            <el-table-column prop="city" label="市" align="center" />
            <el-table-column prop="county" label="区" align="center" />
            <el-table-column prop="town" label="乡镇" align="center" />
            <el-table-column prop="detail" label="详细地址" align="center" />
            <el-table-column
              prop="isDefault"
              label="默认"
              align="center"
              :formatter="formatDefault"
            />
            <el-table-column prop="userId" label="用户ID" align="center" />
            <el-table-column prop="createTime" label="创建时间" align="center" />
            <el-table-column prop="updateTime" label="修改时间" align="center" />
            <el-table-column align="center" width="160" label="操作">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="danger" link size="small" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination
            background
            layout="prev, pager, next"
            @current-change="currentChange"
            @prev-click="prevClick"
            @next-click="nextClick"
            :total="total"
          />
        </div>
      </div>
    </div>
</template>

<script>
import { listAddress, deleteAddress, getAddress } from '@/api/system/address'
import mitt from '@/utils/mitt'
import addressEdit from '@/components/system/addressEdit.vue'
export default {
  components: { addressEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10
      },
      addressList: [],
      total: 0
    }
  },
  methods: {
    async search() {
      try {
        const res = await listAddress(this.searchModel)
        this.addressList = res.data.data.list
        this.total = res.data.data.total
      } catch (err) {
        this.$message.error(err.data.errorMessage)
      }
    },
    add() {
      mitt.emit('openAddressAdd')
    },
    edit(id) {
      getAddress(id)
        .then(res => {
          mitt.emit('openAddressEdit', res.data.data)
        })
        .catch(err => {
          this.$message.error(err.data.errorMessage)
        })
    },
    deleted(id) {
      this.$confirm('删除地址, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAddress(id)
          .then(() => {
            this.search()
            this.$message.success('删除成功')
          })
          .catch(err => {
            this.$message.error(err.data.errorMessage)
          })
      }).catch(() => {})
    },
    currentChange(page) {
      this.searchModel.pageNum = page
      this.search()
    },
    prevClick(page) {
      this.searchModel.pageNum = page
      this.search()
    },
    nextClick(page) {
      this.searchModel.pageNum = page
      this.search()
    },
    formatDefault(row, column, cellValue) {
      return cellValue ? '是' : '否';
    }
  },
  created() {
    this.search()
    mitt.on('searchAddress', this.search)
  },
  beforeUnmount() {
    mitt.off('searchAddress', this.search)
  }
}
</script>