package xh;

import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;
import java.util.Iterator;
import java.util.Set;

public class SocketTest {
    @Test
    public void io() throws IOException {
        ServerSocket serverSocket = new ServerSocket(8888);

        while (true){
            Socket socket = serverSocket.accept();
            InputStream inputStream = socket.getInputStream();

        }

    }


    @Test
    public void nio() throws IOException {
        Selector selector = Selector.open();

        ServerSocketChannel serverChannel = ServerSocketChannel.open();

        serverChannel.bind(new InetSocketAddress(8888));
        serverChannel.configureBlocking(false);

        serverChannel.register(selector, SelectionKey.OP_ACCEPT);

        while (true){
            selector.select();
            Iterator<SelectionKey> keys = selector.selectedKeys().iterator();

            while (keys.hasNext()) {
                SelectionKey key = keys.next();
                if (key.isAcceptable()) {
                    SocketChannel channel = serverChannel.accept();
                    channel.configureBlocking(false);
                    channel.register(selector, SelectionKey.OP_READ);
                } else if (key.isReadable()) {
                    SocketChannel channel = (SocketChannel) key.channel();

                    ByteBuffer buffer = ByteBuffer.allocate(256);
                    try {
                        int bytesRead = channel.read(buffer);
                        if (bytesRead > 0) {
                            System.out.println("Received: " + new String(buffer.array()).trim());
                        } else if (bytesRead == -1) {
                            channel.close();
                        }
                    } catch (SocketException e) {
                        if(e.getMessage().contains("Connection reset")){
                            System.err.println("Error reading from channel: " + e.getMessage());
                        }

                        channel.close();
                    }
                }
                keys.remove();
            }

        }
    }
}
