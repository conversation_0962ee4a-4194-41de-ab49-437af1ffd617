package com.xh.system.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 角色信息表(Role)实体类
 *
 * <AUTHOR>
 * @since 2025-05-20 09:48:26
 */
@Data
public class Role {

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "数据权限")
    private String dataScope;

    @Schema(description = "状态")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "父级ID")
    private Long parentId;
}

