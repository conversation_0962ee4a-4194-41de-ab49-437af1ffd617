package com.xh.auth.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 住户(Resident)实体类
 *
 * <AUTHOR>
 * @since 2025-05-21 20:37:36
 */
@Data
public class RealNameForm {

    @Schema(description = "住户姓名")
    @NotBlank(message = "住户姓名不能为空")
    private String userName;

    @Schema(description = "联系电话")
    @Pattern(regexp = "^1[3-9]\\d{9}$",message = "手机号格式错误")
    @NotBlank(message = "联系电话不能为空")
    private String phone;

    @Schema(description = "验证码key")
    @NotBlank(message = "验证码key")
    private String codeKey;

    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;

}

