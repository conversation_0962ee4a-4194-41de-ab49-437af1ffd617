<view class="container">
  <custom-nav title="我的地址" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 地址列表 -->
  <scroll-view scroll-y="true" class="address-scroll">
    <view class="address-list" wx:if="{{addressList.length > 0}}">
      <view class="address-item" wx:for="{{addressList}}" wx:key="id" bindtap="{{selectMode ? 'selectAddress' : ''}}" data-index="{{index}}">
        <view class="address-content">
          <view class="address-header">
            <view class="user-info">
              <text class="user-name">{{item.recipient}}</text>
              <text class="user-phone">{{item.phone}}</text>
            </view>
            <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
          </view>
          <view class="address-detail">{{item.province}}{{item.city}}{{item.county}}{{item.detail}}</view>
        </view>
        
        <view class="address-actions" wx:if="{{!selectMode}}">
          <view class="action-btn edit-btn" bindtap="editAddress" data-id="{{item.id}}">编辑</view>
          <view class="action-btn delete-btn" bindtap="deleteAddress" data-id="{{item.id}}">删除</view>
        </view>
        
        <view class="select-indicator" wx:if="{{selectMode}}">
          <text class="select-text">选择</text>
        </view>
      </view>
    </view>
    
    <!-- 无地址提示 -->
    <view class="empty-address" wx:else>
      <view class="empty-icon">
        <view class="iconfont icon-address-empty"></view>
      </view>
      <text class="empty-text">您还没有添加地址</text>
    </view>
  </scroll-view>
  
  <!-- 底部按钮区 -->
  <view class="bottom-btns">
    <button class="btn import-btn" bindtap="importWechatAddress">导入微信地址</button>
    <button class="btn add-btn" bindtap="addNewAddress">新增地址</button>
  </view>
</view> 