/*
 Navicat Premium Data Transfer

 Source Server         : 内网199
 Source Server Type    : MySQL
 Source Server Version : 100603
 Source Host           : ************:3308
 Source Schema         : starmoon-empty

 Target Server Type    : MySQL
 Target Server Version : 100603
 File Encoding         : 65001

 Date: 20/05/2025 15:30:32
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`  (
  `id` bigint NOT NULL COMMENT '订单编号',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `pay_type` tinyint NOT NULL DEFAULT 0 COMMENT '支付类型',
  `pay_amount` decimal(10, 2) NOT NULL COMMENT '支付金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '总金额',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '订单状态',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态',
  `cancel_explain` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消说明',
  `refund_explain` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款说明',
  `spu_description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品描述',
  `note` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `org_id` bigint NULL DEFAULT NULL COMMENT '组织id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `refund_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `image` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第一个商品的图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order
-- ----------------------------
INSERT INTO `order` VALUES (1753619580061745152, NULL, 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2022-12-30 14:32:34', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1753621612730515456, NULL, 1, 0.03, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2022-12-30 15:04:52', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1753621622072279040, NULL, 1, 0.03, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, '哈哈', NULL, '2022-12-30 15:05:01', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757406722984509440, NULL, 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '张三f', NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2023-02-10 09:47:35', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757684843050696704, '4200001800202302136278647331', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 11:28:11', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757685731239657472, '4200001777202302137262458189', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 11:42:18', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757699719377715200, '4200001791202302137820344854', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 5, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 15:24:38', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757702784200736768, '4200001786202302130393613132', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 16:13:21', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757702873932627968, '4200001708202302136438448170', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 16:14:46', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757702924141592576, '4200001784202302138020135894', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 16:15:34', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1759215067580071936, NULL, 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2023-03-02 08:50:27', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306262733459030016, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:39:39', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306262930381602816, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:40:26', '2025-03-14 18:40:26', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306262980964909056, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:40:38', '2025-03-14 18:40:38', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263013302992896, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:40:46', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263126574366720, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:41:13', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263256077697024, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:41:44', '2025-03-14 18:41:44', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263352303419392, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:42:07', '2025-03-14 18:42:07', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263466015195136, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:42:34', '2025-03-14 18:42:34', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306272696617140224, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 19:19:14', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306272926284644352, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 19:20:09', '2025-03-14 19:20:19', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306273083868839936, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 19:20:47', '2025-03-14 19:21:17', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for order_detail
-- ----------------------------
DROP TABLE IF EXISTS `order_detail`;
CREATE TABLE `order_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '订单编号',
  `spu_id` bigint NOT NULL COMMENT '产品编号',
  `spu_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `spu_amount` decimal(10, 2) NOT NULL COMMENT '商品金额',
  `pay_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `quantity` int NOT NULL COMMENT '购买数量',
  `sku_id` bigint NULL DEFAULT NULL COMMENT '规格ID',
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '产品规格',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `image` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 306 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_detail
-- ----------------------------
INSERT INTO `order_detail` VALUES (277, 1753619580061745152, 142, '农夫山泉', 0.01, NULL, 1, 548, '[\"200ML\", \"北京\"]', '2022-12-30 14:32:34', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (278, 1753621612730515456, 142, '农夫山泉', 0.01, NULL, 1, 548, '[\"200ML\", \"北京\"]', '2022-12-30 15:04:52', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (279, 1753621612730515456, 142, '农夫山泉', 0.01, NULL, 1, 549, '[\"200ML\", \"云南\"]', '2022-12-30 15:04:52', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (280, 1753621612730515456, 142, '农夫山泉', 0.01, NULL, 1, 551, '[\"300ML\", \"云南\"]', '2022-12-30 15:04:52', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (281, 1753621622072279040, 142, '农夫山泉', 0.01, NULL, 1, 548, '[\"200ML\", \"北京\"]', '2022-12-30 15:05:01', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (282, 1753621622072279040, 142, '农夫山泉', 0.01, NULL, 1, 549, '[\"200ML\", \"云南\"]', '2022-12-30 15:05:01', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (283, 1753621622072279040, 142, '农夫山泉', 0.01, NULL, 1, 551, '[\"300ML\", \"云南\"]', '2022-12-30 15:05:01', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (285, 1757406722984509440, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-10 09:47:35', NULL);
INSERT INTO `order_detail` VALUES (287, 1757684843050696704, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 11:28:11', NULL);
INSERT INTO `order_detail` VALUES (288, 1757685731239657472, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 11:42:18', NULL);
INSERT INTO `order_detail` VALUES (289, 1757699719377715200, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 15:24:38', NULL);
INSERT INTO `order_detail` VALUES (290, 1757702784200736768, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 16:13:21', NULL);
INSERT INTO `order_detail` VALUES (291, 1757702873932627968, 38, '农夫三拳饮用水', 0.01, NULL, 1, 522, '[\"100ML\", \"农夫山泉\", \"国外\"]', '2023-02-13 16:14:46', NULL);
INSERT INTO `order_detail` VALUES (292, 1757702924141592576, 38, '农夫三拳饮用水', 0.01, NULL, 1, 522, '[\"100ML\", \"农夫山泉\", \"国外\"]', '2023-02-13 16:15:34', NULL);
INSERT INTO `order_detail` VALUES (293, 1759215067580071936, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-03-02 08:50:27', NULL);
INSERT INTO `order_detail` VALUES (295, 7306262733459030016, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:39:39', NULL);
INSERT INTO `order_detail` VALUES (296, 7306262930381602816, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:40:26', NULL);
INSERT INTO `order_detail` VALUES (297, 7306262980964909056, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:40:38', NULL);
INSERT INTO `order_detail` VALUES (298, 7306263013302992896, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:40:46', NULL);
INSERT INTO `order_detail` VALUES (299, 7306263126574366720, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:41:13', NULL);
INSERT INTO `order_detail` VALUES (300, 7306263256077697024, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:41:44', NULL);
INSERT INTO `order_detail` VALUES (301, 7306263352303419392, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:42:07', NULL);
INSERT INTO `order_detail` VALUES (302, 7306263466015195136, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:42:34', NULL);
INSERT INTO `order_detail` VALUES (303, 7306272696617140224, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 19:19:14', NULL);
INSERT INTO `order_detail` VALUES (304, 7306272926284644352, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 19:20:09', NULL);
INSERT INTO `order_detail` VALUES (305, 7306273083868839936, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 19:20:47', NULL);

-- ----------------------------
-- Table structure for spu
-- ----------------------------
DROP TABLE IF EXISTS `spu`;
CREATE TABLE `spu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '产品编号',
  `spu_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `category_id` bigint NULL DEFAULT NULL COMMENT '分类id',
  `description` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '产品描述',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '产品状态',
  `details` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '产品详细信息，富文本',
  `media_url` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '用于展示的图片或视频',
  `create_time` datetime NULL DEFAULT current_timestamp COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更改时间',
  `org_id` bigint NOT NULL COMMENT '组织id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 144 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '商品基本信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu
-- ----------------------------
INSERT INTO `spu` VALUES (36, '好东西', 4, '好勒痕', 0, NULL, NULL, '2022-05-08 11:47:57', '2025-03-08 18:58:47', 1);
INSERT INTO `spu` VALUES (38, '农夫三拳饮用水', 4, '好喝嘞很aaa', 0, NULL, NULL, '2022-05-08 13:19:25', '2025-03-08 18:58:41', 1);
INSERT INTO `spu` VALUES (140, '121', 3, '1212', 0, NULL, NULL, '2022-07-08 10:42:43', '2025-03-08 16:46:07', 1);
INSERT INTO `spu` VALUES (141, '121', 3, '2121', 0, NULL, NULL, '2022-09-22 15:37:44', '2025-03-08 16:46:03', 1);
INSERT INTO `spu` VALUES (142, '农夫山泉', 4, '我们不生产水', 0, NULL, NULL, '2022-10-10 11:07:36', '2025-03-08 18:58:35', 2);
INSERT INTO `spu` VALUES (143, '测试a', 6, '无uuuuu阿达啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊', 0, NULL, NULL, '2022-12-30 11:26:44', '2025-03-08 16:26:59', 2);

-- ----------------------------
-- Table structure for spu_category
-- ----------------------------
DROP TABLE IF EXISTS `spu_category`;
CREATE TABLE `spu_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `category_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `category_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `org_id` bigint NOT NULL COMMENT '组织id',
  `parend_id` bigint NULL DEFAULT 0 COMMENT '父id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_category
-- ----------------------------
INSERT INTO `spu_category` VALUES (3, '蔬菜', 'vege', '2022-04-01 13:12:53', 1, NULL);
INSERT INTO `spu_category` VALUES (4, '水', 'water', '2022-06-06 18:52:57', 1, NULL);
INSERT INTO `spu_category` VALUES (6, '食品', '1', '2022-12-30 11:06:46', 2, NULL);

-- ----------------------------
-- Table structure for spu_sku
-- ----------------------------
DROP TABLE IF EXISTS `spu_sku`;
CREATE TABLE `spu_sku`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '规格',
  `spu_id` bigint NOT NULL COMMENT '产品编号',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `stock` bigint NOT NULL DEFAULT 0 COMMENT '库存',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `image` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 566 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品交易单元' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_sku
-- ----------------------------
INSERT INTO `spu_sku` VALUES (520, '[\"100ML\", \"农夫山泉\", \"河南\"]', 38, 0.01, 960, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (521, '[\"100ML\", \"农夫山泉\", \"北京\"]', 38, 0.01, 15, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (522, '[\"100ML\", \"农夫山泉\", \"国外\"]', 38, 0.01, 3, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (523, '[\"100ML\", \"清泉\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (524, '[\"100ML\", \"清泉\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (525, '[\"100ML\", \"清泉\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (526, '[\"100ML\", \"流响\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (527, '[\"100ML\", \"流响\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (528, '[\"100ML\", \"流响\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (529, '[\"500ML\", \"农夫山泉\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (530, '[\"500ML\", \"农夫山泉\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (531, '[\"500ML\", \"农夫山泉\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (532, '[\"500ML\", \"清泉\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (533, '[\"500ML\", \"清泉\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (534, '[\"500ML\", \"清泉\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (535, '[\"500ML\", \"流响\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (536, '[\"500ML\", \"流响\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (537, '[\"500ML\", \"流响\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);

-- ----------------------------
-- Table structure for spu_specs_name
-- ----------------------------
DROP TABLE IF EXISTS `spu_specs_name`;
CREATE TABLE `spu_specs_name`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '属性名编号',
  `spu_id` bigint NOT NULL COMMENT '产品编号',
  `specs_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名',
  `description` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 174 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格名表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_specs_name
-- ----------------------------
INSERT INTO `spu_specs_name` VALUES (148, 36, '11', NULL, '2022-07-06 17:22:08');
INSERT INTO `spu_specs_name` VALUES (149, 36, '22', NULL, '2022-07-06 17:22:08');
INSERT INTO `spu_specs_name` VALUES (164, 38, '容量', NULL, '2022-07-07 14:57:04');
INSERT INTO `spu_specs_name` VALUES (168, 38, '品牌', NULL, '2022-07-07 15:03:52');
INSERT INTO `spu_specs_name` VALUES (169, 38, '产地', NULL, '2022-07-07 15:03:52');
INSERT INTO `spu_specs_name` VALUES (170, 141, '123', NULL, '2022-09-22 15:37:44');
INSERT INTO `spu_specs_name` VALUES (171, 141, '12', NULL, '2022-09-22 15:37:44');
INSERT INTO `spu_specs_name` VALUES (172, 142, '容量', NULL, '2022-10-10 11:07:36');
INSERT INTO `spu_specs_name` VALUES (173, 142, '产地', NULL, '2022-10-10 11:07:36');

-- ----------------------------
-- Table structure for spu_specs_value
-- ----------------------------
DROP TABLE IF EXISTS `spu_specs_value`;
CREATE TABLE `spu_specs_value`  (
  `id` bigint NOT NULL COMMENT 'id',
  `specs_name_id` bigint NULL DEFAULT NULL COMMENT '规格id',
  `specs_value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格值',
  `description` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_specs_value
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
