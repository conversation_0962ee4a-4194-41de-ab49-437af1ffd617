package com.xh.print.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.print.domain.dto.OrderDTO;
import com.xh.print.domain.entity.Order;
import com.xh.print.domain.search.OrderSearch;
import com.xh.print.service.OrderService;
import com.xh.print.dao.OrderDao;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单(Order)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:27
 */
@Tag(name = "管理端，订单模块")
@RestController
@RequestMapping("manage-api/v1/order")
public class OrderController {

    @Autowired
    private OrderService orderService;
    
    @Autowired
    private OrderDao orderDao;


    @Operation(summary = "分页查询订单")
    @GetMapping("page")
    public ResponseEntity<PageInfo<OrderDTO>> queryByPage(OrderSearch search) {
        PageHelper.startPage(search.getPageNum(),search.getPageSize());
        PageHelper.orderBy("id desc");
        List<OrderDTO> orders = orderService.queryByList(search);
        return ResponseEntity.success(new PageInfo<>(orders));
    }


    @Operation(summary = "通过ID查询订单详情")
    @GetMapping
    public ResponseEntity<OrderDTO> queryById(Long id) {
        return ResponseEntity.success(orderService.queryDetailById(id));
    }


    @Operation(summary = "删除订单")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(orderService.deleteById(id));
    }

}

