-- 修复order_detail表结构
-- 执行日期：2025年1月

-- 检查并确保order_detail表有正确的字段结构
-- 根据截图显示的字段结构，表已经包含以下字段：
-- id, order_id, file_name, file_path, unit_price, pay_amount, quantity, specs, create_time, sku_id, page_num, page_size

-- 1. 确保所有必要字段存在（如果字段已存在会报错，可以忽略）

-- 添加SKU ID字段（如果不存在）
-- ALTER TABLE `order_detail` ADD COLUMN `sku_id` bigint NULL COMMENT 'SKU ID';

-- 添加文件路径字段（如果不存在）  
-- ALTER TABLE `order_detail` ADD COLUMN `file_path` varchar(128) NULL COMMENT '文件路径';

-- 添加页数字段（如果不存在）
-- ALTER TABLE `order_detail` ADD COLUMN `page_num` int NULL COMMENT '起始页数';

-- 添加页码字段（如果不存在）
-- ALTER TABLE `order_detail` ADD COLUMN `page_size` int NULL COMMENT '结束页数';

-- 2. 检查字段是否已重命名
-- 如果还有旧字段spu_name，需要重命名为file_name
-- ALTER TABLE `order_detail` CHANGE COLUMN `spu_name` `file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件名称';

-- 如果还有旧字段spu_amount，需要重命名为unit_price  
-- ALTER TABLE `order_detail` CHANGE COLUMN `spu_amount` `unit_price` decimal(10,2) NULL COMMENT '单价';

-- 3. 验证表结构
-- 执行完成后，可以用以下语句验证表结构：
-- DESCRIBE `order_detail`;

-- 4. 预期的完整表结构应该包含：
/*
Field         Type           Null    Key     Default    Extra
id            bigint         NO      PRI     NULL       auto_increment
order_id      bigint         YES             NULL
file_name     varchar(128)   YES             NULL       文件名称
file_path     varchar(128)   YES             NULL       文件路径  
unit_price    decimal(10,2)  YES             NULL       单价
pay_amount    decimal(10,2)  YES             NULL       支付金额
quantity      int            YES             NULL       购买数量
specs         longtext       YES             NULL       产品规格
create_time   datetime       YES             NULL       创建时间
sku_id        bigint         YES             NULL       SKU ID
page_num      int            YES             NULL       起始页数
page_size     int            YES             NULL       结束页数
*/

-- 说明：根据截图显示，表结构已经正确，OrderDetailDao.xml的映射也已更新完成
 