package com.xh.print.controller.app;

import com.xh.common.web.domain.ResponseEntity;
import com.xh.print.domain.entity.PrintAttr;
import com.xh.print.domain.entity.PrintSku;
import com.xh.print.service.PrintSpecsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 商品交易单元(PrintSku)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-07 15:33:55
 */
@Tag(name = "用户端，打印模块")
@RestController
@RequestMapping("users-api/v1/print")
public class UsersPrintController {
 
    @Autowired
    private PrintSpecsService printSpecsService;

    @GetMapping("specs")
    public ResponseEntity<List<PrintSku>> getPrintSku() {
        return ResponseEntity.success(printSpecsService.queryByList());
    }

    @GetMapping("attr")
    public ResponseEntity<List<PrintAttr>> getPrintAttr() {
        return ResponseEntity.success(printSpecsService.queryAttrByList());
    }
  
}

