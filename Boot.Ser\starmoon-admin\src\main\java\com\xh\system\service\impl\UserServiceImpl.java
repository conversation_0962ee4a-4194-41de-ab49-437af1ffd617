package com.xh.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.xh.system.dao.UserDao;
import com.xh.system.domain.dto.PasswordEditDTO;
import com.xh.system.domain.dto.UserCreateDTO;
import com.xh.system.domain.dto.UserEditDTO;
import com.xh.system.domain.entity.User;
import com.xh.system.service.UserService;
import com.xh.common.security.user.UserDetail;
import com.xh.common.utils.SecurityUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息表(User)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 09:50:28
 */
@Service
public class UserServiceImpl implements UserService{

    @Autowired
    private UserDao userDao;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User queryById(Long id) {
        return  userDao.queryById(id);
    }

    @Override
    public List<User> queryByList(String keyword, Integer status, Long orgId, String mobile){
        return userDao.queryByList(keyword, null, status, orgId,mobile);
    }

    @Override
    public PageInfo<User> queryByPage(String keyword, Integer status, Long orgId, String phone) {
        List<User> userList = userDao.queryByList(keyword, null, status, orgId, phone);
        PageInfo<User> pageInfo = new PageInfo<>(userList);
        return pageInfo;
    }


    @Override
    public Long insert(UserCreateDTO userBO) {
        if(checkUserNameExist(userBO.getUserName())){
            throw new ServiceException("用户名已存在");
        }

        String encryption = passwordEncoder.encode(userBO.getPassword());
        userBO.setPassword(encryption);
        User user = new User();
        BeanUtils.copyProperties(userBO,user);
        userDao.insert(user);

        return user.getId();
    }

    @Override
    public boolean update(UserEditDTO dto) {
        User user = new User();
        BeanUtils.copyProperties(dto,user);
        return userDao.update(user) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        UserDetail userDetail = SecurityUtils.currentPrincipal(UserDetail.class);
        if(id.longValue() == userDetail.getId().longValue()){
            throw new ServiceException("无法删除自己");
        }

        return userDao.deleteById(id) > 0;
    }

    //TODO 不允许修改别人的密码
    @Override
    public boolean updatePassword(PasswordEditDTO passwordEditDTO) {

        User user = userDao.queryById(passwordEditDTO.getId());

        if(!passwordEncoder.matches(passwordEditDTO.getOldPassword(),user.getPassword())){
            throw new ServiceException("旧密码错误");
        }

        user.setPassword(passwordEncoder.encode(passwordEditDTO.getNewPassword()));

        return userDao.update(user) > 0;
    }

    //TODO 改为sql 结果大于0 就是存在
    private boolean checkUserNameExist(String userName) {
        List<User> userList = userDao.queryByList(null, userName, null, null, null);
        if(userList.size() > 0){
            return true;
        }
        return false;
    }

}
