package com.xh.print.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;


@Data
public class PrintSku {

    private Long id;

    @Schema(description = "规格")
    private String specs;

    @Schema(description = "规格快照")
    private String specsSnapshot;

    @Schema(description = "金额")
    private Double amount;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;


}

