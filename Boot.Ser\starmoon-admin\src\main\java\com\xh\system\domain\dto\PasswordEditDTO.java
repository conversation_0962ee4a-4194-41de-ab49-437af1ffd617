package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class PasswordEditDTO {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "旧密码")
    private String oldPassword;

    @Schema(description = "新密码")
    @Pattern(regexp = "^[a-zA-Z0-9.!@#$%&*]{5,17}\\S$",message = "密码必须为6-18位数字字符")
    private String newPassword;
}
