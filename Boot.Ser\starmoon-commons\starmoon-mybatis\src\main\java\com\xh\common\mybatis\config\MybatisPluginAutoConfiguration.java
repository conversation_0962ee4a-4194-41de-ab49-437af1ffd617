package com.xh.common.mybatis.config;

import com.github.pagehelper.autoconfigure.PageHelperProperties;
import com.xh.common.mybatis.interceptor.PageInterceptor;
import com.xh.common.mybatis.interceptor.ParamsInterceptor;
import com.xh.common.mybatis.number.NumberGenerate;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Configuration
public class MybatisPluginAutoConfiguration {

    @Value("${number.machine-id}")
    private Long machineId;

    private NumberGenerate numberGenerate;

    private final List<SqlSessionFactory> sqlSessionFactoryList;

    private PageHelperProperties properties;

    @Autowired
    public MybatisPluginAutoConfiguration(List<SqlSessionFactory> sqlSessionFactoryList) {
        this.sqlSessionFactoryList = sqlSessionFactoryList;
        this.numberGenerate = new NumberGenerate(1);

        afterPropertiesSet();
    }

    public void afterPropertiesSet() {
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            org.apache.ibatis.session.Configuration factoryConfiguration = sqlSessionFactory.getConfiguration();
            PageInterceptor pageInterceptor = new PageInterceptor();
            ParamsInterceptor paramsInterceptor = new ParamsInterceptor();
            factoryConfiguration.addInterceptor(pageInterceptor);
            factoryConfiguration.addInterceptor(paramsInterceptor);
        }
    }

}
