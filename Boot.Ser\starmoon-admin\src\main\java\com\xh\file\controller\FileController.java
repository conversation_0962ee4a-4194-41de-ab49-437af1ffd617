package com.xh.file.controller;

import com.xh.common.web.domain.ResponseEntity;
import com.xh.file.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2025324
 */
@Tag(name = "公共模块")
@RestController
@RequestMapping("common-api/v1/file")
public class FileController {


    @Resource
    private FileService fileService;

    @Operation(summary = "获取资源文件")
    @GetMapping("**")
    public void load(){
        fileService.loadResource();
    }

    @Operation(summary = "上传文件")
    @PostMapping(value = "upload",consumes = "multipart/form-data")
    public ResponseEntity<String> upload(@RequestPart MultipartFile file){
        return ResponseEntity.success(fileService.upload(file));
    }


}
