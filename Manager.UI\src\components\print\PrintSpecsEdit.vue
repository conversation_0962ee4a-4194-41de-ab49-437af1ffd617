<template>
  <el-dialog title="编辑规格" v-model="dialog.show" width="35%" v-loading="loading" destroy-on-close>
    <el-form :model="skuModel" label-width="100px">
      <el-form-item label="规格">
        <el-input v-model="skuModel.specs" disabled />
      </el-form-item>
      <el-form-item label="金额">
        <el-input-number v-model="skuModel.amount" :min="0" :precision="2" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="skuModel.note" />
      </el-form-item>
      <el-form-item label="图片">
        <el-upload class="avatar-uploader" :action="uploadUrl" :headers="headers" :show-file-list="false" :on-success="uploadSuccess" list-type="picture-card">
          <img v-if="skuModel.imageShow" :src="imgServer + skuModel.imageUrl" style="width:100%;height:100%;" />
          <el-button v-else size="small" type="primary">上传图片</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {  editSku } from '@/api/print/printSettings'
import mitt from '@/utils/mitt'
export default {
  name: 'PrintSpecsEdit',
  data() {
    return {
      loading: false,
      skuModel: { id: 0},
      dialog: { show: false, title: '' },
      uploadUrl: import.meta.env.VITE_BASE_API + '/common-api/v1/file/upload',
      imgServer: import.meta.env.VITE_BASE_API + '/common-api/v1/file/',
      headers: { 'Authorization': JSON.parse(localStorage.getItem('token')).access_token }
    }
  },
  methods: {
     onSubmit() {
      this.loading = true
      editSku(this.skuModel)
        .then(res => {
          if (res.data.code === 0) {
            this.$message.success('保存成功')
            this.$emit('search')
            this.dialog.show = false
        }
        this.loading = false
      })
      .catch(err => {
        this.$message.error(err.data?.errorMessage || err.message)
        this.loading = false
      })
     
    },
    uploadSuccess(res) {
      if (res.code === 0) {
        this.skuModel.imageShow = true
        this.skuModel.imageUrl = res.data
      } else {
        this.$message.error(res.message)
      }
    }
  },
  mounted() {
    mitt.on('openPrintSpecsEdit', (res)=>{
      console.log("res",res)
      this.skuModel = res.data
      this.dialog.show = true
      this.dialog.title = '编辑规格'
    })
  },
  beforeUnmount() {
    mitt.off('openPrintSpecsEdit')
  }
}
</script>

<style scoped>
.avatar-uploader {
  width: 100px;
  height: 100px;
}
.dialog-footer {
  text-align: right;
}
</style> 