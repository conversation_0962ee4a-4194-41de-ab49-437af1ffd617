<template>
	<el-dialog width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="dictModel" label-width="120px" class="dict-edit-form">
			<el-row class="form-row">
				<el-col :span="12">
					<el-form-item label="中文名称" prop="nameCn">
						<el-input v-model="dictModel.nameCn" placeholder="请输入中文名称" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="英文名称" prop="nameEn">
						<el-input v-model="dictModel.nameEn" placeholder="请输入英文名称" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row class="form-row">
				<el-col :span="12">
					<el-form-item label="父级ID" prop="parentId">
						<el-input-number
							v-model="dictModel.parentId"
							placeholder="父级ID"
							:min="0"
							:max="9007199254740991"
							style="width: 100%;"
							disabled>
						</el-input-number>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="排序值" prop="sort">
						<el-input-number
							v-model="dictModel.sort"
							placeholder="排序值"
							:min="0"
							:max="1073741824"
							style="width: 100%;">
						</el-input-number>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row class="form-row">
				<el-col :span="24">
					<el-form-item label="CSS类名/颜色" prop="cssClass">
						<div class="css-class-container">
							<el-input
								v-model="dictModel.cssClass"
								placeholder="请输入CSS类名或颜色值（如：#FF0000、red、primary）"
								clearable
								class="css-input">
								<template #append>
									<el-color-picker
										v-model="selectedColor"
										@change="onColorChange"
										show-alpha
										:predefine="predefineColors">
									</el-color-picker>
								</template>
							</el-input>
						</div>
						<div class="color-buttons">
							<span class="color-label">快速选择：</span>
							<el-button
								v-for="color in quickColors"
								:key="color.value"
								:type="color.type"
								size="small"
								@click="selectQuickColor(color.value)"
								:style="getQuickColorStyle(color)"
								class="color-btn">
								{{ color.label }}
							</el-button>
						</div>
						<div v-if="dictModel.cssClass" class="color-preview">
							<span class="preview-label">预览：</span>
							<el-tag
								:style="getPreviewStyle()"
								class="preview-tag">
								示例标签
							</el-tag>
						</div>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row class="form-row">
				<el-col :span="24">
					<el-form-item label="备注说明" prop="note">
						<el-input
							v-model="dictModel.note"
							type="textarea"
							placeholder="请输入备注说明"
							:rows="3"
							maxlength="500"
							show-word-limit
							clearable>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialog.show = false">取 消</el-button>
				<el-button type="primary" @click="onSubmit" :loading="loading">确 定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import { addDict,editDict } from "@/api/system/dict";
import mitt from "@/utils/mitt";

export default {
	name: 'DictEdit',
	data() {
		return {
			loading: false,
			dictModel: {
				id: 0,
				nameEn: '',
				nameCn: '',
				cssClass: '',
				parentId: 0,
				sort: 0,
				note: ''
			},
			dialog: {
				show: false,
				title: ''
			},
			selectedColor: '',
			// 预定义颜色
			predefineColors: [
				'#ff4500',
				'#ff8c00',
				'#ffd700',
				'#90ee90',
				'#00ced1',
				'#1e90ff',
				'#c71585',
				'#ff1493',
				'#00ff00',
				'#ff0000'
			],
			// 快速选择颜色
			quickColors: [
				{ label: '主要', value: 'primary', type: 'primary' },
				{ label: '成功', value: 'success', type: 'success' },
				{ label: '信息', value: 'info', type: 'info' },
				{ label: '警告', value: 'warning', type: 'warning' },
				{ label: '危险', value: 'danger', type: 'danger' },
				{ label: '红色', value: '#F56C6C', type: '' },
				{ label: '蓝色', value: '#409EFF', type: '' },
				{ label: '绿色', value: '#67C23A', type: '' },
				{ label: '黄色', value: '#E6A23C', type: '' },
				{ label: '紫色', value: '#9C27B0', type: '' }
			],
			rules: {
				nameCn: [{
					required: true,
					message: '请输入中文名称',
					trigger: 'blur',
				}],
				nameEn: [{
					required: true,
					message: '请输入英文名称',
					trigger: 'blur',
				}],
				sort: [{
					type: 'number',
					message: '排序值必须为数字',
					trigger: 'blur',
				}],
				parentId: [{
					type: 'number',
					message: '父级ID必须为数字',
					trigger: 'blur',
				}]
			}
		}
	},
methods: {
		/**
		 * 提交表单
		 */
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					this.loading = true;

					// 确保数据类型正确
					const submitData = {
						...this.dictModel,
						sort: Number(this.dictModel.sort) || 0,
						parentId: Number(this.dictModel.parentId) || 0
					};

					const api= this.dictModel.id == 0 ? addDict : editDict;

					api(submitData)
						.then(() => {
							this.$message.success("操作成功");
							this.$emit("search");
							this.dialog.show = false;
							this.resetForm();
						})
						.catch(err => {
							this.$message.error(err.data?.errorMessage || '操作失败');
						})
						.finally(() => {
							this.loading = false;
						});
				}
			});
		},

		/**
		 * 颜色选择器变化事件
		 */
		onColorChange(color) {
			if (color) {
				// 确保颜色值为十六进制格式
				if (color.startsWith('rgb')) {
					// 如果是RGB格式，转换为十六进制
					const rgb = color.match(/\d+/g);
					if (rgb && rgb.length >= 3) {
						const hex = '#' + rgb.slice(0, 3).map(x => {
							const hex = parseInt(x).toString(16);
							return hex.length === 1 ? '0' + hex : hex;
						}).join('');
						this.dictModel.cssClass = hex.toUpperCase();
					}
				} else {
					this.dictModel.cssClass = color.toUpperCase();
				}
			}
		},

		/**
		 * 获取快速选择按钮的样式
		 */
		getQuickColorStyle(color) {
			// 如果是Element Plus预定义类型，不设置自定义样式
			const elementTypes = ['primary', 'success', 'info', 'warning', 'danger'];
			if (elementTypes.includes(color.type)) {
				return {};
			}

			// 为实际颜色值设置背景色
			if (color.value.startsWith('#')) {
				return {
					backgroundColor: color.value,
					color: this.getContrastColor(color.value),
					borderColor: color.value
				};
			}

			return {};
		},

		/**
		 * 快速选择颜色
		 */
		selectQuickColor(color) {
			this.dictModel.cssClass = color;
			// 如果是十六进制颜色，同步到颜色选择器
			if (color.startsWith('#')) {
				this.selectedColor = color;
			} else {
				this.selectedColor = '';
			}
		},

		/**
		 * 获取预览样式
		 */
		getPreviewStyle() {
			const cssClass = this.dictModel.cssClass;
			if (!cssClass) return {};

			// 如果是十六进制颜色
			if (cssClass.startsWith('#')) {
				return {
					backgroundColor: cssClass,
					color: this.getContrastColor(cssClass)
				};
			}

			// 如果是RGB颜色
			if (cssClass.startsWith('rgb')) {
				return {
					backgroundColor: cssClass,
					color: '#fff'
				};
			}

			// 如果是预定义的Element Plus类型
			const elementTypes = ['primary', 'success', 'info', 'warning', 'danger'];
			if (elementTypes.includes(cssClass)) {
				return {};
			}

			// 其他情况作为CSS类名处理
			return {
				backgroundColor: cssClass,
				color: '#fff'
			};
		},

		/**
		 * 获取对比色（用于文字颜色）
		 */
		getContrastColor(hexColor) {
			// 移除#号
			const hex = hexColor.replace('#', '');

			// 转换为RGB
			const r = parseInt(hex.substr(0, 2), 16);
			const g = parseInt(hex.substr(2, 2), 16);
			const b = parseInt(hex.substr(4, 2), 16);

			// 计算亮度
			const brightness = (r * 299 + g * 587 + b * 114) / 1000;

			// 根据亮度返回黑色或白色
			return brightness > 128 ? '#000' : '#fff';
		},

		/**
		 * 重置表单
		 */
		resetForm() {
			this.dictModel = {
				id: 0,
				nameEn: '',
				nameCn: '',
				cssClass: '',
				parentId: 0,
				sort: 0,
				note: ''
			};
			this.selectedColor = '';

			// 重置表单验证
			this.$nextTick(() => {
				if (this.$refs.form) {
					this.$refs.form.clearValidate();
				}
			});
		}
	},
	mounted() {
		mitt.on('openDictEdit', (dict) => {
			this.resetForm();
			this.dictModel = {
				...dict,
				sort: Number(dict.sort) || 0,
				parentId: Number(dict.parentId) || 0
			};

			// 如果有颜色值，同步到颜色选择器
			if (dict.cssClass && dict.cssClass.startsWith('#')) {
				this.selectedColor = dict.cssClass;
			}

			this.dialog.show = true;
			this.dialog.title = "修改字典信息";
		});

		mitt.on('openDictAdd', (parentId) => {
			this.resetForm();
			this.dictModel = {
				id: 0,
				nameEn: '',
				nameCn: '',
				cssClass: '',
				parentId: Number(parentId) || 0,
				sort: 0,
				note: ''
			};
			this.dialog.show = true;
			this.dialog.title = "添加字典";
		});
	},

	beforeUnmount() {
		mitt.off('openDictEdit');
		mitt.off('openDictAdd');
	}
}
</script>

<style scoped>
.dict-edit-form :deep(.el-form-item) {
	margin-bottom: 16px !important;
}

.form-row {
	margin-bottom: 8px;
}

.css-class-container {
	width: 100%;
}

.css-input {
	width: 100%;
}

.color-buttons {
	margin-top: 8px;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 8px;
}

.color-label {
	font-size: 12px;
	color: #666;
	margin-right: 4px;
}

.color-btn {
	margin: 0 !important;
}

.color-preview {
	margin-top: 8px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.preview-label {
	font-size: 12px;
	color: #666;
}

.preview-tag {
	font-size: 12px;
}

.dialog-footer {
	text-align: right;
}

/* 颜色选择器样式优化 */
.css-input :deep(.el-input-group__append) {
	padding: 0 8px;
}

.css-input :deep(.el-color-picker) {
	height: 32px;
}

.css-input :deep(.el-color-picker__trigger) {
	width: 32px;
	height: 32px;
	border: none;
	border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.color-buttons {
		flex-direction: column;
		align-items: flex-start;
	}

	.color-btn {
		width: 100%;
		justify-content: center;
	}
}

/* 暗色主题适配 */
.dark-theme .color-label,
.dark-theme .preview-label {
	color: #ccc;
}

.dark-theme .css-input :deep(.el-input-group__append) {
	background-color: var(--el-fill-color-light);
	border-color: var(--el-border-color);
}
</style>