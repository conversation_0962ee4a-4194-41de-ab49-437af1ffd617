package com.xh.system.service;


import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.MenuCreateDTO;
import com.xh.system.domain.dto.MenuDTO;
import com.xh.system.domain.dto.MenuEditDTO;
import com.xh.system.domain.entity.Menu;

import java.util.List;

/**
 * 菜单权限表(Menu)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 09:41:03
 */
public interface MenuService {

    Menu queryById(Long id);

    List<Menu> queryByList(String menuName, Long parentId, Long roleId);

    PageInfo<MenuDTO> queryByPage(String menuName, Long roleId);

    Long insert(MenuCreateDTO dto);

    boolean update(MenuEditDTO dto);

    boolean deleteById(Long id);
}
