package com.xh.system.domain.entity;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 图文读取状态管理表(ImagetextReadstatus)实体类
 *
 * <AUTHOR>
 * @since 2025-05-21 08:50:39
 */
@Data
public class ImagetextReadstatus implements Serializable {

    private static final long serialVersionUID = 249545994724754891L;

    @Schema(description = "图文id")
    private Long imagetextId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "阅读时间")
    private Date readTime;

}

