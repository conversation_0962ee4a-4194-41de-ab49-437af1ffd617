package com.xh.system.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 角色和菜单关联表(RoleMenu)实体类
 *
 * <AUTHOR>
 * @since 2021-12-16 10:31:49
 */
@Data
public class RoleMenu implements Serializable {

    private static final long serialVersionUID = -78108324806443059L;

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "菜单ID")
    private Long menuId;

    @Schema(description = "菜单ID")
    private Long[] menuIds;
}

