package com.xh.common.utils;

import com.xh.common.web.exception.ServiceException;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

public class ObjectUtils {

    public static byte[] objectToByteArray(Object obj){
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ObjectOutputStream objectOutputStream = new ObjectOutputStream(outputStream);
            objectOutputStream.writeObject(obj);
            return outputStream.toByteArray();
        }catch (Exception e){
            throw new ServiceException("序列化失败");
        }
    }

    public static <T> T byteAryToObject(byte[] bytes, Class<T> clazz) throws Exception {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        ObjectInputStream objectInputStream = new ObjectInputStream(inputStream);
        return (T)objectInputStream.readObject();
    }
}
