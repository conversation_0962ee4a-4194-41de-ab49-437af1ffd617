<template>
  <div class="page-container">
    <div class="page-content">
      <imagetext-edit @search="search" :typeList="typeList" ref="editDialog" />
      <div class="card card--search">
        <div class="search-container">
          <div class="search-form">
            <el-input v-model="searchModel.title" placeholder="标题" clearable style="width: 200px;" />
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="search" class="search-btn">搜索</el-button>
            <el-button type="primary" @click="add" class="add-btn">添加</el-button>
          </div>
        </div>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table 
            :data="imagetextList" 
            row-key="id" 
            style="width: 100%;" 
            class="data-table"
            fit
            >
            <el-table-column prop="id" label="ID" align="center"/>
            <el-table-column prop="title" label="标题" align="center"/>
            <el-table-column prop="imageUrl" label="图片" align="center">
              <template #default="scope">
                <el-image v-if="scope.row.imageUrl" :src="imgServer + scope.row.imageUrl"
                 style="width: 60px; height: 40px;" fit="cover" :preview-src-list="[imgServer + scope.row.imageUrl]"/>
              </template>
            </el-table-column>
            <el-table-column prop="link" label="跳转链接" align="center"/>
            <el-table-column prop="sort" label="排序" align="center"/>
            <el-table-column prop="type" label="类型" :formatter="formatType" align="center"/>
            <el-table-column prop="createTime" label="创建时间" align="center"/>
            <el-table-column prop="updateTime" label="修改时间" align="center"/>
            <el-table-column label="操作" width="180" align="center">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="edit(scope.row.id)" class="action-btn">编辑</el-button>
                <el-button type="danger" link size="small" @click="deleted(scope.row.id)" class="action-btn">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listImagetext, deleteImagetext, getImagetext } from '@/api/system/imagetext'
import { listDictByNameEn } from "@/api/system/dict"
import mitt from '@/utils/mitt'
import imagetextEdit from '@/components/system/imagetextEdit.vue'
export default {
  components: { imagetextEdit },
  data() {
    return {
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      searchModel: {
        pageNum: 1,
        pageSize: 10
      },
      imagetextList: [],
      typeList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listImagetext(this.searchModel).then(res => {
        this.imagetextList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    add() {
      mitt.emit('openImagetextAdd')
    },
    edit(id) {
      getImagetext(id).then(res => {
        mitt.emit('openImagetextEdit', res.data.data)
      })
    },
    deleted(id) {
      this.$confirm('删除图文, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteImagetext(id).then(res => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    formatType(row, column, cellValue, index) {
      let result = ''
      for (let item of this.typeList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn
        }
      }
      return result
    },
    async init() {
      try {
        const [imagetext_res, type_res] = await Promise.all([
          listImagetext(this.searchModel),
          listDictByNameEn('sys_imagetext')
        ])
        this.imagetextList = imagetext_res.data.data.list
        this.total = imagetext_res.data.data.total
        this.typeList = type_res.data.data
      } catch (err) {
        this.$message.error(err.data.errorMessage)
      }
    }
  },
  created() {
    this.init()
  }
}
</script>

<style scoped>
/* 组件特定样式可以添加在这里 */
</style> 