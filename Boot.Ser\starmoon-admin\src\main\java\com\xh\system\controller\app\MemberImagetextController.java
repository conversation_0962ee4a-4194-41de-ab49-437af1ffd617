package com.xh.system.controller.app;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.system.domain.entity.Imagetext;
import com.xh.system.service.ImagetextService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户端")
@RestController
@RequestMapping("users-api/v1/imagetext")
public class MemberImagetextController {

    @Autowired
    private ImagetextService imagetextService;

    @Parameters({
            @Parameter(name = "pageNum", description = "页码"),
            @Parameter(name = "pageSize", description = "每页数量"),
            @Parameter(name = "type", description = "类型（banner：轮播图,notice：通知,about_us：关于我们,privacy_policy：隐私政策）", required = false),
    })
    @Operation(summary = "通过分页查询图文")
    @GetMapping("page")
    public ResponseEntity<PageInfo<Imagetext>> queryByPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "type", required = false) String type
    ) {
        PageHelper.startPage(pageNum, pageSize);
        PageHelper.orderBy("id desc");

        return ResponseEntity.success(imagetextService.queryByPage(type));
    }

    @Parameter(name = "id", description = "图文ID", required = true)
    @Operation(summary = "通过ID查询图文")
    @GetMapping
    public ResponseEntity<Imagetext> queryById(Long id) {
        return ResponseEntity.success(imagetextService.queryById(id));
    }
}
