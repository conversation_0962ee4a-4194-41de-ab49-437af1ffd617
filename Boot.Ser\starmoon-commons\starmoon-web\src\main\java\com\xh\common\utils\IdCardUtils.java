package com.xh.common.utils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 身份证号验证
 */
public class IdCardUtils {

    /** 中国公民证件号码码最小长度。 */
    private static final int CHINA_ID_MIN_LENGTH = 15;

    /** 中国公民证件号码码最大长度。 */
    private static final int CHINA_ID_MAX_LENGTH = 18;

    /** 每位加权因子 */
    private static final int power[] = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };

    /** 第18位校检码 */
    private static final String verifyCode[] = { "1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2" };

    private static Map<Integer,String> areaMap = new HashMap<>();

    /**
     * 将15位证件号码码转换为18位
     *
     * @param idNo
     *            15位身份编码
     * @return 18位身份编码
     */
    private static String idNoL15ToL18(String idNo) {
        if(idNo != null && idNo.length() == CHINA_ID_MIN_LENGTH){
            StringBuffer sb = new StringBuffer(idNo);
            sb.insert(6,"19");
            int[] ints = Arrays.asList(sb.toString().split("")).stream().mapToInt(num -> Integer.valueOf(String.valueOf(num))).toArray();
            String checkCode = getCheckCode18(getPowerSum(ints));
            sb.append(checkCode);
            return sb.toString();
        }
        return null;
    }

    /**
     * 验证身份证是否合法
     */
    public static boolean validate(String idNo) {
        if(validateL15(idNo.toUpperCase())){
            return true;
        }
        return validateL18((idNo.toUpperCase()));
    }

    /**
     * 验证18位身份编码是否合法
     *
     * @param idNo 身份编码
     * @return 是否合法
     */
    private static boolean validateL18(String idNo) {
        if(idNo != null && idNo.length() == CHINA_ID_MAX_LENGTH){
            StringBuffer sb = new StringBuffer(idNo);
            int[] ints = Arrays.asList(sb.toString().substring(0,17).split("")).stream().mapToInt(num -> Integer.valueOf(String.valueOf(num))).toArray();
            String checkCode = getCheckCode18(getPowerSum(ints));
            if(!checkCode.equals(sb.toString().substring(17,CHINA_ID_MAX_LENGTH))){
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 验证15位身份编码是否合法
     *
     * @param idNo
     *            身份编码
     * @return 是否合法
     */
    private static boolean validateL15(String idNo) {
        return validateL18(idNoL15ToL18(idNo));
    }


    /**
     * 将身份证的每位和对应位的加权因子相乘之后，再得到和值
     *
     * @param iArr
     * @return 身份证编码。
     */
    private static int getPowerSum(int[] iArr) {
        int count = 0;
        for (int i=0;i<iArr.length;i++){
            count+=iArr[i]*power[i];
        }
        return count;
    }

    /**
     * 将power和值与11取模获得余数进行校验码判断
     *
     * @param iSum
     * @return 校验位
     */
    private static String getCheckCode18(int iSum) {
        return verifyCode[iSum%11];
    }

    /**
     * 根据身份编号获取年龄
     *
     * @param idNo 身份编号
     * @return 年龄
     */
    public static int getAgeByIdCard(String idNo) {
        if(!validate(idNo)){
            return -1;
        }
        if(idNo.length() == CHINA_ID_MIN_LENGTH){
            idNo = idNoL15ToL18(idNo);
        }
        SimpleDateFormat sd = new SimpleDateFormat("yyyyMMdd");
        try {
            Calendar instance = Calendar.getInstance();
            instance.setTime(sd.parse(idNo.substring(6, 14)));
            int year = instance.get(Calendar.YEAR);
            int month = instance.get(Calendar.MONTH)+1;
            int day = instance.get(Calendar.DATE);

            instance.setTime(new Date());
            int cYear = instance.get(Calendar.YEAR);
            int cMonth = instance.get(Calendar.MONTH) + 1;
            int cDay = instance.get(Calendar.DATE);

            int age = cYear -year;

            int a = Integer.valueOf(month + "" + day);
            int b = Integer.valueOf(cMonth + "" + cDay);
            //是否过了生日
            if(b<a){
                age--;
            }
            return age;
        }catch (Exception e){
            return -1;
        }
    }

    public static int getAgeByBirthDay(Date birthDay){
        if (birthDay == null){
            return -1;
        }
        try {
            Calendar instance = Calendar.getInstance();
            instance.setTime(birthDay);
            int year = instance.get(Calendar.YEAR);
            int month = instance.get(Calendar.MONTH)+1;
            int day = instance.get(Calendar.DATE);

            instance.setTime(new Date());
            int cYear = instance.get(Calendar.YEAR);
            int cMonth = instance.get(Calendar.MONTH) + 1;
            int cDay = instance.get(Calendar.DATE);

            int age = cYear -year;

            int a;
            int b;
            if (String.valueOf(day).length()<1){
                a = Integer.valueOf(month + "0" + day);
            }else{
                a = Integer.valueOf(month + "" + day);
            }
            if (String.valueOf(cDay).length()<1){
                b = Integer.valueOf(cMonth + "0" + cDay);
            }else{
                b = Integer.valueOf(cMonth + "" + cDay);
            }
            //是否过了生日
            if(b<a){
                age--;
            }
            return age;
        }catch (Exception e){
            return -1;
        }
    }

    public static Date getBirthDayByIdCard(String idCard){
        if (!validate(idCard)){
            return null;
        }
        if(idCard.length() == CHINA_ID_MIN_LENGTH){
            idCard = idNoL15ToL18(idCard);
        }
        SimpleDateFormat sd = new SimpleDateFormat("yyyyMMdd");
        try {
            Calendar instance = Calendar.getInstance();
            instance.setTime(sd.parse(idCard.substring(6, 14)));
            int year = instance.get(Calendar.YEAR);
            int month = instance.get(Calendar.MONTH)+1;
            int day = instance.get(Calendar.DATE);
            return DateUtils.stringToDate(year+"-"+month+"-"+day,DateUtils.FORMAT_DATE);
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 根据身份编号获取性别
     *
     * @param idNo 身份编号
     * @return 性别(1-男，2-女，0-未知)
     */
    public static String getGenderByIdCard(String idNo) {
        if(!validate(idNo)){
            return "unknown";
        }
        if(idNo.length() == CHINA_ID_MIN_LENGTH ){
            idNo = idNoL15ToL18(idNo);
        }
        return Integer.valueOf(idNo.substring(16, 17)) % 2 == 0 ? "woman" : "man";
    }

    /**
     * 根据身份编号获取户籍省份
     *
     * @param idNo 身份编码
     * @return 省级编码。
     */
    public static String getProvinceByIdCard(String idNo) {
        if(validate(idNo)){
            return areaMap.get(Integer.valueOf(idNo.substring(0,6)));
        }
        return null;
    }
}