import axios from 'axios'

export var baseUrl = import.meta.env.VITE_BASE_API

// 创建axios实例
const service = axios.create({
    baseURL: baseUrl,
    timeout: 5000
})

// request拦截器
service.interceptors.request.use(config => {
    try {
        var authentication = localStorage.getItem("token")
        if (authentication) {
            const authData = JSON.parse(authentication);
            if (authData && authData.access_token) {
                config.headers['Authorization'] = authData.access_token;
            }
        }
    } catch (e) {
        console.error('处理认证信息时出错:', e);
    }
    return config;
}, error => {
    return Promise.reject(error);
})

// respone拦截器
service.interceptors.response.use(
    async response => {
        let code = response.data.code
        if (code == 401) {
            localStorage.clear()
            location.reload()
        } else if (code != 0) {
            return Promise.reject(response)
        } else {
            return response
        }
    },
    error => {
        //网络错误
        if (error && error.message === 'Network Error') {
            return Promise.reject("请求超时");
        }

        // 返回错误，确保错误能被正确捕获
        return Promise.reject(error);
    })

export default service
