package com.xh.ai.autoconfigure;

import com.xh.ai.tools.OrderServiceTool;
import com.xh.ai.vectorstore.RedisVectorStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.InMemoryChatMemory;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class OpenAiAutoConfigure {

    private String alibabaBaseUrl = "https://dashscope.aliyuncs.com/compatible-mode";

    @Value("${spring.ai.dashscope.api-key}")
    private String alibabaApiKey;

    @Value("${spring.ai.dashscope.chat.model}")
    private String alibabaChatModel;

    @Value("${spring.ai.dashscope.embedding.model}")
    private String alibabaEmbeddingModel;


    @Value("${spring.ai.ollama.base-url}")
    private String ollamaBaseUrl;

    @Value("${spring.ai.ollama.chat.model}")
    private String ollamaChatModel;

    @Value("${spring.ai.ollama.embedding.model}")
    private String ollamaEmbeddingModel;

    @Autowired
    private OrderServiceTool orderServiceTool;

    @Bean
    public ChatMemory chatMemory() {
        return new InMemoryChatMemory();
    }

    @Bean
    public ChatClient openAiChatClient(ChatMemory chatMemory) {
        OpenAiApi openAiApi = OpenAiApi.builder().apiKey(alibabaApiKey).baseUrl(ollamaBaseUrl).build();
        OpenAiChatOptions options = OpenAiChatOptions.builder()
                .model(ollamaChatModel)
                .temperature(0.3)
                .topP(0.4)
                .build();

        OpenAiChatModel openAiChatModel = OpenAiChatModel.builder()
                .defaultOptions(options)
                .openAiApi(openAiApi)
                .build();

        return ChatClient.builder(openAiChatModel)
                .defaultSystem("你是智能客服，可以配用户聊天和帮助客户完成操作")
                .defaultAdvisors(new PromptChatMemoryAdvisor(chatMemory))
                .build();
    }


    @Bean
    public VectorStore vectorStore(RedisConnectionFactory redisConnectionFactory) {
        OpenAiApi api = OpenAiApi.builder().apiKey(alibabaApiKey).baseUrl(alibabaBaseUrl).build();

        OpenAiEmbeddingOptions options = OpenAiEmbeddingOptions.builder().model(alibabaEmbeddingModel).build();
        OpenAiEmbeddingModel embeddingModel = new OpenAiEmbeddingModel(api, MetadataMode.EMBED, options);

        RedisVectorStore vectorStore = RedisVectorStore.builder().embeddingModel(embeddingModel).redisConnection(redisConnectionFactory.getConnection()).build();

        return vectorStore;
    }

}