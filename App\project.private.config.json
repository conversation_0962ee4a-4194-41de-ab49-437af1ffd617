{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "%E5%88%BA%E7%8C%AC%E4%BA%91%E5%8D%B0", "setting": {"compileHotReLoad": true, "skylineRenderEnable": false, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "bigPackageSizeSupport": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true}, "libVersion": "3.8.11", "condition": {"miniprogram": {"list": [{"name": "pages/print/settings/settings", "pathName": "pages/print/settings/settings", "query": "", "scene": null, "launchMode": "default"}, {"name": "pages/print/list/list", "pathName": "pages/print/list/list", "query": "", "launchMode": "default", "scene": null}]}}}