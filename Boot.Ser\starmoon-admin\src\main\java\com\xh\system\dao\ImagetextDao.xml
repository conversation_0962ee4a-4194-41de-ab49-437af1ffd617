<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.ImagetextDao">

    <resultMap type="com.xh.system.domain.entity.Imagetext" id="ImagetextMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="link" column="link" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ImagetextMap">
        select
        id, title, image_url, content, link, sort, create_by, type, create_time, update_time
        from sys_imagetext
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="ImagetextMap">
        select
        id, title, image_url, content, link, sort, create_by, type, create_time, update_time
        from sys_imagetext
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_imagetext(title, image_url, content, link, sort, create_by, type, create_time)
        values (#{title}, #{imageUrl}, #{content}, #{link}, #{sort}, #{createBy}, #{type}, now())
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_imagetext
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                image_url = #{imageUrl},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="link != null and link != ''">
                link = #{link},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="createBy != null and '' != createBy">
                create_by = #{createBy},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_imagetext where id = #{id}
    </delete>

</mapper>

