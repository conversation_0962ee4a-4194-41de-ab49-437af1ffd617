package com.xh.file.service.impl;

import com.xh.common.utils.FileUtils;
import com.xh.common.utils.HttpClientUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.file.service.FileService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025324
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    // 允许的图片类型
    private static final List<String> ALLOWED_IMAGES = Arrays.asList(
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/bmp",
            "image/webp"
    );

    // 允许的图片类型
    private static final List<String> ALLOWED_WORD = Arrays.asList(
            "application/pdf",
            "application/doc",
            "application/docx"
    );


    private boolean isWindows = System.getProperty("os.name").toLowerCase().contains("windows");

    private static final String UPLOAD_DIR = "D:/uploads/";

    private static final String ASSETS_DIR = "assets";

    @Override
    public String upload(MultipartFile file) {
        if (file.isEmpty()) {
            return "请上传文件";
        }

        // 检查文件类型是否为图片
        if (!ALLOWED_IMAGES.contains(file.getContentType()) &&  !ALLOWED_WORD.contains(file.getContentType())) {
            throw new ServiceException("仅允许上传图片文件（jpg, png, gif, bmp, webp）");
        }

        try {
            byte[] bytes = file.getBytes();
            if(ALLOWED_IMAGES.contains(file.getContentType())){
                if (!FileUtils.isImageFile(bytes)) {
                    throw new ServiceException("仅允许上传图片文件（jpg, png, gif, bmp, webp,pdf,doc,docx）");
                }
            }

            String originalFilename = file.getOriginalFilename();
            String suffix = originalFilename.substring(file.getOriginalFilename().indexOf("."));
            String newFilename = String.valueOf(UUID.randomUUID()).replaceAll("-", "") + suffix;
            File folder = new File(UPLOAD_DIR + ASSETS_DIR);
            if(!folder.exists()){
                folder.mkdirs();
            }

            Path path = Paths.get(UPLOAD_DIR + ASSETS_DIR +File.separator + newFilename);
            Files.write(path, bytes);

            return ASSETS_DIR + "/" + newFilename;

        } catch (IOException e) {
            log.error("文件上传失败：{}" ,e);
            throw new ServiceException("上传失败");
        }
    }

    @Override
    public void loadResource() {
        HttpServletRequest request = HttpClientUtils.getHttpServletRequest();
        HttpServletResponse response = HttpClientUtils.getHttpServletResponse();

        String filePath = request.getRequestURI().replace("/common-api/v1/file", "");

        Path path = Paths.get(UPLOAD_DIR + filePath);

        try {
            ServletOutputStream outputStream = response.getOutputStream();
            // 允许上传的文件后缀列表

            String contentType = Files.probeContentType(path);

            if (ALLOWED_IMAGES.contains(contentType)) {
                response.setContentType("image/png");
                // 告诉浏览器进行缓存
                response.setHeader("Cache-Control", "public, max-age=3600");
            } else {
                response.setHeader("Content-Type", "application/octet-stream");
                response.setContentType("application/octet-stream; charset=UTF-8");
            }
            File file = path.toFile();
            if (!file.exists()) {
                response.setStatus(404);
            }
            Files.copy(path, outputStream);
            outputStream.flush();

            outputStream.close();

        }catch (IOException e){
            response.setStatus(404);
        }
    }

    @Override
    public File getFileByPath(String filePath) {
        Path path = Paths.get(UPLOAD_DIR + filePath);
        return path.toFile();
    }


}
