package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MenuDTO {

    @Schema(description = "菜单ID")
    private Long id;

    @Schema(description = "菜单名")
    private String menuName;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "菜单路径（显示路径如/index）")
    private String path;

    @Schema(description = "权限")
    private String permission;

    @Schema(description = "组件路径")
    private String componentPath;

    @Schema(description = "菜单类型")
    private String menuType;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "子级")
    private List<MenuDTO> children;
}
