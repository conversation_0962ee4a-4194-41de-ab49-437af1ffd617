package com.xh.common.web.statecode;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum StatusCode {
    LACK_PARAMS(10001,"缺少必要参数"),
    REQ_PARAMS_ERR(10002,"请检查请求参数"),
    FILE_CONTENT_ERROR(10002,"文件数据错误"),
    DATA_FORMAT_ERROR(10003,"数据检验不通过"),
    VERIFY_CODE_NOT_EXIST(1001,"验证码无效"),
    VERIFY_CODE_ERROR(1003,"验证码验证失败"),
    USERNAME_PASSWORD_ERROR(1006,"用户名密码错误"),
    USERNAME_DISABLE(1008,"账户被禁用"),

    SUCCESS(0,"操作成功"),
    FAIL(1,"业务错误"),

    UNAUTHORIZED(401,"未认证"),
    DENIED_ACCESS(403,"没有权限"),
    SYSTEM_ERROR(500,"系统错误");

    private final Integer code;
    private final String message;

    StatusCode(Integer code, String message){
        this.code = code;
        this.message = message;
    }
}
