package com.xh.common.security.config;

import com.xh.common.security.filter.ServerAuthenticationFilter;
import com.xh.common.security.handler.AuthenticationExceptionHandler;
import com.xh.common.security.oauth2.AuthorizationCodeService;
import com.xh.common.security.oauth2.RedisAuthorizationCodeService;
import com.xh.common.security.token.RedisTokenService;
import com.xh.common.security.token.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.CorsConfigurer;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * web安全配置类的适配器
 *
 * <AUTHOR>
 * @since 2023-5-29
 */
@Configuration
public class SecurityConfig {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        DefaultSecurityFilterChain securityFilterChain = http.authorizeHttpRequests(auth ->
                        auth.requestMatchers(
                                "/swagger-ui/**",
                                "/v3/api-docs",
                                "/common-api/v1/file/assets/**",
                                "/common-api/v1/sms/code",
                                "/users-api/v1/auth/token",
                                "/users-api/v1/auth/send-sms-code",
                                "/users-api/v1/auth/register",
                                "/manage-api/v1/chat/**",
                                "/manage-api/v1/auth/public-key",
                                "/manage-api/v1/auth/verify-code",
                                "/manage-api/v1/auth/token",
                                "/manage-api/v1/auth/refresh-token"
                        ).permitAll().anyRequest().authenticated())
                .exceptionHandling(ex -> ex.authenticationEntryPoint(new AuthenticationExceptionHandler()))
                .addFilterBefore(new ServerAuthenticationFilter(tokenService()), LogoutFilter.class)
                .sessionManagement(AbstractHttpConfigurer::disable)
                .csrf(AbstractHttpConfigurer::disable)
                .cors(cors())
                .build();
        return securityFilterChain;
    }

    @Bean
    public TokenService tokenService() {
        return new RedisTokenService(redisConnectionFactory.getConnection());
    }

    @Bean
    public AuthorizationCodeService authorizationCodeService() {
        return new RedisAuthorizationCodeService(redisConnectionFactory.getConnection());
    }

    @Bean
    public AuthenticationManager authenticationManager() {
        return new AuthenticationManager() {
            @Override
            public Authentication authenticate(Authentication authentication) throws AuthenticationException {
                return null;
            }
        };
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    private Customizer<CorsConfigurer<HttpSecurity>> cors() {
        return new Customizer<CorsConfigurer<HttpSecurity>>() {
            @Override
            public void customize(CorsConfigurer<HttpSecurity> httpSecurityCorsConfigurer) {
                httpSecurityCorsConfigurer.configurationSource(new CorsConfigurationSource() {
                    @Override
                    public CorsConfiguration getCorsConfiguration(HttpServletRequest request) {
                        CorsConfiguration corsConfiguration = new CorsConfiguration();
                        corsConfiguration.addAllowedHeader("*");
                        corsConfiguration.addAllowedMethod("*");
                        corsConfiguration.addAllowedOriginPattern("*");
                        corsConfiguration.setAllowCredentials(true);
                        corsConfiguration.setMaxAge(3600L);
                        return corsConfiguration;
                    }
                });
            }
        };
    }
}
