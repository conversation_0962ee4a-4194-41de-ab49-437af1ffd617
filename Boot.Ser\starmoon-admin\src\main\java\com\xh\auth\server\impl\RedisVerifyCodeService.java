package com.xh.auth.server.impl;

import com.xh.auth.domain.VerifyCode;
import com.xh.auth.server.VerifyCodeService;
import com.xh.auth.utils.ImageUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Random;

@Slf4j
@Service
public class RedisVerifyCodeService implements VerifyCodeService {

    public static String VERIFY_CODE = "verifyCode:";
    private RedisConnection redisConnection;

    @Autowired
    public RedisVerifyCodeService(RedisConnectionFactory redisConnectionFactory){
        this.redisConnection = redisConnectionFactory.getConnection();
    }

    /**
     *
     * @param key
     * @return
     */
    @Override
    public VerifyCode generateVerifyCode(String key) {
        try {

            File background = new File(ClassLoader.getSystemResource("static/image/background").getFile());
            File[] backgrounds = background.listFiles();

            File template = new File(ClassLoader.getSystemResource("static/image/template").getFile());
            File[] templates = template.listFiles();

            Random random = new Random();
            VerifyCode verifyCode = ImageUtils.slideVerifyCode(templates[random.nextInt(templates.length)], backgrounds[random.nextInt(backgrounds.length)]);

            byte[] verifyCodeKey = (VERIFY_CODE+key).getBytes();
            redisConnection.set(verifyCodeKey, String.valueOf(verifyCode.getX()).getBytes());
            redisConnection.expire(verifyCodeKey,60);

            return verifyCode;
        }catch (Exception e){
            log.error("生成验证码出错：{}",e.getMessage());
            throw new ServiceException(StatusCode.SYSTEM_ERROR);
        }
    }

    @Override
    public boolean verifyCode(String id, String inputCodeValue) {
        byte[] verifyCodeKey = (VERIFY_CODE+id).getBytes();
        byte[] data = redisConnection.get(verifyCodeKey);

        if(data == null){
            throw new ServiceException(StatusCode.VERIFY_CODE_NOT_EXIST);
        }

        String code = new String(data);

        if(inputCodeValue == null || inputCodeValue == null){
            return false;
        }
        try {
            double value = Double.parseDouble(code);
            double codeValue = Double.parseDouble(inputCodeValue);
            double res = value - codeValue;
            if(res > -2 && res < 2){
                return true;
            }
        }catch (Exception e){
            return false;
        }
        redisConnection.del(verifyCodeKey);
        return false;
    }

}
