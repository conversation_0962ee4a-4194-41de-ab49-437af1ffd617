package com.xh.common.security.token;

import com.xh.common.utils.ObjectUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.security.core.Authentication;

/**
 * 内存里token服务 redis也是存内存
 */
@Slf4j
public class RedisTokenService implements TokenService{

    //access获取accessToken
    private static String ACCESS_BY_ACCESS_TOKEN = "access_by_access_token:";

    //access获取auth
    private static String ACCESS_BY_AUTH = "access_by_auth:";

    //access获取refresh
    private static String ACCESS_BY_REFRESH = "access_by_refresh:";

    //refresh获取auth
    private static String REFRESH_BY_AUTH = "refresh_by_auth:";

    private RedisConnection redisConnection;

    public RedisTokenService(RedisConnection redisConnection){
        this.redisConnection = redisConnection;
    }

    @Override
    public AccessToken getAccessByToken(String token) {
        byte[] bytes = redisConnection.get((ACCESS_BY_ACCESS_TOKEN+token).getBytes());
        try {
            return ObjectUtils.byteAryToObject(bytes, AccessToken.class);
        }catch (Exception e){
            log.error("令牌无效");
            return null;
        }
    }

    @Override
    public Authentication getAuthenticationByToken(String token) {
        byte[] bytes = redisConnection.get((ACCESS_BY_AUTH+token).getBytes());
        try {
            return ObjectUtils.byteAryToObject(bytes, Authentication.class);
        }catch (Exception e){
            log.error("令牌无效");
            return null;
        }
    }

    @Override
    public Authentication getAuthenticationByRefresh(String refreshToken) {
        byte[] bytes = redisConnection.get((REFRESH_BY_AUTH+refreshToken).getBytes());
        try {
            return ObjectUtils.byteAryToObject(bytes, Authentication.class);
        }catch (Exception e){
            log.error("令牌无效");
            return null;
        }
    }

    @Override
    public AccessToken refreshByRefresh(String refreshToken) {

        Authentication authentication = getAuthenticationByRefresh(refreshToken);
        if(authentication == null){
            throw new ServiceException(StatusCode.UNAUTHORIZED);
        }
        AccessToken accessToken = new AccessToken();
        storeAccessToken(accessToken,authentication);

        destroy(null,refreshToken);

        return accessToken;
    }

    @Override
    public void destroy(String accessToken,String refreshToken) {
        if(accessToken != null && refreshToken == null){

            byte[] accessByAccessTokenKey = (ACCESS_BY_ACCESS_TOKEN+accessToken).getBytes();
            byte[] accessByAuthKey = (ACCESS_BY_AUTH+accessToken).getBytes();
            byte[] accessByRefreshKey = (ACCESS_BY_REFRESH+accessToken).getBytes();
            byte[] refreshKey = redisConnection.get(accessByRefreshKey);

            redisConnection.del(accessByAccessTokenKey);
            redisConnection.del(accessByAuthKey);
            redisConnection.del(accessByRefreshKey);
            if(refreshKey != null && refreshKey.length > 0){
                redisConnection.del(refreshKey);
            }
        }

        if(refreshToken != null){
            byte[] refreshTokenKey = (REFRESH_BY_AUTH+refreshToken).getBytes();
            redisConnection.del(refreshTokenKey);
        }

    }

    @Override
    public void storeAccessToken(AccessToken token,Authentication authentication) {
        byte[] tokenByAccessToken = (ACCESS_BY_ACCESS_TOKEN+token.getAccessToken()).getBytes();

        byte[] tokenKey = (ACCESS_BY_AUTH+token.getAccessToken()).getBytes();
        byte[] tokenByRefreshKey = (ACCESS_BY_REFRESH+token.getAccessToken()).getBytes();
        byte[] refreshKey = (REFRESH_BY_AUTH+token.getRefreshToken()).getBytes();

        redisConnection.set(tokenByAccessToken, ObjectUtils.objectToByteArray(token));
        redisConnection.set(tokenKey, ObjectUtils.objectToByteArray(authentication));
        redisConnection.set(tokenByRefreshKey, token.getRefreshToken().getBytes());
        redisConnection.set(refreshKey, ObjectUtils.objectToByteArray(authentication));

        redisConnection.expire(tokenByAccessToken,token.getExpiresIn());
        redisConnection.expire(tokenKey,token.getExpiresIn());
        redisConnection.expire(tokenByRefreshKey,token.getExpiresIn());
        redisConnection.expire(refreshKey,token.getExpiresIn()*3);
    }
}
