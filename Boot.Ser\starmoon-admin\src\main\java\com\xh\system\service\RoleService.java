package com.xh.system.service;


import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.RoleCreateDTO;
import com.xh.system.domain.dto.RoleDTO;
import com.xh.system.domain.dto.RoleEditDTO;
import com.xh.system.domain.entity.Role;

import java.util.List;

/**
 * 角色信息表(Role)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 09:48:27
 */
public interface RoleService {

    RoleDTO queryById(Long id);

    List<Role> queryByList(String roleName, Integer status);

    PageInfo<Role> queryByPage(String roleName, Integer status);

    Long insert(RoleCreateDTO dto);

    boolean update(RoleEditDTO dto);

    boolean deleteById(Long id);

}
