package com.xh.system.service;


import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.PasswordEditDTO;
import com.xh.system.domain.dto.UserCreateDTO;
import com.xh.system.domain.dto.UserEditDTO;
import com.xh.system.domain.entity.User;

import java.util.List;

/**
 * 用户信息表(User)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 09:50:28
 */
public interface UserService {

    User queryById(Long id);

    List<User> queryByList(String keyword, Integer status, Long orgId, String mobile);

    PageInfo<User> queryByPage(String keyword, Integer status, Long orgId, String mobile);

    Long insert(UserCreateDTO dto);

    boolean update(UserEditDTO dto);

    boolean deleteById(Long id);

    boolean updatePassword(PasswordEditDTO passwordEditDTO);

}
