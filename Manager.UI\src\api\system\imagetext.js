import request from '@/utils/request'

// 分页查询图文
export function listImagetext(params) {
  return request({
    url: '/manage-api/v1/imagetext/page',
    method: 'get',
    params
  })
}

// 查询图文详情
export function getImagetext(id) {
  return request({
    url: '/manage-api/v1/imagetext',
    method: 'get',
    params: { id }
  })
}

// 新增图文
export function addImagetext(data) {
  return request({
    url: '/manage-api/v1/imagetext',
    method: 'post',
    data
  })
}

// 编辑图文
export function editImagetext(data) {
  return request({
    url: '/manage-api/v1/imagetext',
    method: 'put',
    data
  })
}

// 删除图文
export function deleteImagetext(id) {
  return request({
    url: '/manage-api/v1/imagetext',
    method: 'delete',
    params: { id }
  })
} 