package com.xh.system.controller;

import com.github.pagehelper.PageInfo;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.system.domain.dto.AddressCreateDTO;
import com.xh.system.domain.dto.AddressEditDTO;
import com.xh.system.domain.entity.Address;
import com.xh.system.service.AddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Tag(name = "管理端，系统模块")
@RestController
@RequestMapping("manage-api/v1/address")
public class AddressController {

    @Autowired
    private AddressService addressService;

    @Operation(summary = "分页查询地址列表")
    @PreAuthorize("hasAuthority('sys:address:query')")
    @GetMapping("page")
    public ResponseEntity<PageInfo<Address>> queryByPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "userId", required = false) Long userId
    ) {
        return ResponseEntity.success(addressService.queryByPage(pageNum, pageSize, userId));
    }

    @Operation(summary = "通过ID查询地址")
    @PreAuthorize("hasAuthority('sys:address:query')")
    @GetMapping
    public ResponseEntity<Address> queryById(@RequestParam("id") Long id) {
        return ResponseEntity.success(addressService.queryById(id));
    }

    @Operation(summary = "新增地址")
    @PreAuthorize("hasAuthority('sys:address:add')")
    @PostMapping
    public ResponseEntity<Long> insert(@Valid @RequestBody AddressCreateDTO dto) {
        return ResponseEntity.success(addressService.insert(dto));
    }

    @Operation(summary = "修改地址")
    @PreAuthorize("hasAuthority('sys:address:edit')")
    @PutMapping
    public ResponseEntity<Boolean> update(@Valid @RequestBody AddressEditDTO dto) {
        return ResponseEntity.success(addressService.update(dto));
    }

    @Operation(summary = "删除地址")
    @PreAuthorize("hasAuthority('sys:address:delete')")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(@RequestParam("id") Long id) {
        return ResponseEntity.success(addressService.deleteById(id));
    }
} 