package com.xh.system.dao;


import com.xh.system.domain.entity.ImagetextReadstatus;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 图文读取状态管理表(ImagetextReadstatus)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-21 08:50:39
 */
public interface ImagetextReadstatusDao {

    /**
     * 通过ID查询单条数据
     *
     * @param imagetextId 主键
     * @return 实例对象
     */
    ImagetextReadstatus queryById(Long imagetextId);

    /**
     * 查询指定行数据
     *
     * @param imagetextReadstatus 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<ImagetextReadstatus> queryAllByLimit(ImagetextReadstatus imagetextReadstatus, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param imagetextReadstatus 查询条件
     * @return 总行数
     */
    long count(ImagetextReadstatus imagetextReadstatus);

    /**
     * 新增数据
     *
     * @param imagetextReadstatus 实例对象
     * @return 影响行数
     */
    int insert(ImagetextReadstatus imagetextReadstatus);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ImagetextReadstatus> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ImagetextReadstatus> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ImagetextReadstatus> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ImagetextReadstatus> entities);

    /**
     * 修改数据
     *
     * @param imagetextReadstatus 实例对象
     * @return 影响行数
     */
    int update(ImagetextReadstatus imagetextReadstatus);

    /**
     * 通过主键删除数据
     *
     * @param imagetextId 主键
     * @return 影响行数
     */
    int deleteById(Long imagetextId);

}

