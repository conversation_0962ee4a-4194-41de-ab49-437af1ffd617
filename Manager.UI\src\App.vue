<template>
	<div id="app">
		<router-view></router-view>
	</div>
</template>

<script>
export default {
	mounted() {
		// 确保主题正确初始化
		if (this.$store && this.$store.theme) {
			console.log('初始化主题:', this.$store.theme.currentTheme);
			this.$store.theme.applyTheme();
		}

		// 监听主题变化
		this.$watch(
			() => this.$store && this.$store.theme ? this.$store.theme.currentTheme : 'light',
			(newTheme) => {
				// 主题变化时更新
				console.log('主题已更改为:', newTheme);
			}
		);
	}
}
</script>

<style>
	html,
	body,
	#app {
		width: 100vw;
		height: 100vh;
		margin: 0px;
		padding: 0px;
		transition: background-color var(--transition-duration), color var(--transition-duration);
	}
</style>