package com.xh.common.shadingsphere;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ShardingSphereConfig {


//    @Bean
//    public DataSource getShardingDataSource() throws SQLException, IOException {
//
//        Properties projectPropertiesByYml = YmlUtils.getProjectPropertiesByYml("sharding.yml");
//        // 获取数据源对象
//        ShardingSphereDataSource shardingSphereDataSource = new ShardingSphereDataSource(null, null);
//
//        return shardingSphereDataSource;
//    }

}
