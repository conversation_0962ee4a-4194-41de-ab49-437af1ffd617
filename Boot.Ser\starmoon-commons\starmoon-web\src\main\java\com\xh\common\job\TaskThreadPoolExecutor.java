package com.xh.common.job;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class TaskThreadPoolExecutor{
    private ThreadPoolExecutor threadPoolExecutor;
    /**
     * TODO 自己实现一个阻塞队列来实现定时任务的自由暂停开始
     */
    private DelayQueue<JobDetail> delayedDelayQueue;
    private Thread thread;
    private boolean state;

    public TaskThreadPoolExecutor() {

        this.delayedDelayQueue = new DelayQueue<>();
        this.threadPoolExecutor = new ThreadPoolExecutor(10,15,10, TimeUnit.SECONDS,new ArrayBlockingQueue<>(100));
        this.state = true;
        thread = new Thread(()->{
            while (state){
                try {
                    Thread.sleep(100);
                    JobDetail take = delayedDelayQueue.take();
                    if(state){
                        threadPoolExecutor.execute(take.getJob());
                        if(take.isNext()){
                            JobDetail jobDetail = JobBuilder.builder().build(take);
                            scheduleJob(jobDetail);
                        }
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        this.thread.start();
    }

    public void scheduleJob(JobDetail jobDetail){
        delayedDelayQueue.put(jobDetail);
    }

    public void deleteJob(String jobName){

    }

    public void shutdown(){
        this.state = false;
        this.threadPoolExecutor.shutdown();
    }
}
