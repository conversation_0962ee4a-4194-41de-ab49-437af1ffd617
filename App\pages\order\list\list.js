const app = getApp();
var api = require('../../../utils/api.js');
var sender = require('../../../utils/sender.js');

Page({
  data: {
    currentTab: 0, // 当前选中的tab
    tabs: ['全部', '待付款', '待配送', '已完成'],
    orderList: [], // 订单列表
    loading: false,
    pageNum: 1,
    pageSize: 10,
    hasMore: true
  },

  onLoad: function (options) {
    this.loadOrderList();
  },
  
  // 切换Tab
  switchTab: function (e) {
    const index = e.currentTarget.dataset.index;
    if (this.data.currentTab === index) {
      return;
    }
    
    this.setData({
      currentTab: index,
      orderList: [],
      pageNum: 1,
      hasMore: true
    });
    
    this.loadOrderList();
  },
  
  // 加载订单列表
  loadOrderList: function () {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    this.setData({
      loading: true
    });
    
    // 构建请求参数
    let params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize
    };
    
    // 根据tab设置状态筛选
    if (this.data.currentTab > 0) {
      params.status = this.data.currentTab - 1; // 0待付款，1待配送，2配送中，3已完成
    }
    
    sender.requestUrl({
      url: api.api_order_list,
      method: 'GET',
      params: params
    }, (data) => {
      // 处理订单数据
      let orders = Array.isArray(data) ? data : (data.list || []);
      
      // 处理每个订单的数据
      orders = orders.map(order => {
        // 设置状态文本
        const statusTexts = ['待付款', '待配送', '配送中', '已完成', '已取消'];
        order.statusText = statusTexts[order.status] || '未知状态';
        
        // 格式化时间
        if (order.createTime) {
          order.createTimeFormatted = this.formatTime(order.createTime);
        }
        
        // 解析地址JSON
        if (order.address && typeof order.address === 'string') {
          try {
            order.addressInfo = JSON.parse(order.address);
          } catch (e) {
            console.error('地址JSON解析失败:', e);
            order.addressInfo = {
              recipient: '未知',
              phone: '未知'
            };
          }
        }
        
        // 确保detailList存在
        if (!order.detailList) {
          order.detailList = [];
        }
        
        return order;
      });
      
      this.setData({
        orderList: [...this.data.orderList, ...orders],
        pageNum: this.data.pageNum + 1,
        hasMore: orders.length === this.data.pageSize,
        loading: false
      });
    }, (error) => {
      console.error('加载订单列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({
        loading: false
      });
    });
  },
  
  // 格式化时间
  formatTime: function(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },
  
  // 查看订单详情
  viewOrderDetail: function (e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/order/detail/detail?id=' + orderId
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      orderList: [],
      pageNum: 1,
      hasMore: true
    });
    
    this.loadOrderList();
    wx.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom: function () {
    this.loadOrderList();
  },
  
  // 快速支付
  quickPay: function (e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showLoading({
      title: '处理中...'
    });
    
    sender.requestUrl({
      url: api.api_order_pay,
      method: 'POST',
      data: { id: orderId }
    }, (data) => {
      wx.hideLoading();
      
      // 调用微信支付
      if (data.payParams) {
        wx.requestPayment({
          ...data.payParams,
          success: (res) => {
            wx.showToast({
              title: '支付成功',
              icon: 'success'
            });
            
            // 刷新列表
            setTimeout(() => {
              this.setData({
                orderList: [],
                pageNum: 1,
                hasMore: true
              });
              this.loadOrderList();
            }, 1500);
          },
          fail: (err) => {
            wx.showToast({
              title: '支付失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '支付参数错误',
          icon: 'none'
        });
      }
    }, (error) => {
      wx.hideLoading();
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    });
  },
  
  // 取消订单
  cancelOrder: function (e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });
          
          sender.requestUrl({
            url: api.api_order_cancel,
            method: 'POST',
            data: { id: orderId }
          }, (data) => {
            wx.hideLoading();
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            });
            
            // 刷新列表
            setTimeout(() => {
              this.setData({
                orderList: [],
                pageNum: 1,
                hasMore: true
              });
              this.loadOrderList();
            }, 1500);
          }, (error) => {
            wx.hideLoading();
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            });
          });
        }
      }
    });
  }
}) 