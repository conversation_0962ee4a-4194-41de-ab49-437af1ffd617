package com.xh.print.dao;

import com.xh.print.domain.entity.TempFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (TempFile)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-21 15:47:07
 */
public interface TempFileDao {

    TempFile queryById(Long id);

    TempFile queryByIdForUsers(@Param("id") Long id,@Param("openid") String openid);

    List<TempFile> queryByList(@Param("openid") String openid);

    int insert(TempFile tempFile);

    int update(TempFile tempFile);

    int deleteById(Long id);

    int deleteByIdForUsers(@Param("id") Long id,@Param("openid") String openid);
}

