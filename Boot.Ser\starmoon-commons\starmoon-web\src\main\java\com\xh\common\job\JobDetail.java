package com.xh.common.job;

import lombok.Data;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

@Data
public class JobDetail implements Delayed {
    private String jobName;
    private String jobGroupName;
    private Job job;
    private TimeUnit timeUnit;
    private int ttl;
    private long createTime;
    private long updateTime;
    private boolean next;

    public JobDetail(){
        this.createTime = System.currentTimeMillis();
    }

    @Override
    public long getDelay(TimeUnit unit) {
        return TimeUnit.MILLISECONDS.convert(this.ttl, timeUnit) + updateTime - System.currentTimeMillis();
    }

    @Override
    public int compareTo(Delayed delayed) {
        return (int) (this.getDelay(timeUnit) - delayed.getDelay(timeUnit));
    }

}
