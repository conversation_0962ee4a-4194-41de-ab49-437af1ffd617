const app = getApp();
var api = require('../../../utils/api.js');
var sender = require('../../../utils/sender.js');

Page({
  data: {
    orderId: '',
    order: {},
    loading: true
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail(options.id);
    }
  },
  
  // 加载订单详情
  loadOrderDetail: function (id) {
    wx.showLoading({
      title: '加载中...'
    });
    
    sender.requestUrl({
      url: api.api_order_detail,
      method: 'GET',
      params: { id: id }
    }, (data) => {
      wx.hideLoading();
      
      // 处理订单数据
      let order = data;
      
      // 解析地址JSON
      if (order.address && typeof order.address === 'string') {
        try {
          order.addressInfo = JSON.parse(order.address);
        } catch (e) {
          console.error('地址JSON解析失败:', e);
          order.addressInfo = {
            recipient: '未知',
            phone: '未知',
            province: '',
            city: '',
            county: '',
            detail: '地址信息解析失败'
          };
        }
      }
      
      // 设置状态文本
      const statusTexts = ['待付款', '待配送', '配送中', '已完成', '已取消'];
      order.statusText = statusTexts[order.status] || '未知状态';
      
      // 格式化时间
      if (order.createTime) {
        order.createTimeFormatted = this.formatTime(order.createTime);
      }
      
      this.setData({
        order: order,
        loading: false
      });
    }, (error) => {
      wx.hideLoading();
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({
        loading: false
      });
    });
  },
  
  // 格式化时间
  formatTime: function(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  },
  
  // 取消订单
  cancelOrder: function () {
    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });
          
          // 调用取消订单接口
          sender.requestUrl({
            url: api.api_order_cancel,
            method: 'POST',
            data: { id: this.data.orderId }
          }, (data) => {
            wx.hideLoading();
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            });
            
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }, (error) => {
            wx.hideLoading();
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },
  
  // 支付订单
  payOrder: function () {
    wx.showLoading({
      title: '处理中...'
    });
    
    // 调用支付接口
    sender.requestUrl({
      url: api.api_order_pay,
      method: 'POST',
      data: { id: this.data.orderId }
    }, (data) => {
      wx.hideLoading();
      
      // 调用微信支付
      if (data.payParams) {
        wx.requestPayment({
          ...data.payParams,
          success: (res) => {
            wx.showToast({
              title: '支付成功',
              icon: 'success'
            });
            
            // 刷新订单状态
            setTimeout(() => {
              this.loadOrderDetail(this.data.orderId);
            }, 1500);
          },
          fail: (err) => {
            wx.showToast({
              title: '支付失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '支付参数错误',
          icon: 'none'
        });
      }
    }, (error) => {
      wx.hideLoading();
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    });
  },
  
  // 修改地址
  modifyAddress: function() {
    if (this.data.order.status !== 0) {
      wx.showToast({
        title: '当前订单状态不支持修改地址',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到地址选择页面
    wx.navigateTo({
      url: '/pages/address/list/list?mode=select&fromOrderDetail=true',
      success: (res) => {
        // 通过eventChannel传递回调函数
        res.eventChannel.on('addressSelected', (data) => {
          this.updateOrderAddress(data.address);
        });
      }
    });
  },
  
  // 更新订单地址
  updateOrderAddress: function(address) {
    wx.showLoading({
      title: '更新中...'
    });
    
    sender.requestUrl({
      url: api.api_order_update_address,
      method: 'POST',
      data: {
        orderId: parseInt(this.data.orderId),
        addressId: parseInt(address.id)
      }
    }, (data) => {
      wx.hideLoading();
      wx.showToast({
        title: '地址修改成功',
        icon: 'success'
      });
      
      // 重新加载订单详情
      this.loadOrderDetail(this.data.orderId);
    }, (error) => {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '修改失败',
        icon: 'none'
      });
    });
  }
}) 