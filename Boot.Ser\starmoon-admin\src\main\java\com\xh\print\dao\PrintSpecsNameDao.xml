<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.print.dao.PrintSpecsNameDao">

    <resultMap type="com.xh.print.domain.entity.PrintSpecsName" id="PrintSpecsNameMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="specsName" column="specs_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PrintSpecsNameMap">
        select id,
               specs_name
        from print_specs_name
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="PrintSpecsNameMap">
        select
        id, specs_name
        from print_specs_name
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="specsName != null and specsName != ''">
                and specs_name = #{specsName}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from print_specs_name
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="specsName != null and specsName != ''">
                and specs_name = #{specsName}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into print_specs_name(specs_name)
        values (#{specsName})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into print_specs_name(specs_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.specsName})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into print_specs_name(specs_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.specsName})
        </foreach>
        on duplicate key update
        specs_name = values(specs_name)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update print_specs_name
        <set>
            <if test="specsName != null and specsName != ''">
                specs_name = #{specsName},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from print_specs_name
        where id = #{id}
    </delete>
    <delete id="delete">
        delete
        from print_specs_name
    </delete>

</mapper>

