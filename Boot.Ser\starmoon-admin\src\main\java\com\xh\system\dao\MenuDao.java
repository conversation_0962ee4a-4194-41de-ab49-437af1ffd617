package com.xh.system.dao;

import com.xh.system.domain.entity.Menu;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 菜单权限表(Menu)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 09:41:02
 */
public interface MenuDao {

    Menu queryById(Long id);

    List<Menu> queryByList(
            @Param("menuName") String menuName,
            @Param("parentId") Long parentId,
            @Param("roleId") Long roleId
    );

    int insert(Menu menu);

    int update(Menu menu);

    int deleteById(Long id);
}

