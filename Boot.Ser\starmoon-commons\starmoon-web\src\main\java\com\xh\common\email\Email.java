package com.xh.common.email;


import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;

import java.util.Properties;

public class Email {

    private String email;
    private String code;
    private Session session;

    public Email(){
        this.email = "<EMAIL>";
        this.code = "QJOTZIZCVUURKPNK";
        createSession();
    }

    public Email(String email, String code){
        this.email = email;
        this.code = code;
        createSession();
    }

    private void createSession() {
        Properties props = new Properties();
        props.put("mail.smtp.host", "smtp.163.com");
        props.put("mail.smtp.auth", "true");
        props.put("mail.transport.protocol", "smtp");
        Authenticator authenticator = new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(email,code);
            }
        };
        session = Session.getInstance(props,authenticator);
    }

    public void send(String subject,String content,String recipients) {
        try {
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(email));
            message.setRecipients(Message.RecipientType.TO,InternetAddress.parse(recipients));
            message.setSubject(subject);
            message.setText(content);

            Transport.send(message);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }
}
