package com.xh.common.web.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xh.common.web.statecode.StatusCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName ServerResponseEntity
 * <AUTHOR>
 * @Date 2025/4/5
 * @Version 1.0
 **/
@Data
public class ResponseEntity<T> {

    private String errorMessage;

    @Schema(description = "状态码（0：正常，1：失败）")
    private Integer code;

    private T data;

    public static <T> ResponseEntity<T> success() {
        ResponseEntity<T> responseEntity = new ResponseEntity<>();
        responseEntity.setCode(StatusCode.SUCCESS.getCode());
        return responseEntity;
    }

    public static <T> ResponseEntity<T> success(T data) {
        ResponseEntity<T> responseEntity = new ResponseEntity<>();
        responseEntity.setCode(StatusCode.SUCCESS.getCode());
        responseEntity.setData(data);
        return responseEntity;
    }

    public static <S,T> ResponseEntity<T> success(S source, Class<T> target){
        String s = JSON.toJSONString(source);
        T vo = JSONObject.parseObject(s, target);
        return success(vo);
    }

    public static <T> ResponseEntity<List<T>> success(List<?> sourceList, Class<T> target) {

        List<T> list = sourceList.stream()
                .map(source -> JSONObject.<T>parseObject(JSON.toJSONBytes(source), target))
                .collect(Collectors.toList());

        return success(list);
    }

    public static <T> ResponseEntity<PageInfo<T>> success(PageInfo<?> sourcePageInfo, Class<T> target) {

        List<T> list = sourcePageInfo.getList().stream()
                .map(source -> JSONObject.<T>parseObject(JSON.toJSONBytes(source), target))
                .collect(Collectors.toList());

        PageInfo<T> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(sourcePageInfo.getTotal());
        return success(pageInfo);
    }

    public static <T> ResponseEntity<T> error(String errorMessage) {
        ResponseEntity<T> responseEntity = new ResponseEntity<>();
        responseEntity.setCode(StatusCode.FAIL.getCode());
        responseEntity.setErrorMessage(errorMessage);
        return responseEntity;
    }

    public static <T> ResponseEntity<T> error(Integer code, String errorMessage) {
        ResponseEntity<T> responseEntity = new ResponseEntity<>();
        responseEntity.setCode(code);
        responseEntity.setErrorMessage(errorMessage);
        return responseEntity;
    }

    public static <T> ResponseEntity<T> error(StatusCode statusCode) {
        ResponseEntity<T> responseEntity = new ResponseEntity<>();
        responseEntity.setCode(statusCode.getCode());
        responseEntity.setErrorMessage(statusCode.getMessage());
        return responseEntity;
    }
}
