package com.xh.common.utils;

import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;

import java.io.*;

/**
 * <AUTHOR>
 */
public class FileUtils {

    public static byte[] fileToStream(File file) throws IOException {
        FileInputStream inputStream = new FileInputStream(file);
        return fileToStream(inputStream);
    }

    public static boolean isImageFile(byte[] fileBytes) throws UnsupportedEncodingException {

        if (fileBytes == null || fileBytes.length < 4) {
            return false;
        }

        // BMP: BM
        if (fileBytes[0] == 'B' && fileBytes[1] == 'M') {
            return true;
        }

        // JPEG: FF D8 FF E0 / FF D8 FF E1 / FF D8 FF ED ...
        if ((fileBytes[0] == (byte) 0xFF) && (fileBytes[1] == (byte) 0xD8)) {
            return true;
        }

        // PNG: 89 50 4E 47 (.PNG)
        if (fileBytes[0] == (byte) 0x89 &&
                fileBytes[1] == 'P' &&
                fileBytes[2] == 'N' &&
                fileBytes[3] == 'G') {
            return true;
        }

        // GIF: 47 49 46 38 ("GIF8")
        if (fileBytes[0] == 'G' &&
                fileBytes[1] == 'I' &&
                fileBytes[2] == 'F' &&
                fileBytes[3] == '8') {
            return true;
        }

        // TIFF: 49 49 2A 00 或者 4D 4D 00 2A
        if ((fileBytes[0] == 'I' && fileBytes[1] == 'I' &&
                fileBytes[2] == 0x2A && fileBytes[3] == 0x00) ||
                (fileBytes[0] == 'M' && fileBytes[1] == 'M' &&
                        fileBytes[2] == 0x00 && fileBytes[3] == 0x2A)) {
            return true;
        }

        // WebP: 52 49 46 46 xx xx xx xx 57 45 42 50
        if (fileBytes.length >= 12 &&
                fileBytes[0] == 'R' &&
                fileBytes[1] == 'I' &&
                fileBytes[2] == 'F' &&
                fileBytes[3] == 'F' &&
                fileBytes[8] == 'W' &&
                fileBytes[9] == 'E' &&
                fileBytes[10] == 'B' &&
                fileBytes[11] == 'P') {
            return true;
        }

        return false;
    }
    public static byte[] fileToStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int len = 0;
        byte[] b = new byte[1024];
        while ((len = inputStream.read(b)) != -1) {
            byteArrayOutputStream.write(b,0,len);
        }
        inputStream.close();
        return byteArrayOutputStream.toByteArray();
    }

    public static String fileToString(File file){
        try {
            FileInputStream fis = new FileInputStream(file);
            DataInputStream dis = new DataInputStream(fis);
            byte[] b = new byte[fis.available()];
            dis.readFully(b);
            dis.close();
            return new String(b);
        }catch (Exception e){
            throw new ServiceException("文件不存在");
        }
    }
}
