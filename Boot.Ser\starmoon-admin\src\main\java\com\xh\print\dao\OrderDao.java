package com.xh.print.dao;

import com.xh.print.domain.entity.Order;
import com.xh.print.domain.search.OrderSearch;

import java.util.List;

/**
 * 订单(Order)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:27
 */
public interface OrderDao {

    Order queryById(Long id);

    List<Order> queryByList(OrderSearch search);

    int insert(Order order);

    int update(Order order);

    int deleteById(Long id);

}

