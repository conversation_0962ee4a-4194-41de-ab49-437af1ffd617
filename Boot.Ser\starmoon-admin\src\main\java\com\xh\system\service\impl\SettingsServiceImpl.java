package com.xh.system.service.impl;

import com.xh.system.dao.SettingsDao;
import com.xh.system.entity.Settings;
import com.xh.system.service.SettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (Settings)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 21:25:46
 */
@Service
public class SettingsServiceImpl implements SettingsService {

    @Autowired
    private SettingsDao settingsDao;


    @Override
    public Settings queryById(Long id) {
        return settingsDao.queryById(id);
    }

    @Override
    public List<Settings> queryByList() {
        return settingsDao.queryByList();
    }

    @Override
    public Long insert(Settings settings) {
        settingsDao.insert(settings);
        return settings.getId();
    }

    @Override
    public boolean update(Settings settings) {
        return settingsDao.update(settings) > 0;
    }


    @Override
    public boolean deleteById(Long id) {
        return settingsDao.deleteById(id) > 0;
    }
}
