var api = require('../../utils/api.js')
var sender = require('../../utils/sender.js')
const app = getApp();

Page({
  data: {
    selectedFiles: [], // 选中的文件列表
    totalPrice: 0,
    selectedCount: 0,
    addresses: [], // 地址列表
    selectedAddress: null, // 选中的地址
    showAddressList: false // 是否显示地址列表
  },

  onLoad: function (options) {
    // 通过eventChannel接收数据
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('selectedFiles', (data) => {
      console.log('接收到选中文件数据:', data);
      this.setData({
        selectedFiles: data.files,
        totalPrice: data.totalPrice,
        selectedCount: data.selectedCount
      });
    });

    // 加载地址列表
    this.loadAddresses();
  },

  // 加载地址列表
  loadAddresses: function() {
    var that = this;
    sender.requestUrl({
      url: api.api_address_page,
      method: 'GET',
      params: {
        pageNum: 1,
        pageSize: 100 // 获取所有地址
      }
    }, function(data) {
      console.log('地址API返回数据:', data);
      
      // 检查数据结构，处理PageInfo分页数据
      let addressList = [];
      if (data && data.list && Array.isArray(data.list)) {
        // PageInfo分页数据结构
        addressList = data.list;
        console.log('使用PageInfo.list数据，共', addressList.length, '个地址');
      } else if (Array.isArray(data)) {
        // 直接数组结构
        addressList = data;
        console.log('使用直接数组数据，共', addressList.length, '个地址');
      } else {
        console.warn('地址数据结构异常:', data);
        addressList = [];
      }
      
      console.log('处理后的地址列表:', addressList);
      
      // 找到默认地址，处理Boolean和数字类型
      const defaultAddress = addressList.find(addr => {
        return addr.isDefault === true || addr.isDefault === 1 || addr.isDefault === '1';
      });
      
      // 设置数据
      that.setData({
        addresses: addressList,
        selectedAddress: defaultAddress || (addressList.length > 0 ? addressList[0] : null)
      });
      
      console.log('找到的默认地址:', defaultAddress);
      console.log('最终选中地址:', that.data.selectedAddress);
      
      // 如果没有地址，提示用户添加
      if (addressList.length === 0) {
        console.log('用户暂无收货地址');
      }
    }, function(error) {
      console.error('加载地址失败:', error);
      wx.showToast({
        title: '加载地址失败',
        icon: 'none'
      });
    });
  },

  // 选择地址
  selectAddress: function() {
    this.setData({
      showAddressList: true
    });
  },

  // 选择具体地址
  chooseAddress: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedAddress = this.data.addresses[index];
    
    this.setData({
      selectedAddress: selectedAddress,
      showAddressList: false
    });
  },

  // 关闭地址列表
  closeAddressList: function() {
    this.setData({
      showAddressList: false
    });
  },

  // 添加新地址
  addNewAddress: function() {
    this.setData({
      showAddressList: false
    });
    wx.navigateTo({
      url: '/pages/address/edit/edit'
    });
  },

  // 减少数量
  decreaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedFiles = this.data.selectedFiles;

    if (selectedFiles[index].quantity > 1) {
      selectedFiles[index].quantity--;
      // 重新计算价格
      this.calculateItemPrice(selectedFiles[index]);
      
      this.setData({
        selectedFiles: selectedFiles
      });
      this.calculateTotal();
    }
  },

  // 增加数量
  increaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedFiles = this.data.selectedFiles;

    selectedFiles[index].quantity++;
    // 重新计算价格
    this.calculateItemPrice(selectedFiles[index]);
    
    this.setData({
      selectedFiles: selectedFiles
    });
    this.calculateTotal();
  },

  // 计算单个文件价格
  calculateItemPrice: function(item) {
    if (item.unitPrice) {
      const pages = item.pageSize - item.pageNum + 1;
      item.price = (item.unitPrice * pages * item.quantity).toFixed(2);
    }
  },

  // 计算总价
  calculateTotal: function() {
    let totalPrice = 0;
    this.data.selectedFiles.forEach(file => {
      totalPrice += parseFloat(file.price || 0);
    });

    this.setData({
      totalPrice: totalPrice.toFixed(2)
    });
  },

  // 提交订单（支付）
  submitOrder: function() {
    if (!this.data.selectedAddress) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return;
    }

    if (this.data.selectedFiles.length === 0) {
      wx.showToast({
        title: '没有选中的文件',
        icon: 'none'
      });
      return;
    }

    const that = this;
    
    wx.showLoading({
      title: '正在提交订单...'
    });

    // 提取临时文件ID
    const tempFileIds = this.data.selectedFiles.map(file => file.id);

    sender.requestUrl({
      url: api.api_order_submit,
      method: 'POST',
      data: {
        tempFileIds: tempFileIds,
        addressId: this.data.selectedAddress.id
      }
    }, function(data) {
      wx.hideLoading();
      
      if (data.success) {
        console.log('订单提交成功，返回数据:', data);
        
        // 如果有支付参数，可以进行支付处理
        if (data.payParams) {
          console.log('支付参数:', data.payParams);
          // 这里可以调用微信支付或其他支付方式
          // wx.requestPayment(...) 
        }
        
        wx.showToast({
          title: '订单提交成功',
          icon: 'success',
          duration: 1500,
          success: function() {
            // 返回到打印列表页面并刷新
            const pages = getCurrentPages();
            if (pages.length >= 2) {
              const prevPage = pages[pages.length - 2];
              if (prevPage.route === 'pages/print/list/list') {
                prevPage.getTempFileList(); // 刷新文件列表
              }
            }
            wx.navigateBack({
              delta: 1
            });
          }
        });
      } else {
        wx.showModal({
          title: '提交失败',
          content: data.message || '订单提交失败，请重试',
          showCancel: false
        });
      }
    }, function(error) {
      wx.hideLoading();
      wx.showModal({
        title: '提交失败',
        content: '网络异常，请重试',
        showCancel: false
      });
    });
  }
}); 