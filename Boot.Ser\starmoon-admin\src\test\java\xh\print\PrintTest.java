package xh.print;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.printing.PDFPageable;
import org.apache.pdfbox.printing.PDFPrintable;
import org.apache.pdfbox.printing.Scaling;
import org.junit.jupiter.api.Test;

import javax.imageio.ImageIO;
import javax.print.PrintService;
import javax.print.attribute.Attribute;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.PrintServiceAttributeSet;
import javax.print.attribute.standard.*;
import javax.swing.*;
import javax.swing.BoxLayout;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.awt.print.*;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import org.apache.pdfbox.rendering.PDFRenderer;
import java.util.concurrent.atomic.AtomicInteger;
import javax.print.PrintServiceLookup;

public class PrintTest {
    // 将毫米转换为点
    private static double mmToPoints(double mm) {
        return mm * 72.0 / 25.4;
    }

    @Test
    public void print() throws IOException, PrinterException {
        File file = new File("C:\\Users\\<USER>\\Desktop\\2023英语中考.pdf");

        try {
            // 渲染所有页为图像
            PDDocument document = PDDocument.load(file);
            int pageCount = document.getNumberOfPages();
            List<BufferedImage> images = new ArrayList<>();
            PDFRenderer renderer = new PDFRenderer(document);
            for (int i = 0; i < pageCount; i++) {
                BufferedImage bufferedImage = renderer.renderImageWithDPI(i, 300);
                BufferedImage scaleImage = scaleImage(bufferedImage, (int) (210 * 300 / 25.4), (int) (297 * 300 / 25.4));
                images.add(scaleImage);
            }



            BufferedImage image = images.get(0);
            // 2. 创建打印服务
            PrinterJob printerJob = PrinterJob.getPrinterJob();
            printerJob.setJobName("Canon TS3400 series");
            PrintService service = printerJob.getPrintService();
            // 查询是否支持 MediaPrintableArea（决定是否能设置边距）

            Object defaultAttributeValue = service.getDefaultAttributeValue(MediaPrintableArea.class);


            // 3. 设置打印属性
            PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
            attributes.add(MediaSizeName.ISO_A4); // 设置为 A4 纸张
            attributes.add(Sides.ONE_SIDED);       // 单面打印
            attributes.add(new MediaPrintableArea(1, 1, 210,297, MediaPrintableArea.MM));

            printerJob.setPrintable(new Printable() {
                @Override
                public int print(Graphics graphics, PageFormat pageFormat, int pageIndex){
                    if (pageIndex > 0) {
                        return Printable.NO_SUCH_PAGE;
                    }

                    Graphics2D g2d = (Graphics2D) graphics;

                    Paper paper = pageFormat.getPaper();
                    // 整张纸张大小（pt）
                    double paperW = paper.getWidth();
                    double paperH = paper.getHeight();
                    // 可打印区域原点及大小（pt）
                    double ix = paper.getImageableX();
                    double iy = paper.getImageableY();
                    double iw = paper.getImageableWidth();
                    double ih = paper.getImageableHeight();
                    // 边距（pt），再转成 mm
                    double leftMm   = ix   * 25.4 / 300.0;
                    double topMm    = iy   * 25.4 / 300.0;
                    double rightMm  = (paperW  - ix - iw) * 25.4 / 300.0;
                    double bottomMm = (paperH  - iy - ih) * 25.4 / 300.0;


                    // 绘制图像
                    g2d.drawImage(image, (int)leftMm+1, (int)topMm, (int)iw-1, (int)ih, null);
                    return Printable.PAGE_EXISTS;
                }
            });
            // 4. 显示打印对话框让用户确认（可选）

            boolean printDialog = printerJob.printDialog(attributes);
            if (printDialog) {
                printerJob.print();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    // 打印预览测试（PDFRenderer + 页脚）
    @Test
    public void previewPrint() throws IOException {
        File file = new File("C:\\Users\\<USER>\\Desktop\\2023英语中考.pdf");
        // 渲染所有页为图像
        PDDocument document = PDDocument.load(file);
        int pageCount = document.getNumberOfPages();
        List<BufferedImage> images = new ArrayList<>();
        PDFRenderer renderer = new PDFRenderer(document);
        for (int i = 0; i < pageCount; i++) {
            BufferedImage bufferedImage = renderer.renderImageWithDPI(i, 300);
            BufferedImage scaleImage = scaleImage(bufferedImage, (int) (210 * 300 / 25.4), (int) (297 * 300 / 25.4));
            images.add(scaleImage);
        }
        PrinterJob printerJob = PrinterJob.getPrinterJob();
        printerJob.setJobName("Canon TS3400 series");

        PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
        attributes.add(MediaSizeName.ISO_A4); // 设置为 A4 纸张
        attributes.add(Sides.ONE_SIDED);       // 单面打印
        attributes.add(new MediaPrintableArea(1, 1, 210,297, MediaPrintableArea.MM));

//        PageFormat pf = printerJob.defaultPage();
        PageFormat pf = printerJob.getPageFormat(attributes);
        Paper paper = pf.getPaper();
// 整张纸张大小（pt）
        double paperW = paper.getWidth();
        double paperH = paper.getHeight();
// 可打印区域原点及大小（pt）
        double ix = paper.getImageableX();
        double iy = paper.getImageableY();
        double iw = paper.getImageableWidth();
        double ih = paper.getImageableHeight();
// 边距（pt），再转成 mm
        double leftMm   = ix   * 25.4 / 72.0;
        double topMm    = iy   * 25.4 / 72.0;
        double rightMm  = (paperW  - ix - iw) * 25.4 / 72.0;
        double bottomMm = (paperH  - iy - ih) * 25.4 / 72.0;

        System.out.printf(
                "边距（mm）: 左%.1f, 上%.1f, 右%.1f, 下%.1f%n",
                leftMm, topMm, rightMm, bottomMm
        );
        // 分页预览对话框
        JDialog dialog = new JDialog((Frame) null, "PDF 分页预览", true);
        dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        dialog.setLayout(new BorderLayout());
        JPanel control = new JPanel();
        JButton prevBtn = new JButton("上一页");
        JLabel pageLabel = new JLabel("第1页/" + pageCount);
        JButton nextBtn = new JButton("下一页");
        control.add(prevBtn);
        control.add(pageLabel);
        control.add(nextBtn);
        dialog.add(control, BorderLayout.NORTH);
        // 显示区域
        JLabel imageLabel = new JLabel();
        imageLabel.setHorizontalAlignment(SwingConstants.CENTER);
        JScrollPane scrollPane = new JScrollPane(imageLabel);
        dialog.add(scrollPane, BorderLayout.CENTER);
        // 缩放预览最大尺寸
        final int maxW = 600, maxH = 800;
        AtomicInteger idx = new AtomicInteger(0);
        Runnable update = () -> {
            // 获取原始图像
            BufferedImage orig = images.get(idx.get());
            // 按比例缩放到最大尺寸
            double wr = (double) maxW / orig.getWidth();
            double hr = (double) maxH / orig.getHeight();
            double scale = Math.min(wr, hr);
            int dw = (int) (orig.getWidth() * scale);
            int dh = (int) (orig.getHeight() * scale);
            Image scaled = orig.getScaledInstance(dw, dh, Image.SCALE_SMOOTH);

            // 创建新的 BufferedImage，用于绘制带边距的图像
            int finalWidth = dw + (int)((leftMm + rightMm)*72/25.4);
            int finalHeight = dh + (int)((topMm + bottomMm)*72/25.4);

            BufferedImage combined = new BufferedImage(finalWidth, finalHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = combined.createGraphics();


            // 填充背景颜色（可选，默认为透明或白色）
            g2d.setColor(Color.WHITE); // 或 Color.BLACK、Color.WHITE 等
            g2d.fillRect(0, 0, finalWidth, finalHeight);

            // 绘制缩放后的图像到指定边距的位置
            g2d.drawImage(scaled, (int)(leftMm * 72 /25.4), (int)(topMm * 72 /25.4), null);

            // 设置预览图像
            imageLabel.setIcon(new ImageIcon(combined));
            pageLabel.setText("第" + (idx.get() + 1) + "页/" + pageCount);
        };
        prevBtn.addActionListener(e -> {
            if (idx.get() > 0) {
                idx.decrementAndGet(); update.run();
            }
        });
        nextBtn.addActionListener(e -> {
            if (idx.get() < pageCount - 1) {
                idx.incrementAndGet(); update.run();
            }
        });
        update.run();
        dialog.setSize(maxW + 50, maxH + 100);
        dialog.setLocationRelativeTo(null);
        dialog.setVisible(true);
    }


    private BufferedImage scaleImage(BufferedImage image, int width, int height) {
        // 直接使用双线性插值缩放（速度快、质量不错）
        BufferedImage newImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = newImage.createGraphics();
        try {
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            double scaleX = (double) width / image.getWidth();
            double scaleY = (double) height / image.getHeight();
            AffineTransform transform = AffineTransform.getScaleInstance(scaleX, scaleY);
            AffineTransformOp op = new AffineTransformOp(transform, AffineTransformOp.TYPE_BILINEAR);
            newImage = op.filter(image, null);
        } finally {
            g2d.dispose();
        }
        return newImage;
    }

    @Test
    public void printBook() throws IOException, PrinterException {
        File file = new File("C:\\Users\\<USER>\\Desktop\\2023英语中考.pdf");

        try {
            // 2. 创建打印服务
            PrinterJob printerJob = PrinterJob.getPrinterJob();
            printerJob.setJobName("Canon TS3400 series");

            // 3. 设置打印属性
            PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
            attributes.add(MediaSizeName.ISO_A4); // 设置为 A4 纸张
            attributes.add(Sides.ONE_SIDED);       // 单面打印
            attributes.add(new MediaPrintableArea(1, 1, 210,297, MediaPrintableArea.MM));
            PageRanges pageRanges = new PageRanges(2, 5); // 2~5页
            attributes.add(pageRanges);

            Book book = new Book();
            PageFormat pageFormat = printerJob.getPageFormat(attributes);
            // 渲染所有页为图像
            PDDocument document = PDDocument.load(file);
            int pageCount = document.getNumberOfPages();
            List<BufferedImage> images = new ArrayList<>();
            PDFRenderer renderer = new PDFRenderer(document);
            for (int i = 0; i < pageCount; i++) {
                BufferedImage bufferedImage = renderer.renderImageWithDPI(i, 300);
                BufferedImage scaleImage = scaleImage(bufferedImage, (int) (210 * 300 / 25.4), (int) (297 * 300 / 25.4));
                images.add(scaleImage);
                book.append(new ImagePrintable(scaleImage),pageFormat);
            }


            printerJob.setPageable( book);
            // 4. 显示打印对话框让用户确认（可选）

            boolean printDialog = printerJob.printDialog(attributes);
            if (printDialog) {
                printerJob.print();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testPrintMargins() {
        PrinterJob printerJob = PrinterJob.getPrinterJob();
        printerJob.setJobName("Canon TS3400 series");

        PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
        attributes.add(MediaSizeName.ISO_A4); // 设置为 A4 纸张
        attributes.add(Sides.ONE_SIDED);       // 单面打印
        attributes.add(new MediaPrintableArea(1, 1, 210,297, MediaPrintableArea.MM));

//        PageFormat pf = printerJob.defaultPage();
        PageFormat pf = printerJob.getPageFormat(attributes);
        Paper paper = pf.getPaper();
// 整张纸张大小（pt）
        double paperW = paper.getWidth();
        double paperH = paper.getHeight();
// 可打印区域原点及大小（pt）
        double ix = paper.getImageableX();
        double iy = paper.getImageableY();
        double iw = paper.getImageableWidth();
        double ih = paper.getImageableHeight();
// 边距（pt），再转成 mm
        double leftMm   = ix   * 25.4 / 72.0;
        double topMm    = iy   * 25.4 / 72.0;
        double rightMm  = (paperW  - ix - iw) * 25.4 / 72.0;
        double bottomMm = (paperH  - iy - ih) * 25.4 / 72.0;

        System.out.printf(
                "边距（mm）: 左%.1f, 上%.1f, 右%.1f, 下%.1f%n",
                leftMm, topMm, rightMm, bottomMm
        );
    }
}
