package com.xh.system.controller.app;

import com.xh.common.web.domain.ResponseEntity;
import com.xh.system.domain.entity.Dict;
import com.xh.system.service.DictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "用户端")
@RestController
@RequestMapping("users-api/v1/dict")
public class MemberDictController {

    @Autowired
    private DictService dictService;

    @Operation(summary = "根据代码查询字典")
    @GetMapping("/search")
    public ResponseEntity<List<Dict>> queryDict(@Parameter(name = "nameEn", description = "字典代码", required = true) String nameEn) {
        return ResponseEntity.success(dictService.queryDictByNameEn(nameEn));
    }

}
