package com.xh.print.dao;

import com.xh.print.domain.entity.OrderDetail;
import com.xh.print.domain.search.OrderDetailSearch;

import java.util.List;

/**
 * 订单明细(OrderDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:51
 */
public interface OrderDetailDao {

    OrderDetail queryById(Long id);

    List<OrderDetail> queryByList(OrderDetailSearch orderDetailSearch);

    int insert(OrderDetail orderDetail);

    int update(OrderDetail orderDetail);

    int deleteById(Long id);

}

