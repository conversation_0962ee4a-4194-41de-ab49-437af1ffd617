package com.xh.system.dao;

import com.xh.system.domain.entity.Role;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 角色信息表(Role)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 09:48:26
 */
public interface RoleDao {

    Role queryById(Long id);

    List<Role> queryByList(@Param("roleName") String roleName,@Param("status") Integer status);

    int insert(Role role);

    int update(Role role);

    int deleteById(Long id);

}

