Component({
  properties: {
    title: {
      type: String,
      value: '导航栏'
    },
    background: {
      type: String,
      value: '#f0f0f0'
    },
    color: {
      type: String,
      value: '#333'
    },
    showBack: {
      type: Boolean,
      value: false
    },
    showRight: {
      type: Boolean,
      value: false
    },
    rightText: {
      type: String,
      value: ''
    }
  },
  
  data: {
    statusBarHeight: 20,
    navHeight: 44
  },
  
  lifetimes: {
    attached: function() {
      let systemInfo = wx.getSystemInfoSync()
      let buttonInfo = wx.getMenuButtonBoundingClientRect()

      // 状态栏的高度
      let rpxStatusHeight = systemInfo.statusBarHeight
      // 导航栏的高度
      let rpxNavHeight = buttonInfo.height + (buttonInfo.top - rpxStatusHeight + 2) * 2

      this.setData({
        statusBarHeight: rpxStatusHeight,
        navBarHeight: rpxNavHeight
      });


    }
  },
  
  methods: {
    goBack() {
      wx.navigateBack({
        delta: 1
      });
    },
    onRightTap() {
      this.triggerEvent('righttap');
    }
  }
}) 