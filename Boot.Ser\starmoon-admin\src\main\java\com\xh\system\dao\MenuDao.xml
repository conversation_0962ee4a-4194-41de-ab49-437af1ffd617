<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.MenuDao">

    <resultMap type="com.xh.system.domain.entity.Menu" id="MenuMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="menuType" column="menu_type" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="componentPath" column="component_path" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="MenuMap">
        select
          *
        from sys_menu
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="MenuMap">
        select
            a.*
        from sys_menu a LEFT JOIN sys_role_menu b ON a.id = b.menu_id
        <where>
            <if test="menuName != null and '' != menuName">
                and a.menu_name like CONCAT('%',#{menuName},'%')
            </if>
            <if test="parentId != null">
                and a.parent_id = #{parentId}
            </if>
            <if test="roleId != null">
                and b.role_id = #{roleId}
            </if>
        </where>
        group by a.id
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="menuName !=null and '' != menuName">
                menu_name,
            </if>
            <if test="parentId !=null">
                parent_id,
            </if>
            <if test="sort !=null">
                sort,
            </if>
            <if test="path !=null and '' != path">
                path,
            </if>
            <if test="componentPath !=null and '' != componentPath">
                component_path,
            </if>
            <if test="menuType !=null and '' != menuType">
                menu_type,
            </if>
            create_time,
            <if test="note !=null and '' != note">
                note,
            </if>
            <if test="icon !=null and '' != icon">
                icon,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="menuName !=null and '' != menuName">
                #{menuName},
            </if>
            <if test="parentId !=null">
                #{parentId},
            </if>
            <if test="sort !=null">
                #{sort},
            </if>
            <if test="path !=null and '' != path">
                #{path},
            </if>
            <if test="componentPath !=null and '' != componentPath">
                #{componentPath},
            </if>
            <if test="menuType !=null and '' != menuType">
                #{menuType},
            </if>
            now(),
            <if test="note !=null and '' != note">
                #{note},
            </if>
            <if test="icon !=null and '' != icon">
                #{icon},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_menu
        <set>
            <if test="menuName != null and menuName != ''">
                menu_name = #{menuName},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="path != null and path != ''">
                path = #{path},
            </if>
            <if test="componentPath != null and componentPath != ''">
                component_path = #{componentPath},
            </if>
            <if test="menuType != null and menuType != ''">
                menu_type = #{menuType},
            </if>
            <if test="icon != null and icon != ''">
                icon = #{icon},
            </if>
            <if test="note != null and note != ''">
                note = #{note},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from sys_menu where id = #{id}
    </delete>
</mapper>

