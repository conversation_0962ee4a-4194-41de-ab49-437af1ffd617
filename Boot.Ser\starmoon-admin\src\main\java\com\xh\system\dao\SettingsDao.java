package com.xh.system.dao;

import com.xh.system.entity.Settings;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * (Settings)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 21:25:45
 */
public interface SettingsDao {

    Settings queryById(Long id);

    List<Settings> queryByList();

    int insert(Settings settings);

    int update(Settings settings);

    int deleteById(Long id);

}

