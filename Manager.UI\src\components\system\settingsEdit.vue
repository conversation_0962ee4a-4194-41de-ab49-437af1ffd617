<template>
  <el-dialog width="30%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
    <el-form :model="settingsModel" label-width="100px">
      <el-form-item label="键">
        <el-input v-model="settingsModel.settingsKey" :disabled="settingsModel.id !== 0" placeholder="请输入设置键" />
      </el-form-item>
      <el-form-item label="值">
        <el-input v-model="settingsModel.settingValue" placeholder="请输入设置值" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="settingsModel.note" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addSettings, editSettings } from '@/api/system/systemSettings'
import mitt from '@/utils/mitt'
export default {
  name: 'settingsEdit',
  data() {
    return {
      loading: false,
      settingsModel: { id: 0, settingsKey: '', settingValue: '', note: '' },
      dialog: {
        show: false,
        title: ''
      }
    }
  },
  methods: {
    onSubmit() {
      this.loading = true
      const operation = this.settingsModel.id === 0 ? addSettings : editSettings
      operation(this.settingsModel)
        .then(() => {
          this.$message.success('操作成功')
          this.dialog.show = false
          this.$emit('search')
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || err.message)
        })
        .finally(() => {
          this.loading = false
        })
    }
  },
  mounted() {
    this.$nextTick(() => {
      mitt.on('openSettingsAdd', () => {
        this.settingsModel = { id: 0, settingsKey: '', settingValue: '', note: '' }
        this.dialog.title = '添加设置'
        this.dialog.show = true
      })
      mitt.on('openSettingsEdit', (data) => {
        this.settingsModel = { ...data }
        this.dialog.title = '编辑设置'
        this.dialog.show = true
      })
    })
  },
  unmounted() {
    mitt.off('openSettingsAdd')
    mitt.off('openSettingsEdit')
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style> 