import { createApp } from 'vue'
import App from './App.vue'

import router from '@/router/index'
import store from '@/store/index'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入全局样式
import '@/assets/styles/global.css'

const app = createApp(App)

// 设置全局配置，关闭警告信息
app.config.warnHandler = () => {};

app.use(router)
app.use(store)
app.use(ElementPlus)

app.mount('#app')
