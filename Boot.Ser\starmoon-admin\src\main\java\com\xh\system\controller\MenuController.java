package com.xh.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.MenuCreateDTO;
import com.xh.system.domain.dto.MenuDTO;
import com.xh.system.domain.dto.MenuEditDTO;
import com.xh.system.domain.entity.Menu;
import com.xh.system.domain.vo.MenuVO;
import com.xh.system.service.MenuService;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.security.user.UserDetail;
import com.xh.common.utils.SecurityUtils;
import com.xh.common.web.domain.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单权限表(Menu)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-20 09:40:59
 */
@Tag(name = "管理端，系统模块")
@RestController
@RequestMapping("manage-api/v1/menu")
public class MenuController {

    @Autowired
    private MenuService menuService;

    @Operation(summary = "加载菜单")
    @GetMapping("load-menu")
    @PreAuthorize("hasAuthority('sys:menu:query')")
    public ResponseEntity<List<MenuVO>> menu() {
        UserDetail userDetail = SecurityUtils.currentPrincipal(UserDetail.class);
        List<Menu> menuList = menuService.queryByList(null, null, userDetail.getRoleId());
        return ResponseEntity.success(menuList, MenuVO.class);
    }

    @Operation(summary = "通过ID查询菜单")
    @GetMapping
    @PreAuthorize("hasAuthority('sys:menu:query')")
    public ResponseEntity<Menu> queryById(Long id) {
        return ResponseEntity.success(menuService.queryById(id));
    }

    @Operation(summary = "通过分页查询菜单")
    @GetMapping("page")
    @PreAuthorize("hasAuthority('sys:menu:query')")
    public ResponseEntity<PageInfo<MenuDTO>> queryByPage(@RequestParam(value = "pageNum",required = false,defaultValue = "1") Integer pageNum,
                                  @RequestParam(value = "pageSize",required = false, defaultValue = "10") Integer pageSize,
                                  @RequestParam(value = "menuName",required = false) String menuName,
                                  @RequestParam(value = "roleId",required = false) Long roleId
    ) {
        PageHelper.startPage(pageNum,pageSize);
        PageHelper.orderBy("id asc");
        PageInfo<MenuDTO> pageInfo = menuService.queryByPage(menuName,roleId);
        return ResponseEntity.success(pageInfo, MenuDTO.class);
    }

    @Operation(summary = "添加菜单")
    @PostMapping
    @PreAuthorize("hasAuthority('sys:menu:add')")
    @OperationLog(title = "添加菜单")
    public ResponseEntity<Long> insert(@Valid @RequestBody MenuCreateDTO dto) {
        return ResponseEntity.success(menuService.insert(dto));
    }

    @Operation(summary = "修改菜单")
    @PutMapping
    @PreAuthorize("hasAuthority('sys:menu:edit')")
    @OperationLog(title = "修改菜单")
    public ResponseEntity<Boolean> update(@Valid @RequestBody MenuEditDTO dto) {
        return ResponseEntity.success(menuService.update(dto));
    }

    @Operation(summary = "删除菜单")
    @DeleteMapping
    @PreAuthorize("hasAuthority('sys:menu:delete')")
    @OperationLog(title = "删除菜单")
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(menuService.deleteById(id));
    }

}