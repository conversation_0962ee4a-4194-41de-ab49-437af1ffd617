package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ImagetextEditDTO {

    @Schema(description = "标题")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "标题")
    @NotBlank(message = "标题不能为空")
    private String title;

    @Schema(description = "配图")
    private String imageUrl;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "跳转连接")
    private String link;

    @Schema(description = "排序")
    private Long sort;

    @NotNull(message = "标题不能为空")
    @Schema(description = "类型（banner,notice,about_us,privacy_policy,text）")
    private String type;

}
