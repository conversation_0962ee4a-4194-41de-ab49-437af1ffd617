package com.xh.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.system.dao.DictDao;
import com.xh.system.domain.dto.DictCreateDTO;
import com.xh.system.domain.dto.DictDTO;
import com.xh.system.domain.dto.DictEditDTO;
import com.xh.system.domain.entity.Dict;
import com.xh.system.service.DictService;
import com.xh.common.web.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * (Dict)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 13:11:11
 */
@Service
public class DictServiceImpl implements DictService{
    @Autowired
    private DictDao dictDao;

    @Override
    public Dict queryById(Long id) {
        return dictDao.queryById(id);
    }

    @Override
    public List<Dict> queryByList(String nameEn, String nameCn, Integer status, Long parentId) {
        return dictDao.queryByList(nameEn,nameCn,status,parentId);
    }

    @Override
    public PageInfo<DictDTO> queryByPage(String nameEn, String nameCn, Integer status, Long parentId) {
        List<Dict> rootList = dictDao.queryByList(nameEn, nameCn, status, 0L);
        PageInfo<Dict> pageInfo = new PageInfo<>(rootList);

        List<Dict> dictList = dictDao.queryByList(null, null, status, null);
        List<DictDTO> collect = rootList.stream().map(dict -> {
            DictDTO dto = new DictDTO();
            BeanUtils.copyProperties(dict, dto);
            dto.setChildren(getChildren(dict.getId(), dictList));
            return dto;
        }).collect(Collectors.toList());

        PageInfo<DictDTO> info = new PageInfo<>(collect);
        info.setTotal(pageInfo.getTotal());
        return info;
    }

    @Override
    public Long insert(DictCreateDTO dto) {
        if(checkUserNameExist(dto.getNameEn(),dto.getParentId())){
            throw new ServiceException("字典已存在");
        }
        Dict dict = new Dict();
        BeanUtils.copyProperties(dto,dict);
        dictDao.insert(dict);
        return dict.getId();
    }

    @Override
    public boolean update(DictEditDTO dto) {
        Dict dict = new Dict();
        BeanUtils.copyProperties(dto,dict);
        return dictDao.update(dict) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return dictDao.deleteById(id) > 0;
    }

    @Override
    public List<Dict> queryDictByNameEn(String nameEn) {
        PageHelper.orderBy("sort asc");
        return dictDao.queryDictByNameEn(nameEn);
    }

    private boolean checkUserNameExist(String nameEn,Long parentId) {
        List<Dict> dictList = dictDao.queryByList(nameEn,null,null,parentId);
        if(dictList.size()>0){
            return true;
        }
        return false;
    }

    private List<DictDTO> getChildren(Long parentId, List<Dict> dictList) {
        List<Dict> children = dictList.stream().filter(temp -> temp.getParentId().intValue() == parentId.intValue()).toList();

        return children.stream().map(item ->{
            DictDTO dto = new DictDTO();
            BeanUtils.copyProperties(item,dto);
            dto.setChildren(getChildren(item.getId(),dictList));
            return dto;
        }).collect(Collectors.toList());
    }
}
