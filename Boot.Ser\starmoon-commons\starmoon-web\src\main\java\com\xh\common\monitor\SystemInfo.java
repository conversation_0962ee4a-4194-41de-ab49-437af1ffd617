package com.xh.common.monitor;

import lombok.Data;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.util.HashMap;
import java.util.Map;

@Data
public class SystemInfo {
    //系统名称
    private String systemName;
    //系统架构
    private String systemArch;
    //cpu使用率
    private double cpuUsage;
    //cpu核心数量
    private int cpuCoreNumber;
    //系统内存
    private MemoryInfo systemMemory;
    //磁盘容量
    private Map<String,MemoryInfo> diskMemory;

    public static SystemInfo create(){
        SystemInfo systemInfo = new SystemInfo();

        com.sun.management.OperatingSystemMXBean os  = (com.sun.management.OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        String osName = os.getName();
        String osArch = os.getArch();
        int coreNumber = os.getAvailableProcessors();
        systemInfo.setSystemName(osName);
        systemInfo.setSystemArch(osArch);
        systemInfo.setCpuCoreNumber(coreNumber);

        long memorySize = os.getTotalPhysicalMemorySize();
        long usableSize = os.getFreePhysicalMemorySize();

        MemoryInfo systemMemory = new MemoryInfo(0,memorySize - usableSize,memorySize,memorySize,MemoryInfo.UNIT_GB);
        systemInfo.setSystemMemory(systemMemory);

        File[] disks = File.listRoots();
        Map<String,MemoryInfo> map = new HashMap<>();
        for (File file : disks) {
            long total = file.getTotalSpace();
            long usable = file.getUsableSpace();
            long used = total - usable;
            MemoryInfo memoryInfo = new MemoryInfo(0,used,total,total,MemoryInfo.UNIT_GB);
            map.put(file.getPath(),memoryInfo);
        }
        systemInfo.setDiskMemory(map);
        systemInfo.setCpuUsage(os.getSystemCpuLoad()*100);

        return systemInfo;
    }
}
