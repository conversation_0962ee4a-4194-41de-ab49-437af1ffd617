<template>
  <el-dialog title="系统设置" :visible.sync="visible" width="500px" @close="onCancel">
    <el-form :model="form" label-width="100px">
      <el-form-item label="键">
        <el-input v-model="form.settingsKey" :disabled="isEdit" placeholder="请输入设置键" />
      </el-form-item>
      <el-form-item label="值">
        <el-input v-model="form.settingValue" placeholder="请输入设置值" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.note" placeholder="备注" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSettings, addSettings, editSettings } from '@/api/system/systemSettings'
export default {
  name: 'SystemSettingsEdit',
  props: {
    visible: Boolean,
    id: [Number, null]
  },
  data() {
    return {
      form: { id: null, settingsKey: '', settingValue: '', note: '' },
      isEdit: false
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadData()
      }
    }
  },
  methods: {
    async loadData() {
      if (this.id) {
        this.isEdit = true
        const res = await getSettings(this.id)
        if (res.code === 0) {
          this.form = res.data
        } else {
          this.$message.error(res.errorMessage || res.message)
        }
      } else {
        this.isEdit = false
        this.form = { id: null, settingsKey: '', settingValue: '', note: '' }
      }
    },
    onCancel() {
      this.$emit('update:visible', false)
    },
    async onSubmit() {
      try {
        let res
        if (this.isEdit) {
          res = await editSettings(this.form)
        } else {
          res = await addSettings(this.form)
        }
        if (res.code === 0) {
          this.$message.success('保存成功')
          this.$emit('update:visible', false)
          this.$emit('saved')
        } else {
          this.$message.error(res.errorMessage || res.message)
        }
      } catch (err) {
        this.$message.error(err.errorMessage || err.message)
      }
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style> 