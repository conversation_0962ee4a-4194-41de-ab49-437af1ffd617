package com.xh.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.security.user.UserDetail;
import com.xh.common.utils.SecurityUtils;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.system.domain.dto.PasswordEditDTO;
import com.xh.system.domain.dto.UserCreateDTO;
import com.xh.system.domain.dto.UserEditDTO;
import com.xh.system.domain.entity.User;
import com.xh.system.domain.vo.UserVO;
import com.xh.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 用户信息表(User)表控制层
 * 用户注册在认证中心
 * <AUTHOR>
 * @since 2025-05-20 09:50:27
 */
@Tag(name = "管理端，系统模块")
@RestController
@RequestMapping("manage-api/v1/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Operation(summary = "获取当前用户",security = { @SecurityRequirement(name = "Authorization") })
    @GetMapping("current")
    public ResponseEntity<UserDetail> user() {
        return ResponseEntity.success(SecurityUtils.currentPrincipal(UserDetail.class));
    }

    @Operation(summary = "通过ID查询用户")
    @Parameter(name = "id", description = "用户ID",required = true)
    @GetMapping
    @PreAuthorize("hasAuthority('sys:user:query')")
    public ResponseEntity<UserVO> queryById(Long id) {
        return ResponseEntity.success(userService.queryById(id), UserVO.class);
    }

    @Operation(summary = "通过分页查询用户")
    @Parameters(value = {
            @Parameter(name = "pageNum", description = "页码"),
            @Parameter(name = "pageSize", description = "每页数量"),
            @Parameter(name = "keyword", description = "搜索关键字(用户名|昵称)"),
            @Parameter(name = "status", description = "状态"),
            @Parameter(name = "phone", description = "手机号"),
            @Parameter(name = "orgId", description = "组织ID")
    })
    @PreAuthorize("hasAuthority('sys:user:query')")
    @GetMapping("page")
    public ResponseEntity<PageInfo<UserVO>> queryByPage(
                    @RequestParam(value = "pageNum",required = false,defaultValue = "1") Integer pageNum,
                    @RequestParam(value = "pageSize",required = false, defaultValue = "10") Integer pageSize,
                    @RequestParam(value = "keyword",required = false) String keyword,
                    @RequestParam(value = "status",required = false) Integer status,
                    @RequestParam(value = "phone",required = false) String phone,
                    @RequestParam(value = "orgId",required = false) Long orgId) {
        PageHelper.startPage(pageNum,pageSize);
        PageHelper.orderBy("id desc");
        PageInfo<User> pageInfo = userService.queryByPage(keyword,status,orgId,phone);
        return ResponseEntity.success(pageInfo,UserVO.class);
    }

  
    @Operation(summary = "添加用户")
    @OperationLog(title = "添加用户")
    @PreAuthorize("hasAuthority('sys:user:add')")
    @PostMapping
    public ResponseEntity<Long> add(@Valid @RequestBody UserCreateDTO dto) {
        return ResponseEntity.success(userService.insert(dto));
    }

    @Operation(summary = "修改用户密码")
    @OperationLog(title = "修改用户密码")
    @PreAuthorize("hasAuthority('sys:user:edit:password')")
    @PutMapping("password")
    public ResponseEntity<Boolean> updatePassword(@Valid @RequestBody PasswordEditDTO passwordEditDTO) {
        return ResponseEntity.success(userService.updatePassword(passwordEditDTO));
    }

    @Operation(summary = "编辑用户")
    @OperationLog(title = "编辑用户")
    @PreAuthorize("hasAuthority('sys:user:edit')")
    @PutMapping
    public ResponseEntity<Boolean> edit(@Valid @RequestBody UserEditDTO dto) {
        return ResponseEntity.success(userService.update(dto));
    }

    @Operation(summary = "删除用户")
    @Parameter(name = "id", description = "用户ID",required = true)
    @OperationLog(title = "删除用户")
    @PreAuthorize("hasAuthority('sys:user:delete')")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(userService.deleteById(id));
    }

}