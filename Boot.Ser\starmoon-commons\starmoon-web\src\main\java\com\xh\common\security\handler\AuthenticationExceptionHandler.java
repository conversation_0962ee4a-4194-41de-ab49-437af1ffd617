package com.xh.common.security.handler;

import com.alibaba.fastjson.JSON;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.common.web.statecode.StatusCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class AuthenticationExceptionHandler implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException{
        response.setHeader("Content-Type","application/json;charset=utf-8");
        response.getWriter().println(JSON.toJSONString(ResponseEntity.error(StatusCode.UNAUTHORIZED)));
    }

}
