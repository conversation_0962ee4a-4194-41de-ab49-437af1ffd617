/* 价格表列表样式 */

.price-item {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.price-header {
  display: flex;
  flex-direction: row;
  margin-bottom: 15px;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.price-title-area {
  flex: 1;
  padding-right: 15px;
}

.price-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.price-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.price-item-left {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 8px;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.price-image {
  width: 100%;
  height: 100%;
}

/* 效果图按钮 */
.effect-btn {
  display: inline-block;
  background-color: transparent;
  color: #ff6633;
  font-size: 13px;
  padding: 4px 10px;
  border-radius: 20px;
  text-align: center;
  border: 1px solid #ff6633;
  flex-shrink: 0;
  margin-top: 3px;
}

/* 富文本内容区域 */
.price-content {
  width: 100%;
}

.price-content rich-text {
  width: 100%;
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

/* 富文本中的图片样式 */
.price-content rich-text image {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  margin: 10px 0;
  border-radius: 5px;
  display: block;
}

/* 空数据提示 */
.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
  color: #999;
  font-size: 14px;
}