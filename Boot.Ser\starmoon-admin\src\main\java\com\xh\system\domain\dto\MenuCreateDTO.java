package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class MenuCreateDTO {

    @Schema(description = "菜单名")
    @NotBlank(message = "菜单名不能为空")
    private String menuName;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "菜单类型")
    private String menuType;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "菜单路径")
    private String path;

    @Schema(description = "组件路径")
    private String componentPath;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "扩展数据")
    private String expandData;

}
