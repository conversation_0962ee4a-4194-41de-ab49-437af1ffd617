package com.xh.print.controller.app;

import com.xh.common.web.domain.ResponseEntity;
import com.xh.print.domain.entity.Order;
import com.xh.print.domain.form.OrderSubmitForm;
import com.xh.print.domain.form.PrintFile;
import com.xh.print.domain.search.OrderSearch;
import com.xh.print.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import com.xh.print.domain.dto.OrderDTO;

/**
 * 订单(Order)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:27
 */
@Tag(name = "用户端，打印模块")
@RestController
@RequestMapping("users-api/v1/order")
public class UsersOrderController {

    @Autowired
    private OrderService orderService;


    @Operation(summary = "分页查询订单")
    @GetMapping("page")
    public ResponseEntity<List<OrderDTO>> queryByPage(OrderSearch search) {
        return ResponseEntity.success(orderService.queryByList(search));
    }

    @Operation(summary = "查询订单详情")
    @GetMapping("detail")
    public ResponseEntity<Object> queryDetailById(@RequestParam Long id) {
        return ResponseEntity.success(orderService.queryDetailById(id));
    }

    @Operation(summary = "取消订单")
    @PostMapping("cancel")
    public ResponseEntity<Boolean> cancelOrder(@RequestBody Map<String, Object> params) {
        Object orderIdObj = params.get("id");
        if (orderIdObj == null) {
            return ResponseEntity.error("订单ID不能为空");
        }

        Long orderId = null;
        try {
            // 处理orderId的类型转换
            if (orderIdObj instanceof Number) {
                orderId = ((Number) orderIdObj).longValue();
            } else if (orderIdObj instanceof String) {
                orderId = Long.parseLong((String) orderIdObj);
            }
        } catch (NumberFormatException e) {
            return ResponseEntity.error("订单ID格式不正确");
        }

        if (orderId == null) {
            return ResponseEntity.error("订单ID不能为空");
        }

        // 这里可以添加取消订单的业务逻辑
        // 比如检查订单状态、更新订单状态等
        boolean result = orderService.cancelOrder(orderId);
        return ResponseEntity.success(result);
    }

    @Operation(summary = "订单支付")
    @PostMapping("pay")
    public ResponseEntity<Map<String, Object>> payOrder(@RequestBody Map<String, Object> params) {
        Object orderIdObj = params.get("id");
        if (orderIdObj == null) {
            return ResponseEntity.error("订单ID不能为空");
        }

        Long orderId = null;
        try {
            // 处理orderId的类型转换
            if (orderIdObj instanceof Number) {
                orderId = ((Number) orderIdObj).longValue();
            } else if (orderIdObj instanceof String) {
                orderId = Long.parseLong((String) orderIdObj);
            }
        } catch (NumberFormatException e) {
            return ResponseEntity.error("订单ID格式不正确");
        }

        if (orderId == null) {
            return ResponseEntity.error("订单ID不能为空");
        }

        Map<String, Object> result = orderService.payOrder(orderId);
        return ResponseEntity.success(result);
    }

    @Operation(summary = "修改订单地址")
    @PostMapping("updateAddress")
    public ResponseEntity<Boolean> updateOrderAddress(@RequestBody Map<String, Object> params) {
        // 处理参数类型转换
        Object orderIdObj = params.get("orderId");
        Object addressIdObj = params.get("addressId");
        
        if (orderIdObj == null || addressIdObj == null) {
            return ResponseEntity.error("订单ID和地址ID不能为空");
        }
        
        Long orderId = null;
        Long addressId = null;
        
        try {
            // 处理orderId的类型转换
            if (orderIdObj instanceof Number) {
                orderId = ((Number) orderIdObj).longValue();
            } else if (orderIdObj instanceof String) {
                orderId = Long.parseLong((String) orderIdObj);
            }
            
            // 处理addressId的类型转换
            if (addressIdObj instanceof Number) {
                addressId = ((Number) addressIdObj).longValue();
            } else if (addressIdObj instanceof String) {
                addressId = Long.parseLong((String) addressIdObj);
            }
        } catch (NumberFormatException e) {
            return ResponseEntity.error("参数格式不正确");
        }
        
        if (orderId == null || addressId == null) {
            return ResponseEntity.error("订单ID和地址ID不能为空");
        }
        
        boolean result = orderService.updateOrderAddress(orderId, addressId);
        return ResponseEntity.success(result);
    }

    @Operation(summary = "用户上传打印文件")
    @PostMapping("detail")
    public ResponseEntity<Long> uploadFile(@Valid @RequestBody PrintFile printFile) {
        return ResponseEntity.success(orderService.uploadFile(printFile));
    }

    @Operation(summary = "提交订单")
    @PostMapping("submit")
    public ResponseEntity<Map<String,Object>> submit(@Valid @RequestBody OrderSubmitForm form) {
        return ResponseEntity.success(orderService.submitOrder(form));
    }

    @Operation(summary = "修改订单")
    @PutMapping
    public ResponseEntity<Boolean> update(Order order) {
        return ResponseEntity.success(orderService.update(order));
    }


    @Operation(summary = "删除订单")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(orderService.deleteById(id));
    }

}

