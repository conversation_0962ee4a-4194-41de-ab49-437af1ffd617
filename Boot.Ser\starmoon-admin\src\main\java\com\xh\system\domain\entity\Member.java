package com.xh.system.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 客户端用户表(Member)实体类
 *
 * <AUTHOR>
 * @since 2025-05-21 20:29:36
 */
@Data
public class Member implements Serializable {
    private static final long serialVersionUID = 482888349479853455L;

    @Schema(description = "微信用户ID")
    private Long id;

    @Schema(description = "微信小程序用户openid")
    private String openid;

    @Schema(description = "微信用户统一ID")
    private String unionid;

    @JsonFormat(pattern = "yyyy-MM-dd",  timezone = "GMT+8")
    @Schema(description = "生日")
    private Date birthday;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "头像")
    private String avatarUrl;

    @Schema(description = "性别(man：男；woman：女)")
    private String gender;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "住户id")
    private Long residentId;

    @Schema(description = "角色")
    private String role;
}

