package com.xh.print.controller.app;

import com.xh.common.web.domain.ResponseEntity;
import com.xh.print.domain.dto.TempFileDTO;
import com.xh.print.domain.entity.TempFile;
import com.xh.print.domain.form.TempFileEditForm;
import com.xh.print.service.TempFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * (TempFile)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-21 15:47:07
 */
@Tag(name = "用户端，打印模块")
@RestController
@RequestMapping("users-api/v1/temp-file")
public class UsersTempFileController {

    @Autowired
    private TempFileService tempFileService;

    @Operation(summary = "查询上传的临时文件列表")
    @GetMapping("list")
    public ResponseEntity<List<TempFile>> queryByList() {
        return ResponseEntity.success(tempFileService.queryByList());
    }

    @Operation(summary = "通过ID查询临时上传的文件")
    @GetMapping
    public ResponseEntity<TempFileDTO> queryById(Long id) {
        return ResponseEntity.success(tempFileService.queryDetailById(id));
    }

    @Operation(summary = "上传临时文件")
    @PostMapping(consumes = "multipart/form-data")
    public ResponseEntity<Long> insert(@RequestPart MultipartFile file,@RequestParam("fileName") String fileName) {
        return ResponseEntity.success(tempFileService.insert(file,fileName));
    }

    @Operation(summary = "修改临时上传的文件")
    @PutMapping
    public ResponseEntity<Boolean> update(@Valid @RequestBody TempFileEditForm form) {
        return ResponseEntity.success(tempFileService.update(form));
    }

    @Operation(summary = "删除临时上传的文件")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(tempFileService.deleteById(id));
    }

}

