package com.xh.common.web.controller;

import com.xh.common.utils.HttpClientUtils;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.UnexpectedTypeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 统一异常处理类
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class BaseController {
    //其他错误
    @ExceptionHandler
    public ResponseEntity<StatusCode> sysError(Exception exception) {
        log.error("系统错误", exception);
        return ResponseEntity.error(StatusCode.SYSTEM_ERROR);
    }

    @ExceptionHandler(value = NoResourceFoundException.class)
    public void noResourceFoundException(NoResourceFoundException e) {
        HttpServletResponse response = HttpClientUtils.getHttpServletResponse();
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }

    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public void noResourceFoundException(HttpRequestMethodNotSupportedException e) {
        HttpServletResponse response = HttpClientUtils.getHttpServletResponse();
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }


    //Post 无法读取数据,数据类型不匹配
    @ExceptionHandler(value = {HttpMessageNotReadableException.class,MethodArgumentTypeMismatchException.class})
    public ResponseEntity<StatusCode> httpMessageNotReadableException(HttpMessageNotReadableException e) {
        return ResponseEntity.error(StatusCode.REQ_PARAMS_ERR);
    }

    //Get 缺少必要参数
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public ResponseEntity<StatusCode> missingServletRequestParameterException() {
        return ResponseEntity.error(StatusCode.LACK_PARAMS);
    }

    //没有接口权限
    @ExceptionHandler(value = AccessDeniedException.class)
    public ResponseEntity<StatusCode> accessDeniedException() {
        return ResponseEntity.error(StatusCode.DENIED_ACCESS);
    }

    //入参验证不通过
    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    public ResponseEntity<StatusCode> methodArgumentNotValidException(MethodArgumentNotValidException ex) {
        return ResponseEntity.error(ex.getBindingResult().getFieldErrors().get(0).getDefaultMessage());
    }
    //业务异常
    @ExceptionHandler(value = ServiceException.class)
    public ResponseEntity<StatusCode> serviceException(ServiceException serviceException) {
        return ResponseEntity.error(serviceException.getCode(),serviceException.getMessage());
    }

}
