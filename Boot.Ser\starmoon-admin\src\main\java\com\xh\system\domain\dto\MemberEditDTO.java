package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class MemberEditDTO {
    /**
     * 微信用户ID
     */
    private Long id;
    /**
     * 生日
     */
    @Schema(description = "生日")
    private Date birthday;
    /**
     * 用户名
     */
    @Schema(description = "住户姓名")
    private String userName;
    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;
    /**
     * 头像
     */
    @Schema(description = "头像URL")
    private String avatarUrl;
    /**
     * 性别(man：男；woman：女)
     */
    @Schema(description = "性别：男man,女woman")
    private String gender;
    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;
    /**
     * 住户id
     */
    @Schema(description = "住户ID")
    private Long residentId;
}
