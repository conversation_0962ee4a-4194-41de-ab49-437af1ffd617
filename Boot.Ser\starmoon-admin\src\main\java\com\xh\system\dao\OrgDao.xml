<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.OrgDao">

    <resultMap type="com.xh.system.domain.entity.Org" id="UserGroupMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="ancestors" column="ancestors" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="detail">
        id, org_name, sort, create_time, update_time, parent_id, ancestors
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="UserGroupMap">
        select
          <include refid="detail"></include>
        from sys_org where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="UserGroupMap">
        select
            <include refid="detail"></include>
        from sys_org
        <where>
            <if test="orgName != null and '' != orgName">
                and org_name like  CONCAT('%',#{orgName},'%')
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
        </where>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orgName !=null and '' != orgName">
                org_name,
            </if>
            <if test="sort != null">
                sort,
            </if>
                create_time,
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="ancestors != null and '' != ancestors">
                ancestors,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="orgName !=null and '' != orgName">
                #{orgName},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
                now(),
            <if test="parentId != null">
                #{parentId},
            </if>
            <if test="ancestors != null and '' != ancestors">
                #{ancestors},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_org
        <set>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="ancestors != null and ancestors != ''">
                ancestors = #{ancestors},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from sys_org where id = #{id} or find_in_set(#{id},ancestors)
    </delete>

</mapper>

