package com.xh.system.dao;

import com.xh.system.domain.entity.Org;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * (UserGroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 09:45:34
 */
public interface OrgDao {

    Org queryById(Long id);

    List<Org> queryByList(@Param("orgName") String orgName,@Param("status") Integer status,@Param("parentId") Long parentId);

    int insert(Org userGroup);

    int update(Org userGroup);

    int deleteById(Long id);
}

