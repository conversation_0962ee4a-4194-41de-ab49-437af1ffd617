{"name": "vue3_cli_default", "version": "0.0.0", "scripts": {"dev": "vite --mode dev", "build": "vite build --mode prod", "serve": "vite preview"}, "dependencies": {"vue": "^3.2.8", "@element-plus/icons-vue": "^0.2.4", "axios": "^0.22.0", "element-plus": "^2.2.17", "jsencrypt": "^3.3.2", "less": "^4.2.0", "mitt": "^3.0.0", "nprogress": "^0.2.0", "vue-router": "^4.0.4", "wangeditor": "^4.7.11"}, "devDependencies": {"@vitejs/plugin-vue": "^1.6.0", "@vue/compiler-sfc": "^3.2.6", "vite": "^2.5.2"}}