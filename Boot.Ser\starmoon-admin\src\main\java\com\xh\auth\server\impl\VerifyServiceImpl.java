package com.xh.auth.server.impl;

import com.xh.auth.domain.Verify;
import com.xh.auth.server.VerifyService;
import com.xh.auth.server.SecretKeyService;
import com.xh.auth.domain.VerifyCode;
import com.xh.auth.server.VerifyCodeService;
import com.xh.common.utils.SecretUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.util.UUID;

@Service
public class VerifyServiceImpl implements VerifyService {

    @Autowired
    private VerifyCodeService verifyCodeService;

    @Autowired
    private SecretKeyService secretKeyService;

    @Override
    public Verify createVerify() {
        String token = String.valueOf(UUID.randomUUID()).replaceAll("-", "");
        VerifyCode verifyCode = verifyCodeService.generateVerifyCode(token);
        KeyPair keyPair = secretKeyService.generateKeyPair(token);
        return new Verify(token,verifyCode,keyPair);
    }

    @Override
    public KeyPair verify(String token, String value){
        KeyPair keyPair = secretKeyService.keyPair(token);
        if (keyPair == null){
            throw new ServiceException(StatusCode.VERIFY_CODE_NOT_EXIST);
        }
        PrivateKey privateKey = keyPair.getPrivate();
        String codeValue;
        try {
             codeValue = SecretUtils.decryptByPrivateKey(value, privateKey);
        }catch (Exception e){
            throw new ServiceException(StatusCode.VERIFY_CODE_NOT_EXIST);
        }

        if(verifyCodeService.verifyCode(token,codeValue)){
            return keyPair;
        }else {
            throw new ServiceException(StatusCode.VERIFY_CODE_ERROR);
        }
    }

}
