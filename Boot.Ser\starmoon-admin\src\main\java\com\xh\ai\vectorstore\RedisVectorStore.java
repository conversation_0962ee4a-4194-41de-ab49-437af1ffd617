package com.xh.ai.vectorstore;

import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Data;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisListCommands;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
public class RedisVectorStore implements VectorStore {

    private RedisConnection redisConnection;

    private EmbeddingModel embeddingModel;

    @Override
    public void add(List<Document> documents) {

        for(Document document: documents){
            float[] embed = embeddingModel.embed(document);
            RedisVectorStoreContent vectorStoreContent = RedisVectorStoreContent.builder()
                    .id(document.getId())
                    .text(document.getText())
                    .metadata(document.getMetadata())
                    .embedding(embed)
                    .createTime(new Date())
                    .build();
            RedisListCommands redisListCommands = redisConnection.listCommands();
            redisListCommands.rPush("vector_store".getBytes(StandardCharsets.UTF_8),JSON.toJSONString(vectorStoreContent).getBytes(StandardCharsets.UTF_8));
        }
    }

    @Override
    public void delete(List<String> idList) {

    }

    @Override
    public void delete(Filter.Expression filterExpression) {

    }


    @Override
    public List<Document> similaritySearch(SearchRequest request) {
        float[] userQueryEmbedding = this.embeddingModel.embed(request.getQuery());

        RedisListCommands redisListCommands = redisConnection.listCommands();
        Long len = redisListCommands.lLen("vector_store".getBytes(StandardCharsets.UTF_8));
        List<RedisVectorStoreContent> store = new ArrayList<>();
        for(int i=0;i<len;i++){
            byte[] bytes = redisListCommands.lIndex("vector_store".getBytes(StandardCharsets.UTF_8), i);
            RedisVectorStoreContent redisVectorStoreContent = JSON.parseObject(new String(bytes),RedisVectorStoreContent.class);
            store.add(redisVectorStoreContent);
        }

        return store.stream()
                .map(item -> {
                    double similarity = SimpleVectorStore.EmbeddingMath.cosineSimilarity(userQueryEmbedding, item.getEmbedding());
                    return Document.builder()
                            .id(item.getId())
                            .metadata(item.getMetadata())
                            .text(item.getText())
                            .score(similarity).build();
                })
                .filter(entry -> entry.getScore() >= request.getSimilarityThreshold())
                .sorted(Comparator.comparingDouble(Document::getScore).reversed())
                .limit(request.getTopK())
                .collect(Collectors.toList());
    }

}
