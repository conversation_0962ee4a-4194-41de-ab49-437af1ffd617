package com.xh.system.service.impl;


import com.xh.system.domain.entity.ImagetextReadstatus;

import com.xh.system.dao.ImagetextReadstatusDao;
import com.xh.system.service.ImagetextReadstatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;


/**
 * 图文读取状态管理表(ImagetextReadstatus)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21 08:50:39
 */
@Service
public class ImagetextReadstatusServiceImpl implements ImagetextReadstatusService {

    @Autowired
    private ImagetextReadstatusDao imagetextReadstatusDao;

    /**
     * 通过ID查询单条数据
     *
     * @param imagetextId 主键
     * @return 实例对象
     */
    @Override
    public ImagetextReadstatus queryById(Long imagetextId) {
        return this.imagetextReadstatusDao.queryById(imagetextId);
    }

    /**
     * 通过分页查询
     *
     * @param imagetextReadstatus 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public Page<ImagetextReadstatus> queryByPage(ImagetextReadstatus imagetextReadstatus, PageRequest pageRequest) {
        long total = this.imagetextReadstatusDao.count(imagetextReadstatus);
        return new PageImpl<>(this.imagetextReadstatusDao.queryAllByLimit(imagetextReadstatus, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param imagetextReadstatus 实例对象
     * @return 实例对象
     */
    @Override
    public ImagetextReadstatus insert(ImagetextReadstatus imagetextReadstatus) {
        this.imagetextReadstatusDao.insert(imagetextReadstatus);
        return imagetextReadstatus;
    }

    /**
     * 修改数据
     *
     * @param imagetextReadstatus 实例对象
     * @return 实例对象
     */
    @Override
    public ImagetextReadstatus update(ImagetextReadstatus imagetextReadstatus) {
        this.imagetextReadstatusDao.update(imagetextReadstatus);
        return this.queryById(imagetextReadstatus.getImagetextId());
    }

    /**
     * 通过主键删除数据
     *
     * @param imagetextId 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long imagetextId) {
        return this.imagetextReadstatusDao.deleteById(imagetextId) > 0;
    }
}
