import request from '@/utils/request'

// 分页查询用户列表
export function listmember(data) {
    return request({
        url: '/manage-api/v1/member/page',
        method: 'get',
        params: data
    })
}


// 添加用户
export function addmember(data) {
    return request({
        url: '/manage-api/v1/member',
        method: 'post',
        data
    })
}

// 编辑用户
export function editmember(data) {
    return request({
        url: '/manage-api/v1/member',
        method: 'put',
        data
    })
}

// 删除用户
export function deletemember(id) {
    return request({
        url: '/manage-api/v1/member',
        method: 'delete',
        params: { id }
    })
}

// 获取用户详情
export function getmember(id) {
    return request({
        url: '/manage-api/v1/member',
        method: 'get',
        params: { id }
    })
}

