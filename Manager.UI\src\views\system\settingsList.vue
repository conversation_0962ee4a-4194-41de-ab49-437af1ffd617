<template>
  <div class="page-container">
    <settings-edit @search="search"></settings-edit>
    <div class="page-content">
      <div class="card card--search">
        <div class="search-container">
          <div class="search-form">
            <el-input v-model="searchModel.settingsKey" placeholder="key" clearable />
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="search" class="search-btn">搜索</el-button>
            <el-button type="primary" @click="add" class="add-btn">添加</el-button>
          </div>
        </div>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table stripe :data="settingsList" style="width: 100%;" class="data-table" fit>
            <el-table-column prop="id" label="ID" align="center" width="100" />
            <el-table-column prop="settingsKey" label="键" align="center" width="200" />
            <el-table-column prop="settingValue" label="值" align="center" width="200" />
            <el-table-column prop="note" label="备注" align="center" />
            <el-table-column align="center" width="160" label="操作">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="edit(scope.row.id)">编辑</el-button>
                <el-button link type="danger" size="small" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick" @next-click="nextClick" :total="total" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mitt from '@/utils/mitt'
import { listSettings, deleteSettings, getSettings } from '@/api/system/systemSettings'
import settingsEdit from '@/components/system/settingsEdit.vue'
export default {
  components: { settingsEdit },
  data() {
    return {
      searchModel: { pageNum: 1, pageSize: 10 },
      settingsList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listSettings(this.searchModel)
        .then(res => {
          this.settingsList = res.data.data.list
          this.total = res.data.data.total
        })
        .catch(err => {
          this.$message.error(err.data.errorMessage)
        })
    },
    add() {
      mitt.emit('openSettingsAdd')
    },
    edit(id) {
      getSettings(id)
        .then(res => {
          mitt.emit('openSettingsEdit', res.data.data)
        })
        .catch(err => {
          this.$message.error(err.data.errorMessage)
        })
    },
    deleted(id) {
      this.$confirm('删除设置, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSettings(id)
          .then(() => {
            this.search()
            this.$message.success('操作成功')
          })
          .catch(err => {
            this.$message.error(err.data.errorMessage)
          })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    prevClick(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    nextClick(num) {
      this.searchModel.pageNum = num
      this.search()
    }
  },
  created() {
    mitt.off('openSettingsAdd')
    mitt.off('openSettingsEdit')
    this.search()
  }
}
</script>

<style scoped>
.card--search {
  margin-bottom: 20px;
}
</style> 