package com.xh.auth.controller;

import com.xh.auth.domain.LoginRequestParams;
import com.xh.auth.domain.Verify;
import com.xh.auth.server.AuthServer;
import com.xh.auth.server.VerifyService;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.security.token.AccessToken;
import com.xh.common.security.token.TokenService;
import com.xh.common.web.domain.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "管理端，授权模块")
@RestController
@RequestMapping("manage-api/v1/auth")
public class AuthController {

    @Autowired
    private AuthServer authServer;

    @Autowired
    private VerifyService verifyService;

    @Autowired
    private TokenService tokenService;

    @Operation(summary = "获取滑动验证码")
    @GetMapping(value = "verify-code")
    public ResponseEntity<Verify> grantVerifyCode(){
        return ResponseEntity.success(verifyService.createVerify());
    }


    @Operation(summary = "获取授权令牌")
    @PostMapping("token")
    @OperationLog(title = "获取授权令牌")
    public ResponseEntity<AccessToken> auth(@Valid @RequestBody LoginRequestParams params){
        return ResponseEntity.success(authServer.auth(params.getUserName(),params.getPassword(),verifyService.verify(params.getToken(), params.getPoint()).getPrivate()));
    }

    @Operation(summary = "刷新登录令牌")
    @PostMapping("refresh-token")
    @OperationLog(title = "刷新令牌")
    public ResponseEntity<AccessToken> refresh(@RequestParam("refreshToken") String refreshToken){
        return ResponseEntity.success(tokenService.refreshByRefresh(refreshToken));
    }

    @Operation(summary = "授权注销")
    @PostMapping("logout")
    @OperationLog(title = "注销令牌")
    public ResponseEntity<Boolean> logout() {
        return ResponseEntity.success(authServer.logout());
    }

}
