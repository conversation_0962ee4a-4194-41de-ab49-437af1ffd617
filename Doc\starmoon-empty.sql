/*
 Navicat Premium Data Transfer

 Source Server         : 内网199
 Source Server Type    : MySQL
 Source Server Version : 100603
 Source Host           : ************:3308
 Source Schema         : starmoon-empty

 Target Server Type    : MySQL
 Target Server Version : 100603
 File Encoding         : 65001

 Date: 27/05/2025 21:30:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name_cn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_en` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `css_class` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `parent_id` int NULL DEFAULT 0,
  `sort` int NULL DEFAULT 0,
  `note` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 156 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据字典' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict` VALUES (123, '通用状态', 'common_status', '#6EFF00', '2025-05-22 09:49:09', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (125, '正常', 'nomal', '#52c41a', '2025-05-22 22:07:32', NULL, 123, 0, NULL);
INSERT INTO `sys_dict` VALUES (126, '异常', 'abnormal', '#f5222d', '2025-05-22 22:08:13', NULL, 123, 1, NULL);
INSERT INTO `sys_dict` VALUES (127, '系统图文', 'sys_imagetext', '#52c41a', '2025-05-22 22:09:35', '2025-05-26 09:17:37', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (128, '轮播图', 'banner', NULL, '2025-05-22 22:10:12', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (133, '关于我们', 'about_us', NULL, '2025-05-22 22:11:54', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (134, '隐私协议', 'privacy_policy', NULL, '2025-05-22 22:12:21', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (136, '图文主类型', 'master_type', NULL, '2025-05-22 22:15:17', '2025-05-26 13:45:55', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (137, '全局图文', 'global', NULL, '2025-05-22 22:15:32', NULL, 136, 0, NULL);
INSERT INTO `sys_dict` VALUES (138, '小区图文', 'community', NULL, '2025-05-22 22:15:50', NULL, 136, 0, NULL);
INSERT INTO `sys_dict` VALUES (139, '楼栋', 'building', NULL, '2025-05-23 16:50:46', NULL, 136, 0, NULL);
INSERT INTO `sys_dict` VALUES (140, '消息图文', 'notice_imagetext', '#52c41a', '2025-05-26 09:17:55', '2025-05-26 09:18:52', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (141, '物业公告', 'property_notice', NULL, '2025-05-26 09:28:44', '2025-05-27 14:48:28', 140, 0, NULL);
INSERT INTO `sys_dict` VALUES (143, '组织类型', 'org_type', NULL, '2025-05-26 13:54:21', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (144, '区域', 'region', NULL, '2025-05-26 13:54:45', NULL, 143, 0, NULL);
INSERT INTO `sys_dict` VALUES (145, '部门', 'dept', NULL, '2025-05-26 13:54:56', NULL, 143, 0, NULL);
INSERT INTO `sys_dict` VALUES (146, '房屋类型', 'building_type', NULL, '2025-05-26 13:55:12', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (147, '楼栋', 'building', NULL, '2025-05-26 13:55:30', NULL, 146, 0, NULL);
INSERT INTO `sys_dict` VALUES (148, '分区(单元)', 'unit', NULL, '2025-05-26 13:56:19', NULL, 146, 0, NULL);
INSERT INTO `sys_dict` VALUES (149, '房间', 'room', NULL, '2025-05-26 13:56:32', NULL, 146, 0, NULL);
INSERT INTO `sys_dict` VALUES (150, '客服消息', 'customer_message', NULL, '2025-05-27 14:48:52', NULL, 140, 0, NULL);
INSERT INTO `sys_dict` VALUES (151, '社区公告', 'community_notice', NULL, '2025-05-27 14:49:21', NULL, 140, 0, NULL);
INSERT INTO `sys_dict` VALUES (152, '紧急通告', 'emergency_notice', NULL, '2025-05-27 14:49:36', NULL, 140, 0, NULL);
INSERT INTO `sys_dict` VALUES (153, '菜单类型', 'menu_type', NULL, '2025-05-27 19:34:26', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (154, '菜单', 'menu', NULL, '2025-05-27 19:34:35', NULL, 153, 0, NULL);
INSERT INTO `sys_dict` VALUES (155, '权限', 'permis', NULL, '2025-05-27 19:35:31', NULL, 153, 0, NULL);

-- ----------------------------
-- Table structure for sys_imagetext
-- ----------------------------
DROP TABLE IF EXISTS `sys_imagetext`;
CREATE TABLE `sys_imagetext`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图文id',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `image_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配图',
  `content` blob NULL COMMENT '内容',
  `link` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `sort` bigint NULL DEFAULT NULL COMMENT '排序',
  `publisher_user_id` bigint NULL DEFAULT NULL COMMENT '发布者用户id',
  `type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型（关联字典：system_imagetext，notic_imagetext）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `master_ids` bigint NULL DEFAULT NULL COMMENT '主类型id（全局为NULL，小区为：小区id）',
  `master_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主类型（小区图文：community）',
  `extend_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展数据',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统图文表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_imagetext
-- ----------------------------
INSERT INTO `sys_imagetext` VALUES (2, '轮播图1', 'assets/1c385998af46416f9f901f5c850d47a5.jpeg', NULL, 'https://www.baidu.com', 1, NULL, 'banner', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_imagetext` VALUES (3, '轮播图2', 'assets/90da8bd7736c486a8cdda02c3d097355.jpeg', NULL, NULL, 2, NULL, 'banner', NULL, '2025-05-27 15:49:53', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_imagetext_readstatus
-- ----------------------------
DROP TABLE IF EXISTS `sys_imagetext_readstatus`;
CREATE TABLE `sys_imagetext_readstatus`  (
  `imagetext_id` bigint NOT NULL COMMENT '图文id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`imagetext_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '图文读取状态管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_imagetext_readstatus
-- ----------------------------

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`  (
  `id` bigint NOT NULL DEFAULT 0 COMMENT 'id',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方法（GET,POST,...）',
  `request_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求地址（/user?id=1）',
  `request_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求体',
  `response_code` tinyint NULL DEFAULT NULL COMMENT '响应状态码',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `org_id` bigint NULL DEFAULT NULL COMMENT '组织id',
  `ip_address` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `consume_time` int NULL DEFAULT NULL COMMENT '耗时',
  `error_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误消息',
  `error_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误码',
  `extra_data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展数据(记录修改之前，和修改之后)',
  `target_id` bigint NULL DEFAULT NULL COMMENT '操作对象',
  `operation_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
  `operation_desc` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `menu_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M菜单 P权限）',
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '父菜单ID',
  `sort` int NULL DEFAULT 0 COMMENT '序号',
  `path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `permission` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `component_path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `note` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `extend_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 177 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 'menu', 0, 1, 'system', NULL, NULL, NULL, '2021-10-05 10:40:22', '2025-05-27 08:52:43', '系统管理目录', NULL);
INSERT INTO `sys_menu` VALUES (100, '用户管理', 'menu', 1, 1, 'user', NULL, 'system/userList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:49', '用户管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (101, '角色管理', 'menu', 1, 2, 'role', NULL, 'system/roleList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:52', '角色管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 'menu', 1, 3, 'menu', NULL, 'system/menuList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:55', '菜单管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (105, '数据字典', 'menu', 1, 4, 'dict', NULL, 'system/dictList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:59', '字典管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (110, '添加', 'permis', 100, 0, '', 'sys:user:add', NULL, NULL, '2021-12-15 16:58:35', '2025-05-27 08:52:43', NULL, NULL);
INSERT INTO `sys_menu` VALUES (123, '添加', 'permis', 102, 0, '', 'sys:menu:add', NULL, NULL, '2021-12-15 16:58:35', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (124, '编辑', 'permis', 101, 0, '', 'sys:role:edit', NULL, NULL, '2021-12-15 16:58:35', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (126, '编辑', 'permis', 102, 0, '', 'sys:menu:edit', NULL, NULL, '2022-01-22 20:21:42', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (127, '删除', 'permis', 102, 0, '', 'sys:menu:delete', NULL, NULL, '2022-01-22 20:22:04', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (128, '添加', 'permis', 105, 0, '', 'sys:dict:add', NULL, NULL, '2022-02-06 18:03:36', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (129, '添加', 'permis', 101, 0, '', 'sys:role:add', NULL, NULL, '2022-02-06 18:09:09', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (130, '编辑', 'permis', 100, 0, '', 'sys:user:edit', NULL, NULL, '2022-02-06 18:20:21', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (151, '消息中心', 'menu', 0, 10, 'message', NULL, NULL, NULL, '2025-05-23 09:43:46', '2025-05-27 14:21:05', '', NULL);
INSERT INTO `sys_menu` VALUES (152, '消息通知', 'menu', 151, 10, 'noticeList', NULL, 'notice/noticeList', NULL, '2025-05-23 15:54:09', '2025-05-27 15:44:23', '', NULL);
INSERT INTO `sys_menu` VALUES (154, '活动管理', 'menu', 0, 10, 'activity', NULL, 'activity/list', NULL, '2025-05-26 09:40:15', '2025-05-27 14:21:05', '', NULL);
INSERT INTO `sys_menu` VALUES (155, '小区管理', 'menu', 0, 2, 'community', NULL, 'community/list', NULL, '2025-05-26 09:40:28', '2025-05-27 14:39:41', '', NULL);
INSERT INTO `sys_menu` VALUES (159, '缴费管理', 'menu', 0, 10, 'payment', NULL, 'payment/list', NULL, '2025-05-26 09:43:06', '2025-05-27 14:21:05', '', NULL);
INSERT INTO `sys_menu` VALUES (164, '好物管理', 'menu', 0, 10, 'product', NULL, 'product/list', NULL, '2025-05-26 09:47:59', '2025-05-27 14:21:05', '', NULL);
INSERT INTO `sys_menu` VALUES (166, '图文管理', 'menu', 1, 10, 'imagetext', NULL, 'system/imagetextList', NULL, '2025-05-27 08:52:27', '2025-05-27 15:24:16', '', NULL);
INSERT INTO `sys_menu` VALUES (167, '小区楼房', 'menu', 155, 1, 'building', NULL, 'community/communityBuildingList', '#', '2025-05-27 14:16:28', '2025-05-27 17:25:31', '', NULL);
INSERT INTO `sys_menu` VALUES (168, '小区信息', 'menu', 155, 0, 'community', NULL, 'community/communityList', '#', '2025-05-27 14:18:07', '2025-05-27 17:14:22', '', NULL);
INSERT INTO `sys_menu` VALUES (169, '小区车辆', 'menu', 155, 3, 'vehicleList', NULL, 'community/communityVehicleList', '#', '2025-05-27 14:34:03', '2025-05-27 17:26:24', '', NULL);
INSERT INTO `sys_menu` VALUES (170, '小区住户', 'menu', 155, 2, 'resident', NULL, 'community/communityResidentList', '#', '2025-05-27 14:39:25', '2025-05-27 17:26:15', '', NULL);
INSERT INTO `sys_menu` VALUES (171, '物业管理', 'menu', 0, 3, 'property', NULL, NULL, '#', '2025-05-27 16:00:28', '2025-05-27 16:02:19', '', NULL);
INSERT INTO `sys_menu` VALUES (172, '物业人员', 'menu', 171, 1, 'personList', NULL, 'property/personList', '#', '2025-05-27 16:01:43', '2025-05-27 16:08:53', '', NULL);
INSERT INTO `sys_menu` VALUES (174, '缴费项目', 'menu', 171, 2, 'paymentItemsList', NULL, 'property/paymentItemsList', '#', '2025-05-27 16:07:50', '2025-05-27 16:08:54', '', NULL);
INSERT INTO `sys_menu` VALUES (175, '物业账单', 'menu', 171, 3, 'propertyBillList', NULL, 'property/billList', '#', '2025-05-27 16:10:44', '2025-05-27 16:11:30', '', NULL);
INSERT INTO `sys_menu` VALUES (176, '组织管理', 'menu', 1, 0, 'organizational', NULL, 'system/organizational', '#', '2025-05-27 19:31:21', '2025-05-27 19:36:19', '', NULL);

-- ----------------------------
-- Table structure for sys_org
-- ----------------------------
DROP TABLE IF EXISTS `sys_org`;
CREATE TABLE `sys_org`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组名',
  `type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组织类型（区域：region,部门：dept）',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父ID',
  `ancestors` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组级/分隔',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7091961583521234954 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组织（区域）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_org
-- ----------------------------
INSERT INTO `sys_org` VALUES (7091961583521234946, '江苏省', NULL, 0, '2025-05-26 14:50:08', '2025-05-26 14:52:09', 7091961583521234945, NULL);
INSERT INTO `sys_org` VALUES (7091961583521234948, '中国', NULL, 0, '2025-05-26 15:03:18', NULL, 0, NULL);
INSERT INTO `sys_org` VALUES (7091961583521234949, '江苏省', NULL, 0, '2025-05-26 15:03:24', NULL, 7091961583521234948, NULL);
INSERT INTO `sys_org` VALUES (7091961583521234950, '苏州市', NULL, 0, '2025-05-26 15:03:36', NULL, 7091961583521234949, NULL);
INSERT INTO `sys_org` VALUES (7091961583521234951, '吴江区', NULL, 0, '2025-05-26 15:03:43', NULL, 7091961583521234950, NULL);
INSERT INTO `sys_org` VALUES (7091961583521234952, '网信科技', NULL, 0, '2025-05-26 15:04:06', NULL, 7091961583521234951, NULL);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `sort` int NULL DEFAULT 0 COMMENT '显示顺序',
  `data_scope` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据范围',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'superAdmin', 1, NULL, '2021-10-05 10:40:22', '2022-02-06 18:07:51', '超级管理员');
INSERT INTO `sys_role` VALUES (2, '管理员', 'admin', 2, NULL, '2021-10-05 10:40:22', '2022-05-16 16:03:23', '普通角色');

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 1);
INSERT INTO `sys_role_menu` VALUES (1, 100);
INSERT INTO `sys_role_menu` VALUES (1, 101);
INSERT INTO `sys_role_menu` VALUES (1, 102);
INSERT INTO `sys_role_menu` VALUES (1, 105);
INSERT INTO `sys_role_menu` VALUES (1, 110);
INSERT INTO `sys_role_menu` VALUES (1, 123);
INSERT INTO `sys_role_menu` VALUES (1, 124);
INSERT INTO `sys_role_menu` VALUES (1, 126);
INSERT INTO `sys_role_menu` VALUES (1, 127);
INSERT INTO `sys_role_menu` VALUES (1, 128);
INSERT INTO `sys_role_menu` VALUES (1, 129);
INSERT INTO `sys_role_menu` VALUES (1, 130);
INSERT INTO `sys_role_menu` VALUES (1, 151);
INSERT INTO `sys_role_menu` VALUES (1, 152);
INSERT INTO `sys_role_menu` VALUES (1, 154);
INSERT INTO `sys_role_menu` VALUES (1, 155);
INSERT INTO `sys_role_menu` VALUES (1, 159);
INSERT INTO `sys_role_menu` VALUES (1, 164);
INSERT INTO `sys_role_menu` VALUES (1, 166);
INSERT INTO `sys_role_menu` VALUES (1, 167);
INSERT INTO `sys_role_menu` VALUES (1, 168);
INSERT INTO `sys_role_menu` VALUES (1, 169);
INSERT INTO `sys_role_menu` VALUES (1, 170);
INSERT INTO `sys_role_menu` VALUES (1, 171);
INSERT INTO `sys_role_menu` VALUES (1, 172);
INSERT INTO `sys_role_menu` VALUES (1, 174);
INSERT INTO `sys_role_menu` VALUES (1, 175);
INSERT INTO `sys_role_menu` VALUES (1, 176);
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 110);
INSERT INTO `sys_role_menu` VALUES (2, 113);
INSERT INTO `sys_role_menu` VALUES (2, 122);
INSERT INTO `sys_role_menu` VALUES (2, 125);
INSERT INTO `sys_role_menu` VALUES (2, 130);
INSERT INTO `sys_role_menu` VALUES (2, 132);
INSERT INTO `sys_role_menu` VALUES (2, 137);
INSERT INTO `sys_role_menu` VALUES (2, 138);
INSERT INTO `sys_role_menu` VALUES (2, 139);
INSERT INTO `sys_role_menu` VALUES (2, 140);
INSERT INTO `sys_role_menu` VALUES (2, 141);
INSERT INTO `sys_role_menu` VALUES (2, 142);
INSERT INTO `sys_role_menu` VALUES (7, 1);
INSERT INTO `sys_role_menu` VALUES (7, 105);
INSERT INTO `sys_role_menu` VALUES (7, 113);
INSERT INTO `sys_role_menu` VALUES (7, 122);
INSERT INTO `sys_role_menu` VALUES (7, 125);
INSERT INTO `sys_role_menu` VALUES (7, 128);
INSERT INTO `sys_role_menu` VALUES (8, 1);
INSERT INTO `sys_role_menu` VALUES (9, 113);
INSERT INTO `sys_role_menu` VALUES (9, 122);
INSERT INTO `sys_role_menu` VALUES (9, 125);
INSERT INTO `sys_role_menu` VALUES (9, 132);
INSERT INTO `sys_role_menu` VALUES (9, 137);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `gender` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别',
  `avatar_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '帐号状态',
  `org_id` bigint NOT NULL COMMENT '组织',
  `role_id` bigint NOT NULL COMMENT '角色',
  `note` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7091961589154185217 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'system', NULL, '1212', '$2a$10$mrL/kCDGGSLWBDr0b0JyquBanoZnXoiNrupX42r9AdlGpB.7Ze8Gy', '111111', '1', '', '1', 1, 1, '1212', '2023-07-21 13:38:27', '2025-03-06 18:55:36');

-- ----------------------------
-- Table structure for t_community
-- ----------------------------
DROP TABLE IF EXISTS `t_community`;
CREATE TABLE `t_community`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '小区ID',
  `community_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `note` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `lng` double(11, 8) NULL DEFAULT NULL COMMENT '经度',
  `lat` double(11, 8) NULL DEFAULT NULL COMMENT '纬度',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `expand_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展数据',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态(0：禁用；1：正常)',
  `sort` int NULL DEFAULT NULL COMMENT '序号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `org_id` bigint NULL DEFAULT NULL COMMENT '组织id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小区' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_community
-- ----------------------------
INSERT INTO `t_community` VALUES (2, '网信科技', '', 120.66326300, 31.12082500, '邦宁', '', NULL, 0, '2025-05-27 10:02:05', '2025-05-27 10:29:12', 7091961583521234948);
INSERT INTO `t_community` VALUES (3, '城南花园', '', 120.65870000, 31.12962100, '江陵街道', '', NULL, 0, '2025-05-27 10:25:51', '2025-05-27 10:29:59', 7091961583521234948);
INSERT INTO `t_community` VALUES (4, '新港天成', '', 120.66699300, 31.13119200, '江陵街道', '', NULL, 0, '2025-05-27 10:26:09', '2025-05-27 10:30:22', 7091961583521234948);
INSERT INTO `t_community` VALUES (5, '绿地太湖', '', 120.59515300, 31.12698500, '太湖大道', '', NULL, 0, '2025-05-27 10:26:30', '2025-05-27 10:30:48', 7091961583521234948);

-- ----------------------------
-- Table structure for t_community_building
-- ----------------------------
DROP TABLE IF EXISTS `t_community_building`;
CREATE TABLE `t_community_building`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `building_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '建筑编号',
  `type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `lng` double(11, 8) NULL DEFAULT NULL COMMENT '纬度',
  `lat` double(11, 8) NULL DEFAULT NULL COMMENT '经度',
  `alt` double(11, 8) NULL DEFAULT NULL COMMENT '海拔',
  `expand_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展数据',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `sort` int NULL DEFAULT 0 COMMENT '序号',
  `community_id` bigint NOT NULL COMMENT '所属小区',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '楼栋表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_community_building
-- ----------------------------
INSERT INTO `t_community_building` VALUES (4, '一栋', NULL, 120.66326300, 31.12082500, 20.00000000, '', '2025-05-27 11:10:11', '2025-05-27 21:18:28', '', 0, 2);
INSERT INTO `t_community_building` VALUES (5, '一单元', NULL, 120.66326300, 31.12082500, 1.00000000, '', '2025-05-27 11:12:25', '2025-05-27 21:18:28', '', 0, 2);
INSERT INTO `t_community_building` VALUES (6, '101', NULL, 120.66326300, 31.12082500, 1.00000000, '', '2025-05-27 11:12:41', '2025-05-27 21:18:28', '', 0, 2);

-- ----------------------------
-- Table structure for t_community_building_room
-- ----------------------------
DROP TABLE IF EXISTS `t_community_building_room`;
CREATE TABLE `t_community_building_room`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `room_number` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房间编号',
  `type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '户型',
  `unit_number` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单元编号',
  `building_id` bigint NULL DEFAULT NULL COMMENT '楼栋ID',
  `area` decimal(10, 2) NULL DEFAULT NULL COMMENT '面积m²',
  `expand_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展数据',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '楼栋房间表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_community_building_room
-- ----------------------------
INSERT INTO `t_community_building_room` VALUES (1, 'string', 'string', 'string', 9007199254740991, 0.10, 'string', 'string', NULL, NULL);
INSERT INTO `t_community_building_room` VALUES (2, 'string', 'string', 'string', 9007199254740991, 0.10, 'string', 'string', NULL, NULL);

-- ----------------------------
-- Table structure for t_community_resident
-- ----------------------------
DROP TABLE IF EXISTS `t_community_resident`;
CREATE TABLE `t_community_resident`  (
  `id` bigint NOT NULL COMMENT 'ID',
  `room_id` bigint NOT NULL COMMENT '房间id',
  `resident_id` bigint NOT NULL COMMENT '住户id',
  `resident_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住户类型（业主，租客，家属）',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住址',
  `tags` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `photo` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '照片',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '人房表（小区住户）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_community_resident
-- ----------------------------

-- ----------------------------
-- Table structure for t_community_vehicle
-- ----------------------------
DROP TABLE IF EXISTS `t_community_vehicle`;
CREATE TABLE `t_community_vehicle`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '小区车辆id',
  `plate_number` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌编号',
  `plate_color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌颜色（关联字典plate_color）',
  `parking_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车位类型',
  `parking_number` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车位编号',
  `valid_begin_time` datetime NULL DEFAULT NULL COMMENT '有效期开始时间',
  `valid_end_time` datetime NULL DEFAULT NULL COMMENT '有效期结束时间',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `main_use` bit(1) NULL DEFAULT NULL COMMENT '主要使用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `media` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '媒体对象用来存储图片',
  `community_id` bigint NULL DEFAULT NULL COMMENT '小区id',
  `resident_id` bigint NULL DEFAULT NULL COMMENT '住户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小区车辆表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_community_vehicle
-- ----------------------------
INSERT INTO `t_community_vehicle` VALUES (1, '豫P25Z95', 'blue', '', '', NULL, NULL, '1', 'normal', b'1', NULL, '2025-05-27 14:35:36', '', NULL, NULL);

-- ----------------------------
-- Table structure for t_community_visitor
-- ----------------------------
DROP TABLE IF EXISTS `t_community_visitor`;
CREATE TABLE `t_community_visitor`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `visitor_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '访客姓名',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `vehicle_number` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌号',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `stay_duration` int NULL DEFAULT NULL COMMENT '停留时长',
  `time_unit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时间单位',
  `visit_time` datetime NULL DEFAULT NULL COMMENT '来访时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `resident_id` bigint NULL DEFAULT NULL COMMENT '住户id',
  `community_id` bigint NOT NULL COMMENT '小区id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小区访客信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_community_visitor
-- ----------------------------

-- ----------------------------
-- Table structure for t_community_work_order
-- ----------------------------
DROP TABLE IF EXISTS `t_community_work_order`;
CREATE TABLE `t_community_work_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `work_order_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单类型',
  `work_order_describe` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '描述',
  `media` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '媒体',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `openid` bigint NOT NULL COMMENT '小程序用户id',
  `person_id` bigint NULL DEFAULT NULL COMMENT '物业人员id',
  `master_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分支类型（building,public）',
  `master_id` bigint NULL DEFAULT NULL,
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小区工单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_community_work_order
-- ----------------------------

-- ----------------------------
-- Table structure for t_member
-- ----------------------------
DROP TABLE IF EXISTS `t_member`;
CREATE TABLE `t_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '微信用户ID',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信小程序用户openid',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信用户统一ID',
  `user_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `gender` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别(man：男；woman：女)',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `resident_id` bigint NULL DEFAULT NULL COMMENT '住户id',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `role` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'tourist' COMMENT '用户角色',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户端用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_member
-- ----------------------------
INSERT INTO `t_member` VALUES (1, 'o7nun7eStCo5fseNxeSceVHuz-Q0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_member` VALUES (2, '111', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 15:36:19', NULL, NULL, 'user');
INSERT INTO `t_member` VALUES (3, 'o7nun7a9uuYlWTOSj6XlTtnz5M4U', NULL, '卜凡傲', NULL, NULL, 'man', '13685156020', NULL, '2025-05-27 15:36:19', 1, '1990-06-06', 'user');

-- ----------------------------
-- Table structure for t_property_bill
-- ----------------------------
DROP TABLE IF EXISTS `t_property_bill`;
CREATE TABLE `t_property_bill`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '物业账单id',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `pay_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付类型',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `openid` int NULL DEFAULT NULL COMMENT '用户openid（业主不是app用户，请通知业主注册缴费）',
  `building_id` bigint NOT NULL COMMENT '楼房id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物业账单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_property_bill
-- ----------------------------

-- ----------------------------
-- Table structure for t_property_bill_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_property_bill_detail`;
CREATE TABLE `t_property_bill_detail`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `payment_item` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费项目',
  `amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '项目金额',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `bill_id` int NULL DEFAULT NULL COMMENT '账单id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物业账单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_property_bill_detail
-- ----------------------------

-- ----------------------------
-- Table structure for t_property_payment_items
-- ----------------------------
DROP TABLE IF EXISTS `t_property_payment_items`;
CREATE TABLE `t_property_payment_items`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `payment_item_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费项目名',
  `payment_item_describe` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费项目描述',
  `amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '金额',
  `community_id` bigint NULL DEFAULT NULL COMMENT '小区id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物业缴费项目' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_property_payment_items
-- ----------------------------

-- ----------------------------
-- Table structure for t_property_person
-- ----------------------------
DROP TABLE IF EXISTS `t_property_person`;
CREATE TABLE `t_property_person`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `person_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员姓名',
  `gender` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `certificate_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件类型',
  `id_card` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住址',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `emer_phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应急手机号',
  `number` int NULL DEFAULT NULL COMMENT '编号',
  `duty` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
  `entry_time` datetime NULL DEFAULT NULL COMMENT '入职时间',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `salary` decimal(10, 2) NULL DEFAULT NULL COMMENT '薪资',
  `performance` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '绩效',
  `qualification` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学历',
  `major` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业',
  `skills` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '技能（财务管理,会计审核）',
  `certificates` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书（会计证,财务证）',
  `media` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '照片（{face_url:xxx,type:image}）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `org_id` bigint NOT NULL COMMENT '组织部门id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物业人员信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_property_person
-- ----------------------------

-- ----------------------------
-- Table structure for t_resident
-- ----------------------------
DROP TABLE IF EXISTS `t_resident`;
CREATE TABLE `t_resident`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '住户id',
  `resident_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '住户姓名',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `gender` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `certificate_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件类型',
  `id_card_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号码',
  `native_place` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '籍贯',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `photo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '照片',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台住户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_resident
-- ----------------------------
INSERT INTO `t_resident` VALUES (1, '卜凡傲', '1990-06-06', 'man', 'ID_CARD', '320323199006060298', NULL, '13685156020', '', '2025-05-27 08:37:06', NULL);

SET FOREIGN_KEY_CHECKS = 1;
