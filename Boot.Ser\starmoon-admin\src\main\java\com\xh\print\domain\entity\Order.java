package com.xh.print.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单(Order)实体类
 *
 * <AUTHOR>
 * @since 2025-06-14 11:10:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Order implements Serializable {
    private static final long serialVersionUID = -60229077916360950L;
/**
     * 订单编号
     */
    private Long id;
/**
     * 运单号
     */
    private Integer shippingNo;
/**
     * 支付单号
     */
    private String payNo;
/**
     * 支付类型
     */
    private Integer payType;
/**
     * 支付金额
     */
    private Double payAmount;
/**
     * 总金额
     */
    private Double totalAmount;
/**
     * 运输金额
     */
    private Double shippingAmount;
/**
     * 产品金额
     */
    private Double productAmount;
/**
     * 用户ID
     */
    private String userId;
/**
     * 地址（存储地址JSON字符串）
     */
    private String address;
/**
     * 订单状态
     */
    private Integer status;
/**
     * 取消说明
     */
    private String cancelExplain;
/**
     * 退款说明
     */
    private String refundExplain;
/**
     * 订单备注
     */
    private String note;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 修改时间
     */
    private Date updateTime;
/**
     * 支付时间
     */
    private Date payTime;
/**
     * 退款时间
     */
    private Date refundTime;

}

