package com.xh.auth.domain;

import lombok.Data;

import java.security.KeyPair;

@Data
public class Verify {

    private String token;

    private byte[] oriImage;

    private byte[] blockImage;

    private byte[] secretKey;

    public Verify(String token, VerifyCode verifyCode, KeyPair keyPair){
        this.token = token;
        this.oriImage = verifyCode.getOriImage();
        this.blockImage = verifyCode.getBlockImage();
        this.secretKey = keyPair.getPublic().getEncoded();

    }

}
