package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class RoleDTO {

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色名")
    private String roleName;

    @Schema(description = "角色代码")
    private String roleCode;

    @Schema(description = "数据范围")
    private Integer dataScope;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private Date createTime;

    @Schema(description = "备注")
    private Date updateTime;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "权限ID（菜单id）")
    private Long[] permissions;

}
