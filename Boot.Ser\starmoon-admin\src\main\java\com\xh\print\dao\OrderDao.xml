<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.print.dao.OrderDao">

    <resultMap type="com.xh.print.domain.entity.Order" id="OrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="shippingNo" column="shipping_no" jdbcType="INTEGER"/>
        <result property="payNo" column="pay_no" jdbcType="VARCHAR"/>
        <result property="payType" column="pay_type" jdbcType="INTEGER"/>
        <result property="payAmount" column="pay_amount" jdbcType="NUMERIC"/>
        <result property="totalAmount" column="total_amount" jdbcType="NUMERIC"/>
        <result property="shippingAmount" column="shipping_amount" jdbcType="NUMERIC"/>
        <result property="productAmount" column="product_amount" jdbcType="NUMERIC"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="cancelExplain" column="cancel_explain" jdbcType="VARCHAR"/>
        <result property="refundExplain" column="refund_explain" jdbcType="VARCHAR"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="refundTime" column="refund_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryById" resultMap="OrderMap">
        select id,
               shipping_no,
               pay_no,
               pay_type,
               pay_amount,
               total_amount,
               shipping_amount,
               product_amount,
               user_id,
               address,
               status,
               cancel_explain,
               refund_explain,
               note,
               create_time,
               update_time,
               pay_time,
               refund_time
        from `order`
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="OrderMap">
        select
        id, shipping_no, pay_no, pay_type, pay_amount, total_amount, shipping_amount, product_amount, user_id, address,
        status, cancel_explain, refund_explain, note, create_time, update_time, pay_time, refund_time
        from `order`
        <where>

            <if test="status != null">
                and status = #{status}
            </if>

        </where>

    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO  `order`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="shippingNo != null ">
                shipping_no,
            </if>
            <if test="payNo != null  and '' != payNo">
                pay_no,
            </if>
            <if test="payType != null ">
                pay_type,
            </if>
            <if test="payAmount != null ">
                pay_amount,
            </if>
            <if test="totalAmount != null ">
                total_amount,
            </if>
            <if test="shippingAmount != null ">
                shipping_amount,
            </if>
            <if test="productAmount != null ">
                product_amount,
            </if>
            <if test="userId != null  and '' != userId">
                user_id,
            </if>
            <if test="address != null  and '' != address">
                address,
            </if>
            <if test="status != null ">
                status,
            </if>
            <if test="cancelExplain != null  and '' != cancelExplain">
                cancel_explain,
            </if>
            <if test="refundExplain != null  and '' != refundExplain">
                refund_explain,
            </if>
            <if test="note != null  and '' != note">
                note,
            </if>
            create_time,

            <if test="payTime != null ">
                pay_time,
            </if>
            <if test="refundTime != null ">
                refund_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="shippingNo != null ">
                #{shippingNo},
            </if>
            <if test="payNo != null  and '' != payNo">
                #{payNo},
            </if>
            <if test="payType != null ">
                #{payType},
            </if>
            <if test="payAmount != null ">
                #{payAmount},
            </if>
            <if test="totalAmount != null ">
                #{totalAmount},
            </if>
            <if test="shippingAmount != null ">
                #{shippingAmount},
            </if>
            <if test="productAmount != null ">
                #{productAmount},
            </if>
            <if test="userId != null  and '' != userId">
                #{userId},
            </if>
            <if test="address != null  and '' != address">
                #{address},
            </if>
            <if test="status != null ">
                #{status},
            </if>
            <if test="cancelExplain != null  and '' != cancelExplain">
                #{cancelExplain},
            </if>
            <if test="refundExplain != null  and '' != refundExplain">
                #{refundExplain},
            </if>
            <if test="note != null  and '' != note">
                #{note},
            </if>
            #{createTime},

            <if test="payTime != null ">
                #{payTime},
            </if>
            <if test="refundTime != null ">
                #{refundTime},
            </if>
        </trim>
    </insert>

    <update id="update">
        update  `order`
        <set>
            <if test="shippingNo != null">
                shipping_no = #{shippingNo},
            </if>
            <if test="payNo != null and payNo != ''">
                pay_no = #{payNo},
            </if>
            <if test="payType != null">
                pay_type = #{payType},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount},
            </if>
            <if test="shippingAmount != null">
                shipping_amount = #{shippingAmount},
            </if>
            <if test="productAmount != null">
                product_amount = #{productAmount},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="cancelExplain != null and cancelExplain != ''">
                cancel_explain = #{cancelExplain},
            </if>
            <if test="refundExplain != null and refundExplain != ''">
                refund_explain = #{refundExplain},
            </if>
            <if test="note != null and note != ''">
                note = #{note},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete
        from  `order`
        where id = #{id}
    </delete>

</mapper>

