package com.xh.common.utils;

import com.xh.common.web.exception.ServiceException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DateUtils {
    public static String FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss";
    public static String FORMAT_DATE = "yyyy-MM-dd";
    public static String FORMAT_TIME = "HH:mm:ss";

    public static String dateToString(Date date,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    public static Date stringToDate(String dateStr,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(dateStr);
        }catch (ParseException e){
            throw new ServiceException("时间格式化异常");
        }
    }
}
