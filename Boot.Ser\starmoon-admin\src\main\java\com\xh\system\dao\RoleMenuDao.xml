<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.RoleMenuDao">

    <resultMap type="com.xh.system.domain.entity.RoleMenu" id="RoleMenuMap">
        <result property="roleId" column="role_id" jdbcType="INTEGER"/>
        <result property="menuId" column="menu_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryByRoleId" resultType="long">
        select
          menu_id
        from sys_role_menu
        where role_id = #{roleId}
    </select>

    <!--新增所有列-->
    <insert id="insertBatch">
        insert into sys_role_menu (role_id,menu_id)
        values
        <foreach collection="menuIds" item="item" separator=",">
            (#{roleId},#{item})
        </foreach>
    </insert>


    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_role_menu where role_id = #{roleId}
    </delete>

</mapper>

