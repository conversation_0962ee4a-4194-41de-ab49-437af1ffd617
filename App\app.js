var api = require('./utils/api.js')
App({
  onLaunch: function () {
    wx.setStorageSync('LoginStatus', false)
    this.login()
  },
 
  login: function () {
    var that = this;
    wx.login({
      success: function (res) {
        wx.request({
          url: api.api_login+'?code='+`${res.code}`,
          header: {
            'Content-Type': 'application/json'
          },
          method: 'POST',
          success: function (res) {
            let data = res.data.data
            wx.setStorageSync('AccessToken', data.accessToken)
            wx.setStorageSync('LoginStatus', true)
            wx.setStorageSync('UserInfo', data.memberDetail)
            if (that.userInfoReadyCallback) {
              that.userInfoReadyCallback(res)
            }
          },
          fail: function (res) {
            console.log(res);
          }
        })
      }
    })
  },
  globalData: {
    userInfo: null
  }
})
