var api = require('./utils/api.js')
App({
  onLaunch: function () {
    wx.setStorageSync('LoginStatus', false)
    this.login()
  },
 
  login: function () {
    var that = this;
    wx.login({
      success: function (res) {
        wx.request({
          url: api.api_login+'?code='+`${res.code}`,
          header: {
            'Content-Type': 'application/json'
          },
          method: 'POST',
          success: function (res) {
            if (res.data.code === 200) {
              let data = res.data.data
              wx.setStorageSync('AccessToken', data.accessToken)
              wx.setStorageSync('LoginStatus', true)
              wx.setStorageSync('UserInfo', data.memberDetail)
              if (that.userInfoReadyCallback) {
                that.userInfoReadyCallback(res)
              }
            } else {
              // 登录失败，可能是新用户需要注册
              console.log('微信登录失败，需要注册:', res.data);
              wx.setStorageSync('LoginStatus', false)
              // 可以在这里引导用户到注册页面
              if (that.loginFailCallback) {
                that.loginFailCallback(res)
              }
            }
          },
          fail: function (res) {
            console.log('微信登录请求失败:', res);
            wx.setStorageSync('LoginStatus', false)
            if (that.loginFailCallback) {
              that.loginFailCallback(res)
            }
          }
        })
      }
    })
  },
  globalData: {
    userInfo: null
  }
})
