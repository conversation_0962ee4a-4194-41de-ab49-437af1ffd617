<template>
	<slide-verify-code ref="verifyCode" @onSubmit="onSubmit"></slide-verify-code>
	<div id="index">
		<el-card id="card">
			<el-form :rules="rules" ref="form" :model="dataModel">
				<el-form-item>
					<div style="height: 80px;display: flex;align-items: center;">
						<span class="mobile-title" style="font-size: 30px;font-weight: bold;color: #4472FC;">管理后台</span>
					</div>
				</el-form-item>
				<el-row>
					<el-col>
						<el-form-item prop="userName">
							<el-input placeholder="用户名" v-model="dataModel.userName">
								<template #prefix>
									<el-icon :size="20">
										<User></User>
									</el-icon>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col>
						<el-form-item prop="password">
							<el-input placeholder="密码" type="password" @keyup.enter="onVerify"
								v-model="dataModel.password">
								<template #prefix>
									<el-icon :size="20">
										<Lock></Lock>
									</el-icon>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col>
						<el-form-item>
							<el-button class="mobile-button" style="width: 100%; height: 50px;font-size: 20px;" type="primary"
								@click="onVerify">登 录
							</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script>
import JSEncrypt from 'jsencrypt'
import SlideVerifyCode from '@/components/verifyCode/slideVerifyCode.vue'
import { User, Lock } from '@element-plus/icons-vue'

import {
	authLogin,
	getVerifyCode
} from '@/api/system/auth'

import { reload } from '@/store/modules/user'
import mitt from "@/utils/mitt"

export default {
	components: {
		SlideVerifyCode,
		User,
		Lock
	},
	data() {
		var validatePassword = (_rule, value, callback) => {
			var reg = /^\S{6,18}$/
			if (value == undefined || value == null) {
				return callback(new Error('密码不能为空'));
			}
			if (!reg.test(value)) {
				return callback(new Error('密码长度为6-18位'));
			}
			callback()
		}
		return {
			dataModel: {},
			rules: {
				userName: [{
					required: true,
					message: '请输入用户名',
					trigger: 'blur'
				}],
				password: [{
					required: true,
					validator: validatePassword,
					trigger: 'blur'
				}],
			},
			dictList: [],
			total: 0
		}
	},
	methods: {
		onVerify() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					this.onVerifyCode()
				}
			})
		},
		onVerifyCode() {
			getVerifyCode()
				.then(res => {
					this.dataModel.secretKey = res.data.data.secretKey
					this.dataModel.token = res.data.data.token
					mitt.emit('openVerifyCode', {
						blockImage: res.data.data.blockImage,
						oriImage: res.data.data.oriImage
					})
				})
		},
		onSubmit(point) {
			sessionStorage.clear()
			var encrypt = new JSEncrypt()
			encrypt.setPublicKey(this.dataModel.secretKey)

			const params = {
				token: this.dataModel.token,
				userName: encrypt.encrypt(this.dataModel.userName),
				password: encrypt.encrypt(this.dataModel.password),
				point: encrypt.encrypt(String(point))
			}

			authLogin(params)
				.then(res => {
					localStorage.setItem("token", JSON.stringify(res.data.data))
					reload()
					mitt.emit('closeVerifyCode')
				})
				.catch(err => {
					// 关闭验证码弹窗
					mitt.emit('closeVerifyCode')

					// 显示错误信息
					const errorMessage = err.data?.errorMessage || '登录失败，请重试'
					this.$message.error(errorMessage)

					// 如果是验证码相关错误，重新获取验证码
					if (err.data?.code == 1003 || errorMessage.includes('验证') || errorMessage.includes('滑动')) {
						// 延迟一下再重新获取验证码，让用户看到错误信息
						setTimeout(() => {
							this.onVerifyCode()
						}, 1500)
					}
				})
		}
	},
	created() {
		// 清理可能存在的事件监听器
		mitt.off("openVerifyCode")
		mitt.off("closeVerifyCode")
	},

	beforeUnmount() {
		// 组件销毁前清理事件监听器
		mitt.off("openVerifyCode")
		mitt.off("closeVerifyCode")
	}
}
</script>

<style scoped>
#index {
	overflow: hidden;
	box-sizing: border-box;
	height: 100vh;
}

#card {
	width: 25vw;
	height: 50vh;
	margin: 10% auto;
}



::v-deep .el-input__inner {
	height: 50px !important;
	font-size: 17px;
}

::v-deep .el-input--small {
	line-height: 50px;
}

/* 修复前缀图标对齐 */
::v-deep .el-input__prefix {
	display: flex;
	align-items: center;
}

/* 修复暗色主题下的图标背景 */
::v-deep .dark-theme .el-input__prefix,
::v-deep .dark-theme .el-input__prefix *,
::v-deep .dark-theme .el-input__prefix .el-icon,
::v-deep .dark-theme .el-input__prefix .el-icon *,
::v-deep .dark-theme .el-input__prefix svg {
	background-color: transparent !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
	#card {
		width: 90%;
		height: auto;
		min-height: 400px;
		margin: 10vh auto;
		padding: 20px;
	}

	/* 移动端标题样式 */
	.mobile-title {
		font-size: 24px !important;
	}

	/* 移动端按钮样式 */
	.mobile-button {
		height: 45px !important;
		font-size: 18px !important;
	}
}
</style>