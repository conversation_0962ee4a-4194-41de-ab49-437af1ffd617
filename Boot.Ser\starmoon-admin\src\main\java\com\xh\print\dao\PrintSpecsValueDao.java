package com.xh.print.dao;

import com.xh.print.domain.entity.PrintSpecsValue;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 规格值表(PrintSpecsValue)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-07 15:34:33
 */
public interface PrintSpecsValueDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PrintSpecsValue queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param printSpecsValue 查询条件
     * @param pageable        分页对象
     * @return 对象列表
     */
    List<PrintSpecsValue> queryAllByLimit(PrintSpecsValue printSpecsValue, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param printSpecsValue 查询条件
     * @return 总行数
     */
    long count(PrintSpecsValue printSpecsValue);

    /**
     * 新增数据
     *
     * @param printSpecsValue 实例对象
     * @return 影响行数
     */
    int insert(PrintSpecsValue printSpecsValue);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PrintSpecsValue> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PrintSpecsValue> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PrintSpecsValue> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PrintSpecsValue> entities);

    /**
     * 修改数据
     *
     * @param printSpecsValue 实例对象
     * @return 影响行数
     */
    int update(PrintSpecsValue printSpecsValue);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    int delete();
}

