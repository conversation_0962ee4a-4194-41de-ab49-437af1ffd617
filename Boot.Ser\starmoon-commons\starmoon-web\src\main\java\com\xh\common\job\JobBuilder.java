package com.xh.common.job;

import java.util.concurrent.TimeUnit;

public class JobBuilder {
    private String jobName;
    private String jobGroupName;
    private Job job;
    private TimeUnit timeUnit;
    private int ttl;
    private boolean loop;

    public static JobBuilder builder(){
        return new JobBuilder();
    }

    public JobBuilder jobKey(String jobName,String jobGroupName){
        this.jobName = jobName;
        this.jobGroupName = jobGroupName;
        return this;
    }

    public JobBuilder job(Job job){
        this.job = job;
        return this;
    }

    public JobBuilder trigger(int ttl,TimeUnit timeUnit,boolean loop){
        this.ttl = ttl;
        this.timeUnit = timeUnit;
        this.loop = loop;
        return this;
    }

    public JobDetail build(){
        JobDetail jobDetail = new JobDetail();
        jobDetail.setTtl(ttl);
        jobDetail.setTimeUnit(timeUnit);
        jobDetail.setNext(loop);
        jobDetail.setJob(new Job());
        jobDetail.setJobName(jobName);
        jobDetail.setJobGroupName(jobGroupName);
        jobDetail.setUpdateTime(System.currentTimeMillis());
        return jobDetail;
    }

    public JobDetail build(JobDetail jobDetail){
        jobDetail.setUpdateTime(System.currentTimeMillis());
        return jobDetail;
    }

}
