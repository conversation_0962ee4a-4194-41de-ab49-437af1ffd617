package com.xh.system.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 地址表(Address)实体类
 * <AUTHOR> @since 2025-06-05
 */
@Data
public class Address implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "收件人")
    private String recipient;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区县")
    private String county;

    @Schema(description = "乡镇")
    private String town;

    @Schema(description = "详细地址")
    private String detail;

    @Schema(description = "默认地址")
    private Boolean isDefault;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;
} 