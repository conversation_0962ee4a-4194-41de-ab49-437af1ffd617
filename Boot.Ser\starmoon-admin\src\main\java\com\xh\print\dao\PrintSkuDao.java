package com.xh.print.dao;

import com.xh.print.domain.entity.PrintAttr;
import com.xh.print.domain.entity.PrintSku;

import java.util.List;

/**
 * 商品交易单元(PrintSku)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-07 15:33:55
 */
public interface PrintSkuDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PrintSku queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<PrintSku> queryByList();

    List<PrintAttr> queryAttrByList();

    /**
     * 新增数据
     *
     * @param printSku 实例对象
     * @return 影响行数
     */
    int insert(PrintSku printSku);


    /**
     * 修改数据
     *
     * @param printSku 实例对象
     * @return 影响行数
     */
    int update(PrintSku printSku);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    int delete();

}

