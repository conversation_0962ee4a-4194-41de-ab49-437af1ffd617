package com.xh.system.dao;

import com.xh.system.domain.entity.Dict;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * (Dict)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 13:11:10
 */
public interface DictDao {

    Dict queryById(Long id);

    List<Dict> queryByList(@Param("nameEn") String nameEn, @Param("nameCn") String nameCn, @Param("status") Integer status, @Param("parentId") Long parentId);

    int insert(Dict dict);

    int update(Dict dict);

    int deleteById(Long id);

    List<Dict> queryDictByNameEn(String nameEn);
}

