package com.xh.print.service.impl;

import com.xh.print.domain.entity.OrderDetail;
import com.xh.print.dao.OrderDetailDao;
import com.xh.print.domain.search.OrderDetailSearch;
import com.xh.print.service.OrderDetailService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 订单明细(OrderDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:51
 */
@Service
public class OrderDetailServiceImpl implements OrderDetailService {

    @Autowired
    private OrderDetailDao orderDetailDao;


    @Override
    public OrderDetail queryById(Long id) {
        return orderDetailDao.queryById(id);
    }


    @Override
    public List<OrderDetail> queryByList(OrderDetailSearch search) {
        return orderDetailDao.queryByList(search);
    }


    @Override
    public Long insert(OrderDetail orderDetail) {
        orderDetailDao.insert(orderDetail);
        return orderDetail.getId();
    }

    @Override
    public boolean update(OrderDetail orderDetail) {
        return orderDetailDao.update(orderDetail) > 0;
    }


    @Override
    public boolean deleteById(Long id) {
        return orderDetailDao.deleteById(id) > 0;
    }
}
