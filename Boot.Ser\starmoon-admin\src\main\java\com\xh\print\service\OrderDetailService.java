package com.xh.print.service;

import com.xh.print.domain.entity.OrderDetail;
import com.xh.print.domain.search.OrderDetailSearch;

import java.util.List;

/**
 * 订单明细(OrderDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:51
 */
public interface OrderDetailService {

    OrderDetail queryById(Long id);

    List<OrderDetail> queryByList(OrderDetailSearch orderDetailSearch);

    Long insert(OrderDetail orderDetail);

    boolean update(OrderDetail orderDetail);

    boolean deleteById(Long id);

}
