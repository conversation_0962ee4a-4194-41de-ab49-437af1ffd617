<!--pages/price/price.wxml-->
<view class="container">
  <custom-nav title="价格表" color="#333"></custom-nav>
  
  <!-- 价格表类型列表 -->
  <view class="price-list">
    <block wx:for="{{priceList}}" wx:key="id">
      <view class="price-item">
        <!-- 标题区域 -->
        <view class="price-header">
          <!-- 标题和描述 -->
          <view class="price-title-area">
            <view class="price-title">{{item.title}}</view>
            <view class="price-desc" wx:if="{{item.description}}">{{item.description}}</view>
          </view>
          
          <!-- 效果图按钮 -->
          <view class="effect-btn" catchtap="viewEffectImage" data-id="{{item.id}}">查看效果图</view>
        </view>
        
        <!-- 富文本内容区域 -->
        <view class="price-content">
          <rich-text nodes="{{item.content}}" space="nbsp"></rich-text>
        </view>
      </view>
    </block>
  </view>
  
  <!-- 暂无数据提示 -->
  <view class="empty-tip" wx:if="{{priceList.length === 0 && !isLoading}}">
    <text>暂无价格表数据</text>
  </view>
</view>