package com.xh.common.mybatis.interceptor;

import com.xh.common.mybatis.number.NumberGenerate;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.math.BigInteger;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Slf4j
@Intercepts({
        @Signature(type = Executor.class,method = "query",args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class,method = "query",args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class,method = "update",args = {MappedStatement.class, Object.class}),
})
public class ParamsInterceptor implements Interceptor {

    private NumberGenerate numberGenerate;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement)args[0];
        Object param = args[1];

        Map<String,Object> map;
        if(param == null){
            map = new HashMap<>();
        }else if(param instanceof Map){
            map = (Map)param;
        }else if(param.getClass().isPrimitive()
                || param.getClass().isArray()
                || param instanceof String
                || param instanceof Integer
                || param instanceof Double
                || param instanceof Float
                || param instanceof Long
                || param instanceof Boolean
                || param instanceof Byte
                || param instanceof Short
                || param instanceof Collection){
            map = new HashMap<>();
            String id = mappedStatement.getId();
            String clazzName = id.substring(0, id.lastIndexOf("."));
            String methodName  = id.substring(id.lastIndexOf(".")+1);
            Class<?> clazz = Class.forName(clazzName);
            for(Method method: clazz.getMethods()){
                if(method.getName().equals(methodName)){
                    Parameter parameter = method.getParameters()[0];
                    Param annotation = parameter.getAnnotation(Param.class);
                    if(annotation != null){
                        map.put(annotation.value(),param);
                    }else {
                        map.put(parameter.getName(), param);
                    }
                }
            }
        }else {
            map = new HashMap<>();
            Class<?> clazz = param.getClass();
            while (clazz != null) {
                Field[] declaredFields = clazz.getDeclaredFields();
                for(Field field: declaredFields){
                    field.setAccessible(true);
                    if(!map.containsKey(field.getName())){
                        map.put(field.getName(),field.get(param));
                    }
                }
                clazz = clazz.getSuperclass();
            }
        }
        map.put("params.data_permission"," and org_id = 1");
//        args[1] = map;
        BoundSql boundSql = mappedStatement.getBoundSql(map);
        Object proceed = invocation.proceed();
        String[] keyProperties = mappedStatement.getKeyProperties();

        MetaObject metaObject = SystemMetaObject.forObject(param);
        if(keyProperties != null){
            for(String key: keyProperties){
                Object value = map.get(key);
                if(value instanceof BigInteger bigInteger){
                    long longValue = bigInteger.longValue();
                    metaObject.setValue(key,longValue);
                }else {
                    metaObject.setValue(key,value);
                }
            }
        }
        return proceed;
    }



}
