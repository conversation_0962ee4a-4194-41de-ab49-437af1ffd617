import router from '@/router/index'

import {
	currentUser
} from "@/api/system/user"
import {
	loadMenu
} from '@/api/system/menu'


const modules = import.meta.glob("../../views/**/*.vue")

const data = {
	userinfo: {},
	permissions: [],
	logined: false
}

const reload = () => {
	onLoad(true)
}


const getChildren = (route, routerList) => {
	let list = []
	if (routerList == null || routerList.length == 0) {
		return list
	}
	for (let item of routerList) {
		if (route.id == item.parentId) {
			list.push({
				path: item.path,
				menuName: item.menuName,
				sort: item.sort,
				children: getChildren(item, routerList),
				icon: item.icon,
				type: item.type
			})
		}
	}
	return list.sort((a, b) => a.sort - b.sort)
}


async function onLoad(toPath) {
	var token = localStorage.getItem("token")
	if (token) {
		try{
			const [user_res, permission_res] = await Promise.all([
				currentUser(),
				loadMenu()
			])
			data.logined = true
			data.userinfo = user_res.data.data
			data.permissions = permission_res.data.data

			for (let item of data.permissions) {
				const component = modules['../../views/' + item.componentPath + '.vue']
				if (component) {
					router.addRoute('index', {
						path: '/' + item.path,
						component: component
					})
				}
			}

			let list = []
			for (let item of data.permissions) {
				if (item.parentId == 0) {
					list.push({
						path: item.path,
						menuName: item.menuName,
						sort: item.sort,
						children: getChildren(item, data.permissions),
						icon: item.icon,
						type: item.type
					})
				}
			}
			data.treePermissions = list.sort((a, b) => a.sort - b.sort)
			if(toPath){
				router.push("/index")
			}
		}catch(err){
			data.logined = false
		}
	} else {
		data.logined = false
	}
}


await onLoad(false)


router.beforeEach((to, from, next) => {
	if(to.path == "/login" && data.logined){
		return next("/home")
	}else if(to.path != "/login" && !data.logined) {
		localStorage.clear()
		return next("/login")
	}
	return next()
})

export default data

export {
	reload
}