<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xh</groupId>
    <artifactId>Boot.Ser</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>starmoon-admin</module>
        <module>starmoon-commons</module>
    </modules>

    <!--版本管理-->
    <properties>
        <project.encoding>UTF-8</project.encoding>
        <java.version>21</java.version>
        <maven-compiler-plugin.version>3.12.0</maven-compiler-plugin.version>
        <!-- 核心依赖 -->
        <spring-boot.version>3.4.3</spring-boot.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>

        <!--AI依赖-->
        <spring-ai.version>1.0.0-M7</spring-ai.version>
        <alibaba-ai-dashscope-sdk.version>2.19.0</alibaba-ai-dashscope-sdk.version>

        <mybatis-spring-boot-starter.version>3.0.3</mybatis-spring-boot-starter.version>
        <pagehelper-spring-boot-starter.version>2.1.0</pagehelper-spring-boot-starter.version>
        <rocketmq-springboot.version>2.3.2</rocketmq-springboot.version>
        <shardingsphere-jdbc.version>5.5.2</shardingsphere-jdbc.version>
        <fastjson.version>2.0.32</fastjson.version>
        <httpclient.version>4.5.14</httpclient.version>
        <!-- 文件服务工具-->
        <minio.version>8.5.12</minio.version>

    </properties>
    <!--依赖管理-->
    <dependencyManagement>
        <dependencies>
            <!-- 核心依赖 -->
            <!--spring boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <!--spring ai-->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <version>${shardingsphere-jdbc.version}</version>
            </dependency>
            <!-- 文件服务工具-->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

    </dependencies>


    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>spring-snapshots</id>
            <url>https://repo.spring.io/snapshot</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>static/**</include>
                    <include>**/*.yml</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <!-- 解决匿名类找不到符号-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.encoding}</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>