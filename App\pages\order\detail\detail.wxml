<scroll-view class="scroll-container" scroll-y="true" enhanced="true" show-scrollbar="false">
  <view class="container">
    <custom-nav title="订单详情" color="#333" showBack="{{true}}"></custom-nav>
    
    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-content">
        <text class="status-text">{{order.statusText}}</text>
        <text class="status-desc" wx:if="{{order.status === 0}}">请尽快完成支付</text>
        <text class="status-desc" wx:elif="{{order.status === 1}}">您的订单即将送达</text>
        <text class="status-desc" wx:elif="{{order.status === 3}}">感谢您的使用</text>
      </view>
    </view>
    
    <!-- 订单信息 -->
    <view class="info-card">
      <view class="card-title">订单信息</view>
      <view class="info-item">
        <text class="info-label">订单编号</text>
        <text class="info-value">{{order.id}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">下单时间</text>
        <text class="info-value">{{order.createTimeFormatted || order.createTime}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">支付方式</text>
        <text class="info-value">微信支付</text>
      </view>
    </view>
    
    <!-- 配送信息 -->
    <view class="info-card">
      <view class="card-title">
        <text>配送信息</text>
        <view wx:if="{{order.status === 0}}" class="modify-address-btn" bindtap="modifyAddress">修改地址</view>
      </view>
      <view class="address-info">
        <view class="user-info">
          <text class="user-name">{{order.addressInfo.recipient}}</text>
          <text class="user-phone">{{order.addressInfo.phone}}</text>
        </view>
        <view class="address-detail">{{order.addressInfo.province}}{{order.addressInfo.city}}{{order.addressInfo.county}}{{order.addressInfo.detail}}</view>
      </view>
    </view>
    
    <!-- 订单内容 -->
    <view class="info-card">
      <view class="card-title">订单内容</view>
      <view class="goods-list">
        <view class="goods-item" wx:for="{{order.detailList}}" wx:key="id">
          <view class="goods-info">
            <view class="goods-name">{{item.fileName}}</view>
            <view class="goods-params">{{item.specs}} 第{{item.pageNum}}-{{item.pageSize}}页</view>
          </view>
          <view class="goods-price-info">
            <text class="goods-price">¥{{item.unitPrice}}</text>
            <text class="goods-count">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
      
      <!-- 价格明细 -->
      <view class="price-detail">
        <view class="price-item">
          <text>商品金额</text>
          <text>¥{{order.productAmount}}</text>
        </view>
        <view class="price-item">
          <text>配送费</text>
          <text>¥{{order.shippingAmount || '0.00'}}</text>
        </view>
        <view class="price-item discount" wx:if="{{order.discountAmount > 0}}">
          <text>优惠</text>
          <text>-¥{{order.discountAmount}}</text>
        </view>
        <view class="price-item total">
          <text>实付款</text>
          <text class="total-price">¥{{order.payAmount}}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮占位，防止内容被固定按钮遮挡 -->
    <view class="bottom-placeholder" wx:if="{{order.status === 0}}"></view>
  </view>
</scroll-view>

<!-- 底部按钮 -->
<view class="bottom-btns" wx:if="{{order.status === 0}}">
  <button class="btn cancel-btn" bindtap="cancelOrder">取消订单</button>
  <button class="btn pay-btn" bindtap="payOrder">立即支付</button>
</view> 