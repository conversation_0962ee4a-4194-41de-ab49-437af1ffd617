<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.print.dao.OrderDetailDao">

    <resultMap type="com.xh.print.domain.entity.OrderDetail" id="OrderDetailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="skuId" column="sku_id" jdbcType="INTEGER"/>
        <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="unit_price" jdbcType="NUMERIC"/>
        <result property="payAmount" column="pay_amount" jdbcType="NUMERIC"/>
        <result property="quantity" column="quantity" jdbcType="INTEGER"/>
        <result property="pageNum" column="page_num" jdbcType="INTEGER"/>
        <result property="pageSize" column="page_size" jdbcType="INTEGER"/>
        <result property="specs" column="specs" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="image" column="image" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryById" resultMap="OrderDetailMap">
        select *
        from order_detail
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="OrderDetailMap">
        select
        *
        from order_detail
        <where>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
        </where>
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null ">
                order_id,
            </if>
            <if test="skuId != null ">
                sku_id,
            </if>
            <if test="filePath != null  and '' != filePath">
                file_path,
            </if>
            <if test="fileName != null  and '' != fileName">
                file_name,
            </if>
            <if test="fileType != null  and '' != fileType">
                file_type,
            </if>
            <if test="unitPrice != null ">
                unit_price,
            </if>
            <if test="payAmount != null ">
                pay_amount,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="pageNum != null ">
                page_num,
            </if>
            <if test="pageSize != null ">
                page_size,
            </if>
            <if test="specs != null  and '' != specs">
                specs,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="image != null  and '' != image">
                image,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null ">
                #{orderId},
            </if>
            <if test="skuId != null ">
                #{skuId},
            </if>
            <if test="filePath != null  and '' != filePath">
                #{filePath},
            </if>
            <if test="fileName != null  and '' != fileName">
                #{fileName},
            </if>
            <if test="fileType != null  and '' != fileType">
                #{fileType},
            </if>
            <if test="unitPrice != null ">
                #{unitPrice},
            </if>
            <if test="payAmount != null ">
                #{payAmount},
            </if>
            <if test="quantity != null ">
                #{quantity},
            </if>
            <if test="pageNum != null ">
                #{pageNum},
            </if>
            <if test="pageSize != null ">
                #{pageSize},
            </if>
            <if test="specs != null  and '' != specs">
                #{specs},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="image != null  and '' != image">
                #{image},
            </if>
        </trim>
    </insert>

    <update id="update">
        update order_detail
        <set>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId},
            </if>
            <if test="filePath != null and filePath != ''">
                file_path = #{filePath},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="fileType != null and fileType != ''">
                file_type = #{fileType},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="pageNum != null">
                page_num = #{pageNum},
            </if>
            <if test="pageSize != null">
                page_size = #{pageSize},
            </if>
            <if test="specs != null and specs != ''">
                specs = #{specs},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="image != null and image != ''">
                image = #{image},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete
        from order_detail
        where id = #{id}
    </delete>

</mapper>

