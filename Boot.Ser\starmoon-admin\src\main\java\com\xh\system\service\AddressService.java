package com.xh.system.service;

import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.AddressCreateDTO;
import com.xh.system.domain.dto.AddressEditDTO;
import com.xh.system.domain.entity.Address;

public interface AddressService {

    Address queryById(Long id);

    PageInfo<Address> queryByPage(Integer pageNum, Integer pageSize, Long userId);

    Long insert(AddressCreateDTO dto);

    boolean update(AddressEditDTO dto);

    boolean deleteById(Long id);
} 