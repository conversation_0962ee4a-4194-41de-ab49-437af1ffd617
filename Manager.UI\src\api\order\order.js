import request from '@/utils/request'

// 分页查询订单列表
export function listOrder(params) {
  return request({ url: '/manage-api/v1/order/page', method: 'get', params })
}

// 通过ID查询订单
export function getOrder(id) {
  return request({ url: '/manage-api/v1/order', method: 'get', params: { id } })
}

// 新增订单
export function addOrder(data) {
  return request({ url: '/manage-api/v1/order', method: 'post', data })
}

// 编辑订单
export function editOrder(data) {
  return request({ url: '/manage-api/v1/order', method: 'put', data })
}

// 删除订单
export function deleteOrder(id) {
  return request({ url: '/manage-api/v1/order', method: 'delete', params: { id } })
} 