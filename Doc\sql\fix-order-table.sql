-- 修复order表结构 - 移除废弃字段
-- 执行日期：2025年1月

-- 1. 删除废弃的支付状态字段（如果存在）
ALTER TABLE `order` DROP COLUMN IF EXISTS `pay_status`;

-- 2. 确保产品金额字段存在
ALTER TABLE `order` ADD COLUMN IF NOT EXISTS `product_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '产品金额' AFTER `shipping_amount`;

-- 3. 修改address字段类型，确保能存储完整的JSON地址信息
ALTER TABLE `order` MODIFY COLUMN `address` TEXT COMMENT '地址JSON';

-- 4. 验证表结构
-- 执行完成后，可以用以下语句验证表结构：
-- DESCRIBE `order`;

-- 说明：订单状态统一使用status字段管理
-- status: 0=待支付, 1=待配送, 2=配送中, 3=已完成, 4=已取消 