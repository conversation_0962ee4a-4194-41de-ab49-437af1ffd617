package com.xh.system.dao;

import com.xh.system.domain.entity.Imagetext;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统图文表(Imagetext)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-21 08:30:39
 */
public interface ImagetextDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Imagetext queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<Imagetext> queryByList(@Param("type") String type);


    /**
     * 新增数据
     *
     * @param imagetext 实例对象
     * @return 影响行数
     */
    int insert(Imagetext imagetext);

    /**
     * 修改数据
     *
     * @param imagetext 实例对象
     * @return 影响行数
     */
    int update(Imagetext imagetext);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

