<template>
  <div class="page-container">
    <div class="page-content">
      <member-edit
        @search="search"
      ></member-edit>
      <div class="card card--search">
        <div class="search-container">
          <div class="search-form">
            <el-input
              v-model="searchModel.keyword"
              placeholder="用户名|昵称"
              clearable
              style="width: 200px;"
            />
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="search" class="search-btn">搜索</el-button>
            <el-button type="primary" @click="add" class="add-btn">添加</el-button>
          </div>
        </div>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table
            stripe
            :data="userList"
            style="width: 100%;"
            class="data-table"
            fit
            
          >
            <el-table-column prop="id" label="ID" align="center"/>
            <el-table-column prop="openid" align="center" label="openid" />
            <el-table-column prop="avatarUrl" align="center" label="头像" />
            <el-table-column prop="nickName" align="center" label="昵称" />
            <el-table-column prop="phone" align="center" label="手机号" />
            <el-table-column prop="createTime" align="center" label="创建时间" />
            <el-table-column prop="updateTime" align="center" label="更新时间" />
            <el-table-column align="center" width="160" label="操作">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="edit(scope.row)" class="action-btn">编辑</el-button>
                <el-button type="danger" link size="small" @click="deleted(scope.row.id)" class="action-btn">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination
            background
            layout="prev, pager, next"
            @current-change="currentChange"
            @prev-click="prevClick"
            @next-click="nextClick"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>

</template>
<script>
import { listmember} from "@/api/system/member";
import { listDictByNameEn } from "@/api/system/dict";
import mitt from "@/utils/mitt";
import memberEdit from "@/components/system/memberEdit.vue";
export default {
  components: { memberEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
      },
      userList: [],
  
    };
  },
  methods: {
    handleClose() {
      this.dialog.show = false;
    },
    search() {
      listmember(this.searchModel)
        .then((res) => {
          this.userList = res.data.data.list;
          this.total = res.data.data.total;
        })
        .catch((err) => {
          this.$message.error(err.data.errorMessage);
        });
    },

    edit(row) {
      this.dialog.show = true;
      this.dialog.title = "编辑用户";
      this.userModel = { ...row };
    },
    submit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          console.log("提交");
          console.log(this.userModel);
        }
      });
    },
    deleted(id) {
      this.$confirm("删除用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteUser(id)
            .then((res) => {
              this.search();
              this.$message.success("操作成功");
            })
            .catch((err) => {
              this.$message.error(err.data.errorMessage);
            });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(newPage) {
      this.searchModel.pageNum = newPage;
      this.search();
    },
    nextClick(newPage) {
      this.searchModel.pageNum = newPage;
      this.search();
    },
    formatStatus(row, column, cellValue, index) {
      let result = "";
      for (let item of this.statusList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
          break;
        }
      }
      return result;
    },
    formatRole(row, column, cellValue, index) {
      let result = "";
      for (let item of this.roleList) {
        if (item.id == cellValue) {
          result = item.roleName;
          break;
        }
      }
      return result;
    },
    formatSex(row, column, cellValue, index) {
      let result = "";
      for (let item of this.sexList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
          break;
        }
      }
      return result;
    },
    async init() {
      try {
        const [user_res] = await Promise.all([
        listmember(this.searchModel),
        ]);
    
        this.userList = user_res.data.data.list;
        this.total = user_res.data.data.total;
      } catch (err) {
        this.$message.error(err.data.errorMessage);
      }
    },
  },
  created() {
    this.init();
  },
  unmounted() {
    mitt.off("openUserAdd");
    mitt.off("openUserEdit");
  },
};
</script>

<style scoped>
.user-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.card--table {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-top: 0;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.search-flex {
  display: flex;
  align-items: center;
}

.card--search {
  margin-bottom: 20px;
  flex: none;
  height: auto;
  padding: 20px 20px;
  display: flex;
  align-items: center;
}
</style> 