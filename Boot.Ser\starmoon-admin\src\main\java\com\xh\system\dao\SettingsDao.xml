<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.SettingsDao">

    <resultMap type="com.xh.system.entity.Settings" id="SettingsMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="settingsKey" column="settings_key" jdbcType="VARCHAR"/>
        <result property="settingValue" column="setting_value" jdbcType="VARCHAR"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryById" resultMap="SettingsMap">
        select id,
               settings_key,
               setting_value,
               note,
               create_time,
               update_time
        from sys_settings
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="SettingsMap">
        select
        id, settings_key, setting_value, note, create_time, update_time
        from sys_settings
        <where>
            <if test="settingsKey != null and settingsKey != ''">
                and settings_key = #{settingsKey}
            </if>
        </where>

    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO sys_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="settingsKey != null  and '' != settingsKey">
                settings_key,
            </if>
            <if test="settingValue != null  and '' != settingValue">
                setting_value,
            </if>
            <if test="note != null  and '' != note">
                note,
            </if>
            create_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="settingsKey != null  and '' != settingsKey">
                #{settingsKey},
            </if>
            <if test="settingValue != null  and '' != settingValue">
                #{settingValue},
            </if>
            <if test="note != null  and '' != note">
                #{note},
            </if>
                now()
        </trim>
    </insert>

    <update id="update">
        update sys_settings
        <set>
            <if test="settingsKey != null and settingsKey != ''">
                settings_key = #{settingsKey},
            </if>
            <if test="settingValue != null and settingValue != ''">
                setting_value = #{settingValue},
            </if>
            <if test="note != null and note != ''">
                note = #{note},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete
        from sys_settings
        where id = #{id}
    </delete>

</mapper>

