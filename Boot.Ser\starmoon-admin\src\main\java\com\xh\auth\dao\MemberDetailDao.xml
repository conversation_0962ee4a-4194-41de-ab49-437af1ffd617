<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.auth.dao.MemberDetailDao">

    <resultMap type="com.xh.common.wechat.user.MemberDetail" id="MemberDetailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="openid" column="openid" jdbcType="VARCHAR"/>
        <result property="unionid" column="unionid" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="realName" column="real_name" jdbcType="VARCHAR"/>
        <result property="avatarUrl" column="avatar_url" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryByOpenid" resultMap="MemberDetailMap">
        SELECT * FROM t_member WHERE openid = #{openid}
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO t_member(openid, unionid, phone, role, create_time)
        VALUES (#{openid}, #{unionid}, #{phone}, #{role}, now())
    </insert>

    <update id="update">
        update t_member
        <set>
            <if test="unionid != null and unionid != ''">
                unionid = #{unionid},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="realName != null and realName != ''">
                real_name = #{realName},
            </if>
            <if test="avatarUrl != null and avatarUrl != ''">
                avatar_url = #{avatarUrl},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="birthday != null">
                birthday = #{birthday},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="role != null">
                `role` = #{role},
            </if>
        </set>
        where openid = #{openid}
    </update>
</mapper>

