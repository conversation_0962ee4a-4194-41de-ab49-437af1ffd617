package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OrgDTO {

    @Schema(description = "组织ID")
    private Long id;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "祖级列表/分割")
    private String ancestors;

    @Schema(description = "子级")
    private List<OrgDTO> children;
}
