<view class="container">
  <custom-nav title="{{isEdit ? '编辑地址' : '新增地址'}}" color="#333" showBack="{{true}}"></custom-nav>
  
  <view class="form-card">
    <view class="form-item">
      <view class="form-label">收货人</view>
      <input class="form-input" placeholder="请输入收货人姓名" value="{{address.recipient}}" bindinput="inputName" />
    </view>
    
    <view class="form-item">
      <view class="form-label">手机号码</view>
      <input class="form-input" type="number" maxlength="11" placeholder="请输入手机号码" value="{{address.phone}}" bindinput="inputPhone" />
    </view>
    
    <view class="form-item">
      <view class="form-label">所在地区</view>
      <picker mode="region" bindchange="regionChange" value="{{[address.province, address.city, address.county]}}">
        <view class="picker-content">
          <text wx:if="{{address.province}}">{{address.province}} {{address.city}} {{address.county}}</text>
          <text wx:else class="placeholder">请选择所在地区</text>
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="form-label">详细地址</view>
      <input class="form-textarea" placeholder="请输入详细地址" value="{{address.detail}}" bindinput="inputDetailAddress"></input>
    </view>
    
    <view class="form-item switch-item">
      <view class="form-label">设为默认地址</view>
      <switch checked="{{address.isDefault}}" bindchange="switchDefault" color="#ff6633"></switch>
    </view>
  </view>
  
  <view class="btn-container">
    <button class="save-btn" bindtap="saveAddress">保存</button>
  </view>
</view> 