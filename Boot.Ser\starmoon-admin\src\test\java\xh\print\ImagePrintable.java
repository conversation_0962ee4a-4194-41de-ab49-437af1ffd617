package xh.print;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.print.PageFormat;
import java.awt.print.Paper;
import java.awt.print.Printable;
import java.awt.print.PrinterException;

public class ImagePrintable implements Printable {

    private BufferedImage image;

    public ImagePrintable(BufferedImage image) {
        this.image = image;
    }

    @Override
    public int print(Graphics graphics, PageFormat pageFormat, int pageIndex) throws PrinterException {
        Graphics2D g2d = (Graphics2D) graphics;

        Paper paper = pageFormat.getPaper();
        // 整张纸张大小（pt）
        double paperW = paper.getWidth();
        double paperH = paper.getHeight();
        // 可打印区域原点及大小（pt）
        double ix = paper.getImageableX();
        double iy = paper.getImageableY();
        double iw = paper.getImageableWidth();
        double ih = paper.getImageableHeight();
        // 边距（pt），再转成 mm
        double leftMm   = ix   * 25.4 / 72.0;
        double topMm    = iy   * 25.4 / 72.0;

        // 绘制图像
        g2d.drawImage(image, (int)leftMm+1, (int)topMm, (int)iw-1, (int)ih, null);
        return Printable.PAGE_EXISTS;
    }
}
