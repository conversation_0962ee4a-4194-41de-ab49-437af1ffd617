package com.xh.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.xh.system.dao.OrgDao;
import com.xh.system.domain.dto.OrgCreateDTO;
import com.xh.system.domain.dto.OrgDTO;
import com.xh.system.domain.dto.OrgEditDTO;
import com.xh.system.domain.entity.Org;
import com.xh.system.service.OrgService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * (UserGroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 09:45:38
 */
@Service
public class OrgServiceImpl implements OrgService {

    @Autowired
    private OrgDao orgDao;

    @Override
    public Org queryById(Long id) {
        return this.orgDao.queryById(id);
    }

    @Override
    public List<Org> queryByList(String orgName, Integer status) {
        return orgDao.queryByList(orgName,status,null);
    }

    @Override
    public  PageInfo<OrgDTO> queryByPage(String orgName, Integer status) {
        List<Org> rootList = orgDao.queryByList(orgName,status,0L);
        PageInfo<Org> pageInfo = new PageInfo<>(rootList);

        List<Org> orgList = orgDao.queryByList(orgName,status,null);
        List<OrgDTO> collect = rootList.stream().map(item -> {
            OrgDTO dto = new OrgDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setChildren(getChildren(item.getId(), orgList));
            return dto;
        }).collect(Collectors.toList());

        PageInfo<OrgDTO> info = new PageInfo<>(collect);
        info.setTotal(pageInfo.getTotal());
        return info;
    }


    @Override
    public Long insert(OrgCreateDTO dto) {
        Org org = new Org();
        BeanUtils.copyProperties(dto,org);
        orgDao.insert(org);
        return org.getId();
    }

    @Override
    public boolean update(OrgEditDTO dto) {
        Org org = new Org();
        BeanUtils.copyProperties(dto,org);

        return orgDao.update(org) > 0;
    }



    @Override
    public boolean deleteById(Long id) {

        return orgDao.deleteById(id) > 0;
    }

    private List<OrgDTO> getChildren(Long parentId, List<Org> orgList) {
        List<Org> children = orgList.stream().filter(temp -> temp.getParentId().intValue() == parentId.intValue()).toList();
        return children.stream().map(item ->{
            OrgDTO dto = new OrgDTO();
            BeanUtils.copyProperties(item,dto);
            dto.setChildren(getChildren(item.getId(),orgList));
            return dto;
        }).collect(Collectors.toList());
    }
}
