package com.xh.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.RoleCreateDTO;
import com.xh.system.domain.dto.RoleEditDTO;
import com.xh.system.domain.entity.Role;
import com.xh.system.domain.vo.RoleVO;
import com.xh.system.service.RoleService;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.web.domain.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 角色信息表(Role)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-20 09:48:26
 */
@Tag(name = "管理端，系统模块")
@RestController
@RequestMapping("manage-api/v1/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @Parameter(name = "id", description = "角色ID",required = true)
    @Operation(summary = "通过ID查询角色")
    @PreAuthorize("hasAuthority('sys:role:query')")
    @GetMapping
    public ResponseEntity<RoleVO> queryById(Long id) {
        return ResponseEntity.success(roleService.queryById(id), RoleVO.class);
    }

    @Operation(summary = "通过分页查询角色")
    @GetMapping("page")
    @PreAuthorize("hasAuthority('sys:role:query')")
    public ResponseEntity<PageInfo<Role>> queryByPage(@RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                        @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                                        @RequestParam(value = "roleName", required = false) String roleName,
                                                        @RequestParam(value = "status", required = false) Integer status
    ) {
        PageHelper.startPage(pageNum, pageSize);
        PageHelper.orderBy("sort asc");
        PageInfo<Role> pageInfo = roleService.queryByPage(roleName, status);
        return ResponseEntity.success(pageInfo);
    }

    @Operation(summary = "添加角色")
    @PostMapping
    @PreAuthorize("hasAuthority('sys:role:add')")
    @OperationLog(title = "添加角色")
    public ResponseEntity<Long> insert(@Valid @RequestBody RoleCreateDTO dto) {
        return ResponseEntity.success(roleService.insert(dto));
    }

    @Operation(summary = "编辑角色")
    @PutMapping
    @PreAuthorize("hasAuthority('sys:role:edit')")
    @OperationLog(title = "编辑角色")
    public ResponseEntity<Boolean> update(@Valid @RequestBody RoleEditDTO dto) {
        return ResponseEntity.success(roleService.update(dto));
    }

    @Operation(summary = "删除角色")
    @Parameter(name = "id", description = "角色ID",required = true)
    @DeleteMapping
    @PreAuthorize("hasAuthority('sys:role:delete')")
    @OperationLog(title = "删除角色")
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(roleService.deleteById(id));
    }

}