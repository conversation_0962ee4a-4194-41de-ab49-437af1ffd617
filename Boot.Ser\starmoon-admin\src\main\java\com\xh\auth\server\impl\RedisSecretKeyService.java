package com.xh.auth.server.impl;

import com.xh.auth.server.SecretKeyService;
import com.xh.common.utils.ObjectUtils;
import com.xh.common.utils.SecretUtils;
import com.xh.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Service;

import java.security.KeyPair;

@Service
public class RedisSecretKeyService implements SecretKeyService {

    private static String SECRET_KEY = "secretKey:";

    private RedisConnection redisConnection;

    @Autowired
    public RedisSecretKeyService(RedisConnectionFactory redisConnectionFactory){
        this.redisConnection = redisConnectionFactory.getConnection();
    }

    @Override
    public KeyPair generateKeyPair(String key) {
        byte[] secretKeyKey = (SECRET_KEY+key).getBytes();
        KeyPair keyPair = SecretUtils.generateKeyPair(1024);
        redisConnection.set(secretKeyKey, ObjectUtils.objectToByteArray(keyPair));
        redisConnection.expire(secretKeyKey,60);

        return keyPair;
    }

    @Override
    public KeyPair keyPair(String key) {
        byte[] secretKeyKey = (SECRET_KEY+key).getBytes();
        try {
            KeyPair keyPair = ObjectUtils.byteAryToObject(redisConnection.get(secretKeyKey), KeyPair.class);
            redisConnection.del(secretKeyKey);
            return keyPair;
        }catch (Exception e){
            return null;
        }
    }

}
