package com.xh.system.controller;

import com.github.pagehelper.PageInfo;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.system.domain.dto.MemberEditDTO;
import com.xh.system.domain.entity.Member;
import com.xh.system.service.MemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 客户端用户表(Member)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-21 20:29:36
 */
@Tag(name = "管理端，C端用户管理")
@RestController
@RequestMapping("manage-api/v1/member")
public class MemberController {
   
    @Autowired
    private MemberService memberService;

    @Operation(summary = "通过分页查询用户")
    @GetMapping("page")
    public ResponseEntity<PageInfo<Member>> queryByPage() {
        return ResponseEntity.success(memberService.queryByPage());
    }


    @Parameter(name = "id", description = "用户ID", required = true)
    @Operation(summary = "通过ID查询用户")
    @GetMapping
    public ResponseEntity<Member> queryById(Long id) {
        return ResponseEntity.success(memberService.queryById(id));
    }

    @Operation(summary = "编辑用户")
    @PutMapping
    public ResponseEntity<Boolean> update(MemberEditDTO dto) {
        return ResponseEntity.success(memberService.update(dto));
    }

    @Parameter(name = "id", description = "小程序用户ID", required = true)
    @Operation(summary = "删除用户")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(memberService.deleteById(id));
    }

}

