package com.xh.print.service;

import com.xh.print.domain.dto.TempFileDTO;
import com.xh.print.domain.entity.TempFile;
import com.xh.print.domain.form.TempFileEditForm;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * (TempFile)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-21 15:47:08
 */
public interface TempFileService {

    TempFile queryById(Long id);

    TempFileDTO queryDetailById(Long id);

    List<TempFile> queryByList();

    Long insert(MultipartFile file,String fileName);

    Long insert(TempFile tempFile);

    boolean update(TempFileEditForm form);

    boolean update(TempFile tempFile);

    boolean deleteById(Long id);

}
