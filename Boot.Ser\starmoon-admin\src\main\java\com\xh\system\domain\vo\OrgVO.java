package com.xh.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OrgVO {

    @Schema(description = "组织ID")
    private Long id;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "子级")
    private List<OrgVO> children;

}
