package com.xh.system.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (Settings)实体类
 *
 * <AUTHOR>
 * @since 2025-06-13 21:25:45
 */
@Data
public class Settings implements Serializable {
    private static final long serialVersionUID = 698536532111108073L;

    private Long id;

    private String settingsKey;

    private String settingValue;

    private String note;

    private Date createTime;

    private Date updateTime;


}

