package com.xh.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

public class SmsUtils {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    public static String sendSms(String appid,String appSecret,String signName,String templateCode, String phone, String templateParam){

        String httpMethod = "POST";
        String canonicalUri = "/";
        String host = "dysmsapi.aliyuncs.com";
        String xAcsAction = "SendSms";
        String xAcsVersion = "2017-05-25";

        TreeMap<String, String> headers = new TreeMap<>();
        headers.put("host", host);
        headers.put("x-acs-action", xAcsAction);
        headers.put("x-acs-version", xAcsVersion);
        // 设置日期格式化时区为GMT
        SDF.setTimeZone(new SimpleTimeZone(0, "GMT"));
        headers.put("x-acs-date", SDF.format(new Date()));
        headers.put("x-acs-signature-nonce", UUID.randomUUID().toString());

        TreeMap<String, Object> queryParam = new TreeMap<>();
        queryParam.put("SignName", signName);
        queryParam.put("TemplateCode", templateCode);
        queryParam.put("PhoneNumbers", phone);
        queryParam.put("TemplateParam", templateParam);

        // 签名过程
        try {

            // 步骤 1：拼接规范请求串
            // 请求参数，当请求的查询字符串为空时，使用空字符串作为规范化查询字符串
            StringBuilder canonicalQueryString = new StringBuilder();
            queryParam.entrySet().stream().map(entry -> percentCode(entry.getKey()) + "="
                    + percentCode(String.valueOf(entry.getValue()))).forEachOrdered(queryPart -> {
                // 如果canonicalQueryString已经不是空的，则在查询参数前添加"&"
                if (canonicalQueryString.length() > 0) {
                    canonicalQueryString.append("&");
                }
                canonicalQueryString.append(queryPart);
            });

            // 计算请求体的哈希值  // 请求体，当请求正文为空时，比如GET请求，RequestPayload固定为空字符串
            String requestPayload = "";
            String hashedRequestPayload = sha256Hex(requestPayload.getBytes(StandardCharsets.UTF_8));
            headers.put("x-acs-content-sha256", hashedRequestPayload);
            // 构造请求头，多个规范化消息头，按照消息头名称（小写）的字符代码顺序以升序排列后拼接在一起
            StringBuilder canonicalHeaders = new StringBuilder();
            // 已签名消息头列表，多个请求头名称（小写）按首字母升序排列并以英文分号（;）分隔
            StringBuilder signedHeadersSb = new StringBuilder();
            headers.entrySet().stream().filter(entry -> entry.getKey().toLowerCase().startsWith("x-acs-") || "host".equalsIgnoreCase(entry.getKey()) || "content-type".equalsIgnoreCase(entry.getKey())).sorted(Map.Entry.comparingByKey()).forEach(entry -> {
                String lowerKey = entry.getKey().toLowerCase();
                String value = String.valueOf(entry.getValue()).trim();
                canonicalHeaders.append(lowerKey).append(":").append(value).append("\n");
                signedHeadersSb.append(lowerKey).append(";");
            });
            String signedHeaders = signedHeadersSb.substring(0, signedHeadersSb.length() - 1);
            String canonicalRequest = httpMethod + "\n" + canonicalUri + "\n" + canonicalQueryString + "\n" + canonicalHeaders + "\n" + signedHeaders + "\n" + hashedRequestPayload;

            // 步骤 2：拼接待签名字符串 // 计算规范化请求的哈希值
            String hashedCanonicalRequest = sha256Hex(canonicalRequest.getBytes(StandardCharsets.UTF_8));
            String stringToSign = "ACS3-HMAC-SHA256" + "\n" + hashedCanonicalRequest;

            // 步骤 3：计算签名
            String signature = hmac256(appSecret.getBytes(StandardCharsets.UTF_8), stringToSign);

            // 步骤 4：拼接 Authorization
            String authorization = "ACS3-HMAC-SHA256" + " " + "Credential=" + appid + ",SignedHeaders=" + signedHeaders + ",Signature=" + signature;
            headers.put("Authorization", authorization);
        } catch (Exception e) {
            // 异常处理
            System.out.println("Failed to get authorization");
            e.printStackTrace();
        }

        String url = "https://" + host + canonicalUri;

        String resStr = HttpClientUtils.post(url, queryParam, null, headers);
        JSONObject jsonObject = JSON.parseObject(resStr);
        if(jsonObject.containsKey("Message") && jsonObject.getString("Message").equals("OK")){
            return "SUCCESS";
        }else {
            return "FAIL";
        }
    }

    /**
     * 使用HmacSHA256算法生成消息认证码（MAC）。
     *
     * @param secretKey 密钥，用于生成MAC的密钥，必须保密。
     * @param str       需要进行MAC认证的消息。
     * @return 返回使用HmacSHA256算法计算出的消息认证码。
     * @throws Exception 如果初始化MAC或计算MAC过程中遇到错误，则抛出异常。
     */
    public static String hmac256(byte[] secretKey, String str) throws Exception {
        // 实例化HmacSHA256消息认证码生成器
        Mac mac = Mac.getInstance("HmacSHA256");
        // 创建密钥规范，用于初始化MAC生成器
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey, mac.getAlgorithm());
        // 初始化MAC生成器
        mac.init(secretKeySpec);
        // 计算消息认证码并返回
        byte[] bytes = mac.doFinal(str.getBytes(StandardCharsets.UTF_8));
        return HexFormat.of().formatHex(bytes);

    }

    /**
     * 使用SHA-256算法计算字符串的哈希值并以十六进制字符串形式返回。
     *
     * @param input 需要进行SHA-256哈希计算的字节数组。
     * @return 计算结果为小写十六进制字符串。
     * @throws Exception 如果在获取SHA-256消息摘要实例时发生错误。
     */
    public static String sha256Hex(byte[] input) throws Exception {
        // 获取SHA-256消息摘要实例
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        // 计算字符串s的SHA-256哈希值
        byte[] d = md.digest(input);
        // 将哈希值转换为小写十六进制字符串并返回
        return HexFormat.of().formatHex(d).toLowerCase();
    }

    /**
     * 对指定的字符串进行URL编码。
     * 使用UTF-8编码字符集对字符串进行编码，并对特定的字符进行替换，以符合URL编码规范。
     *
     * @param str 需要进行URL编码的字符串。
     * @return 编码后的字符串。其中，加号"+"被替换为"%20"，星号"*"被替换为"%2A"，波浪号"%7E"被替换为"~"。
     */
    public static String percentCode(String str) {
        if (str == null) {
            throw new IllegalArgumentException("输入字符串不可为null");
        }
        try {
            return URLEncoder.encode(str, "UTF-8").replace("+", "%20").replace("*", "%2A").replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("UTF-8编码不被支持", e);
        }
    }
}
