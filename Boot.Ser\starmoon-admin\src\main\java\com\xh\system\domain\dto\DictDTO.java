package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DictDTO {

    @Schema(description = "字典ID")
    private Long id;

    @Schema(description = "英文名(代码)")
    private String nameEn;

    @Schema(description = "中文名")
    private String nameCn;

    @Schema(description = "样式")
    private String cssClass;


    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "子级")
    private List<DictDTO> children;
}
