<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.print.dao.PrintSkuDao">

    <resultMap type="com.xh.print.domain.entity.PrintSku" id="PrintSkuMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="specs" column="specs" jdbcType="VARCHAR"/>
        <result property="specsSnapshot" column="specs_snapshot" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="NUMERIC"/>
        <result property="image" column="image" jdbcType="VARCHAR"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap type="com.xh.print.domain.entity.PrintAttr" id="PrintAttrMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="attrName" column="specs_name" jdbcType="VARCHAR"/>

        <collection columnPrefix="psv_" property="attrValues" ofType="com.xh.print.domain.entity.PrintAttrValue">
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="attrValue" column="specs_value" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PrintSkuMap">
        select id,
               specs,
               specs_snapshot,
               amount,
               image,
               note,
               create_time,
               update_time
        from print_sku
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="PrintSkuMap">
        select
        id, specs, specs_snapshot, amount, image, note, create_time, update_time
        from print_sku
        <where>
            <if test="keyword != null and '' != keyword">
                and specs = concat('%',#{keyword},'%')
            </if>
        </where>
    </select>

    <select id="queryAttrByList" resultMap="PrintAttrMap">
        SELECT
            psn.id,
            psn.specs_name ,
            psv.id psv_id,
            psv.specs_value psv_specs_value,
            psv.description psv_description
        FROM
            print_specs_name psn
                JOIN `print_specs_value` psv ON psn.id = psv.specs_name_id
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into print_sku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="specs != null and '' != specs">
                specs,
            </if>
            <if test="specsSnapshot != null and '' != specsSnapshot">
                specs_snapshot,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="image != null">
                image,
            </if>
            <if test="note != null">
                note,
            </if>
            create_time
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="specs != null and '' != specs">
                #{specs},
            </if>
            <if test="specsSnapshot != null and '' != specsSnapshot">
                #{specsSnapshot},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="image != null">
                #{image},
            </if>
            <if test="note != null">
                #{note},
            </if>
            now()
        </trim>

    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update print_sku
        <set>
            <if test="specs != null and specs != ''">
                specs = #{specs},
            </if>
            <if test="specsSnapshot != null and specsSnapshot != ''">
                specs_snapshot = #{specsSnapshot},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="image != null and image != ''">
                image = #{image},
            </if>
            <if test="note != null and note != ''">
                note = #{note},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from print_sku
        where id = #{id}
    </delete>

    <delete id="delete">
        delete
        from print_sku
    </delete>

</mapper>

