const app = getApp();
var api = require('../../utils/api.js');
var sender = require('../../utils/sender.js');

Page({
  data: {
    phone: '',
    code: '',
    codeKey: '',
    countdown: 0,
    canSendCode: true,
    loading: false,
    agreementChecked: false
  },

  onLoad: function (options) {
    // 检查是否已经登录
    const loginStatus = wx.getStorageSync('LoginStatus');
    if (loginStatus) {
      wx.redirectTo({
        url: '/pages/index/index'
      });
    }
  },

  // 手机号输入
  onPhoneInput: function(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 验证码输入
  onCodeInput: function(e) {
    this.setData({
      code: e.detail.value
    });
  },

  // 同意协议
  onAgreementChange: function(e) {
    this.setData({
      agreementChecked: e.detail.value.length > 0
    });
  },

  // 发送验证码
  sendCode: function() {
    if (!this.data.canSendCode) {
      return;
    }

    const phone = this.data.phone.trim();
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '发送中...'
    });

    sender.requestUrl({
      url: api.api_send_sms_code,
      method: 'POST',
      data: { phone: phone }
    }, (data) => {
      wx.hideLoading();
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      });

      this.setData({
        codeKey: data.codeKey,
        canSendCode: false,
        countdown: 60
      });

      this.startCountdown();
    }, (error) => {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      });
    });
  },

  // 开始倒计时
  startCountdown: function() {
    const timer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          canSendCode: true,
          countdown: 0
        });
      } else {
        this.setData({
          countdown: countdown
        });
      }
    }, 1000);
  },

  // 注册
  register: function() {
    const { phone, code, codeKey, agreementChecked } = this.data;

    if (!phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    if (!code.trim()) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }

    if (!agreementChecked) {
      wx.showToast({
        title: '请同意用户协议',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '注册中...'
    });

    // 先获取微信登录code
    wx.login({
      success: (res) => {
        sender.requestUrl({
          url: api.api_register,
          method: 'POST',
          data: {
            code: res.code,
            phone: phone.trim(),
            smsCode: code.trim(),
            codeKey: codeKey
          }
        }, (data) => {
          wx.hideLoading();
          this.setData({ loading: false });

          // 保存登录信息
          wx.setStorageSync('AccessToken', data.accessToken);
          wx.setStorageSync('LoginStatus', true);
          wx.setStorageSync('UserInfo', data.memberDetail);

          wx.showToast({
            title: '注册成功',
            icon: 'success'
          });

          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/index/index'
            });
          }, 1500);
        }, (error) => {
          wx.hideLoading();
          this.setData({ loading: false });
          wx.showToast({
            title: error.message || '注册失败',
            icon: 'none'
          });
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({ loading: false });
        wx.showToast({
          title: '获取微信授权失败',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到登录页面
  goToLogin: function() {
    wx.redirectTo({
      url: '/pages/login/login'
    });
  },

  // 查看用户协议
  viewAgreement: function() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议内容...',
      showCancel: false
    });
  }
});
