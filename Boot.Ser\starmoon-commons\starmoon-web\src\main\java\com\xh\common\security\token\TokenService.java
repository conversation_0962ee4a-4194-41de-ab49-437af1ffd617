package com.xh.common.security.token;


import org.springframework.security.core.Authentication;

public interface TokenService {
    
    AccessToken getAccessByToken(String token);

    Authentication getAuthenticationByToken(String token);

    Authentication getAuthenticationByRefresh(String refreshToken);

    AccessToken refreshByRefresh(String refreshToken);

    void destroy(String accessToken,String refreshToken);

    void storeAccessToken(AccessToken accessToken,Authentication authentication);
}
