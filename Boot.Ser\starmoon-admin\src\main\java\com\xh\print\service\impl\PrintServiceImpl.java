package com.xh.print.service.impl;

import com.xh.common.web.exception.ServiceException;
import com.xh.file.service.FileService;
import com.xh.print.domain.entity.ImagePrintable;
import com.xh.print.domain.form.PrintFile;
import com.xh.print.service.PrintService;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.MediaPrintableArea;
import javax.print.attribute.standard.MediaSizeName;
import javax.print.attribute.standard.PageRanges;
import javax.print.attribute.standard.Sides;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.awt.print.*;
import java.io.File;
import java.util.ArrayList;
import java.util.List;


@Service
public class PrintServiceImpl implements PrintService {

    @Autowired
    private FileService fileService;

    @Override
    public boolean print(List<PrintFile> printFileList) {

        for(PrintFile printFile: printFileList){
            try {
                File file = fileService.getFileByPath(printFile.getFilePath());

                javax.print.PrintService[] printServices = PrinterJob.lookupPrintServices();
                PrinterJob printerJob = PrinterJob.getPrinterJob();
                printerJob.setJobName("Canon TS3400 series");

                // 3. 设置打印属性
                PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
                attributes.add(MediaSizeName.ISO_A4); // 设置为 A4 纸张
                attributes.add(Sides.ONE_SIDED);       // 单面打印
                attributes.add(new MediaPrintableArea(1, 1, 210,297, MediaPrintableArea.MM));
                PageRanges pageRanges = new PageRanges(1, 2); // 2~5页
                attributes.add(pageRanges);

                Book book = new Book();
                PageFormat pageFormat = printerJob.getPageFormat(attributes);
                // 渲染所有页为图像
                PDDocument document = PDDocument.load(file);
                int pageCount = document.getNumberOfPages();
                List<BufferedImage> images = new ArrayList<>();
                PDFRenderer renderer = new PDFRenderer(document);
                for (int i = 0; i < 2; i++) {
                    BufferedImage bufferedImage = renderer.renderImageWithDPI(i, 300);
                    BufferedImage scaleImage = scaleImage(bufferedImage, (int) (210 * 300 / 25.4), (int) (297 * 300 / 25.4));
                    images.add(scaleImage);
                    book.append(new ImagePrintable(scaleImage),pageFormat);
                }


                printerJob.setPageable(book);
                printerJob.print(attributes);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }


        return true;
    }

    @Override
    public List<BufferedImage> printPreview(PrintFile printFile) {
        File file = fileService.getFileByPath(printFile.getFilePath());

        try {
            // 2. 创建打印服务
            PrinterJob printerJob = PrinterJob.getPrinterJob();
            printerJob.setJobName("Canon TS3400 series");

            // 3. 设置打印属性
            PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
            attributes.add(MediaSizeName.ISO_A4); // 设置为 A4 纸张
            attributes.add(Sides.ONE_SIDED);       // 单面打印
            attributes.add(new MediaPrintableArea(1, 1, 210,297, MediaPrintableArea.MM));

            // 渲染所有页为图像
            PDDocument document = PDDocument.load(file);
            int pageCount = document.getNumberOfPages();

            List<BufferedImage> images = new ArrayList<>();
            PDFRenderer renderer = new PDFRenderer(document);
            for (int i = 0; i < pageCount; i++) {
                BufferedImage bufferedImage = renderer.renderImageWithDPI(i, 300);
                BufferedImage scaleImage = scaleImage(bufferedImage, (int) (210 * 300 / 25.4), (int) (297 * 300 / 25.4));
                images.add(scaleImage);
            }

            PageFormat pf = printerJob.getPageFormat(attributes);
            Paper paper = pf.getPaper();
// 整张纸张大小（pt）
            double paperW = paper.getWidth();
            double paperH = paper.getHeight();
// 可打印区域原点及大小（pt）
            double ix = paper.getImageableX();
            double iy = paper.getImageableY();
            double iw = paper.getImageableWidth();
            double ih = paper.getImageableHeight();
// 边距（pt），再转成 mm
            double leftMm   = ix   * 25.4 / 72.0;
            double topMm    = iy   * 25.4 / 72.0;
            double rightMm  = (paperW  - ix - iw) * 25.4 / 72.0;
            double bottomMm = (paperH  - iy - ih) * 25.4 / 72.0;

            final int maxW = 600, maxH = 800;

            List<BufferedImage> imageList = new ArrayList<>();
            for(BufferedImage orig: images){
                // 按比例缩放到最大尺寸
                double wr = (double) maxW / orig.getWidth();
                double hr = (double) maxH / orig.getHeight();
                double scale = Math.min(wr, hr);
                int dw = (int) (orig.getWidth() * scale);
                int dh = (int) (orig.getHeight() * scale);
                Image scaled = orig.getScaledInstance(dw, dh, Image.SCALE_SMOOTH);

                // 创建新的 BufferedImage，用于绘制带边距的图像
                int finalWidth = dw + (int)((leftMm + rightMm)*72/25.4);
                int finalHeight = dh + (int)((topMm + bottomMm)*72/25.4);

                BufferedImage combined = new BufferedImage(finalWidth, finalHeight, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2d = combined.createGraphics();


                // 填充背景颜色（可选，默认为透明或白色）
                g2d.setColor(Color.WHITE); // 或 Color.BLACK、Color.WHITE 等
                g2d.fillRect(0, 0, finalWidth, finalHeight);

                // 绘制缩放后的图像到指定边距的位置
                g2d.drawImage(scaled, (int)(leftMm * 72 /25.4), (int)(topMm * 72 /25.4), null);
                g2d.dispose();
                imageList.add(combined);
            }


            return imageList;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("预览失败");
        }

    }
//    private BufferedImage scaleImage(BufferedImage image, int width, int height) {
//        // 直接使用双线性插值缩放（速度快、质量不错）
//        BufferedImage newImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
//        Graphics2D g2d = newImage.createGraphics();
//        try {
//            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
//            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
//            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
//
//            double scaleX = (double) width / image.getWidth();
//            double scaleY = (double) height / image.getHeight();
//            AffineTransform transform = AffineTransform.getScaleInstance(scaleX, scaleY);
//            AffineTransformOp op = new AffineTransformOp(transform, AffineTransformOp.TYPE_BILINEAR);
//            newImage = op.filter(image, null);
//        } finally {
//            g2d.dispose();
//        }
//        return newImage;
//    }
    private BufferedImage scaleImage(BufferedImage image, int width, int height) {
        Image tmp = image.getScaledInstance(width, height, Image.SCALE_SMOOTH);
        BufferedImage newImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = newImage.createGraphics();
        try {
            graphics.drawImage(tmp, 0, 0, null);
        } finally {
            graphics.dispose();
        }
        return newImage;
    }
}
