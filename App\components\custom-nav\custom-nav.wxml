<view class="custom-nav">
 <!-- 状态栏占位 -->
 <view class="nav-status" style="width: 100%; height: {{statusBarHeight}}px;"></view>
  <!-- 导航栏内容 -->
  <view class="nav-content" style="width: 100%; height: {{navBarHeight}}px; color: {{color}};">
    <view wx:if="{{showBack}}" class="back-btn" bindtap="goBack">
      <image class="back-icon" src="/images/icon/icon_left_arrow.png" mode="aspectFit"></image>
    </view>
    <view class="title">{{title}}</view>
    <view wx:if="{{showRight}}" class="right-btn" bindtap="onRightTap">
      <text class="right-text">{{rightText}}</text>
    </view>
  </view>
</view>
 
<view class="custom-res" style="height: {{statusBarHeight + navBarHeight}}px;"></view>

