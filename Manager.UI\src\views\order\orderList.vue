<template>
  <div class="page-container">
    <order-edit ref="orderEdit" @search="search"></order-edit>
    <div class="page-content">
      <div class="card card--search">
        <div class="search-container">
          <div class="search-form">
            <el-input v-model="searchModel.keyword" placeholder="订单号" clearable style="width: 200px;" />
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="search" class="search-btn">搜索</el-button>
          </div>
        </div>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table stripe :data="orderList" style="width: 100%;" fit>
            <el-table-column prop="id" label="ID" align="center" width="100" />
            <el-table-column prop="totalAmount" label="总金额" align="center" />
            <el-table-column prop="status" label="状态" align="center" />
            <el-table-column prop="createTime" label="创建时间" align="center" />
            <el-table-column prop="updateTime" label="更新时间" align="center" />
            <el-table-column align="center" width="160" label="操作">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="edit(scope.row.id)">详情</el-button>
                <el-button link type="danger" size="small" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick" @next-click="nextClick" :total="total" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import orderEdit from '@/components/order/orderEdit.vue'
import { listOrder, getOrder, deleteOrder } from '@/api/order/order'
import mitt from '@/utils/mitt'
export default {
  components: { orderEdit },
  data() {
    return {
      searchModel: { pageNum: 1, pageSize: 10, keyword: '' },
      orderList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listOrder(this.searchModel)
        .then(res => {
          this.orderList = res.data.data.list
          this.total = res.data.data.total
        })
        .catch(err => {
          this.$message.error(err.data.errorMessage)
        })
    },
    add() {
      mitt.emit('openOrderAdd')
    },
    edit(id) {
      getOrder(id)
        .then(res => {
          mitt.emit('openOrderEdit', res.data.data)
        })
        .catch(err => {
          this.$message.error(err.data.errorMessage)
        })
    },
    deleted(id) {
      this.$confirm('删除订单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOrder(id)
          .then(() => {
            this.search()
            this.$message.success('操作成功')
          })
          .catch(err => {
            this.$message.error(err.data.errorMessage)
          })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    prevClick(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    nextClick(num) {
      this.searchModel.pageNum = num
      this.search()
    }
  },
  created() {
    mitt.off('openOrderAdd')
    mitt.off('openOrderEdit')
    this.search()
  }
}
</script>

<style scoped>
.card--search {
  margin-bottom: 20px;
}
</style> 