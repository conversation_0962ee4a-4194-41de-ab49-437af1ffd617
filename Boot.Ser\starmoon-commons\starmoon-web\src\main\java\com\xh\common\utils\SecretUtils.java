package com.xh.common.utils;

import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Slf4j
public class SecretUtils {
    //私钥加密
    public static String encryptByPrivateKey(String data, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE,privateKey);
        return Base64.getEncoder().encodeToString(cipher.doFinal(data.getBytes()));
    }
    //私钥解密
    public static String decryptByPrivateKey(String data,PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE,privateKey);
        return new String(cipher.doFinal(Base64.getDecoder().decode(data)));
    }

    //公钥加密
    public static String encryptByPublicKey(String data, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE,publicKey);
        return Base64.getEncoder().encodeToString(cipher.doFinal(data.getBytes()));
    }

    //公钥解密
    public static String decryptByPublicKey(String data,PublicKey publicKey)throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE,publicKey);
        return new String(cipher.doFinal(Base64.getDecoder().decode(data)));
    }


    /**
     * sha256withRSA签名
     * @param data 签名数据
     * @param privateKey 密钥
     * @return
     */
    public static String signatureSha256withRSA(String data,PrivateKey privateKey){
        try {
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signature.sign());
        }catch (NoSuchAlgorithmException e){
            log.error("加密类不存在：{}",e);
            throw new ServiceException(StatusCode.SYSTEM_ERROR);
        }catch (InvalidKeyException e){
            log.error("无效的密钥：{}",e);
            throw new ServiceException(StatusCode.SYSTEM_ERROR);
        }catch (SignatureException e){
            log.error("签名错误：{}",e);
            throw new ServiceException(StatusCode.SYSTEM_ERROR);
        }

    }

    /**
     * sha256withRSA验签
     * @param data 要验证的数据
     * @param sign 要验证的签名
     * @param publicKey 公钥
     * @return
     */
    public static boolean verifySha256withRSA(String data,String sign,PublicKey publicKey){
        try {
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(publicKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));

            return signature.verify(Base64.getDecoder().decode(sign));
        }catch (Exception e){
            throw new ServiceException("签名验证失败");
        }
    }
    public static KeyPair generateKeyPair(int number){
        try {
            // 生成 RSA 密钥对
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(number);
            return keyGen.generateKeyPair();
        }catch (Exception e){
            throw new ServiceException(StatusCode.SYSTEM_ERROR);
        }
    }
    /**
     * 生成密钥
     * @param number
     * @return
     */
    public static String generateSecretKey(int number){
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
            keyGen.init(number);
            SecretKey secretKey = keyGen.generateKey();
            byte[] encoded = secretKey.getEncoded();
            return Base64.getEncoder().encodeToString(encoded);
        }catch (Exception e){
            throw new ServiceException(StatusCode.SYSTEM_ERROR);
        }
    }
    /**
     * AES_GCM_NOPadding解密
     * @param ciphertext 密文
     * @param nonce 加密随机串
     * @param add 额外数据
     * @param key 密钥串 (api v3密钥)
     * @return
     */
    public static String decryptByAesGcm(String ciphertext,String nonce,String add,String key){
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");

            SecretKey secretKey = new SecretKeySpec(key.getBytes(),"AES");

            GCMParameterSpec spec = new GCMParameterSpec(128, nonce.getBytes());

            cipher.init(Cipher.DECRYPT_MODE,secretKey,spec);

            if(add != null){
                cipher.updateAAD(add.getBytes());
            }

            return new String(cipher.doFinal(Base64.getDecoder().decode(ciphertext)));
        }catch (Exception e){
            throw new ServiceException("解密失败");
        }
    }


    public static PublicKey stringToPublicKey(String key){
        try {
            key = key
                    .replace("-----BEGIN PUBLIC KEY-----","")
                    .replace("-----END PUBLIC KEY-----","")
                    .replaceAll("\\s+","");

            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] decode = Base64.getDecoder().decode(key);
            return keyFactory.generatePublic(new X509EncodedKeySpec(decode));
        }catch (Exception e){
            throw new ServiceException(StatusCode.SYSTEM_ERROR);
        }
    }
    public static PublicKey flieToPublicKey(File file){
        return stringToPublicKey(FileUtils.fileToString(file));
    }

    /**
     * @return
     */
    public static PrivateKey stringToPrivateKey(String key){
        try {
            key = key
                    .replace("-----BEGIN PRIVATE KEY-----","")
                    .replace("-----END PRIVATE KEY-----","")
                    .replace("-----BEGIN RSA PRIVATE KEY-----","")
                    .replace("-----END RSA PRIVATE KEY-----","")
                    .replaceAll("\\s+","");
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] decode = Base64.getDecoder().decode(key);
            return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(decode));
        }catch (Exception e){
            throw new ServiceException(StatusCode.FILE_CONTENT_ERROR);
        }
    }

    public static PrivateKey fileToPrivateKey(File file){
        return stringToPrivateKey(FileUtils.fileToString(file));
    }

    public static PublicKey stringToCertificatePublicKey(String key){
        try {
            key = key
                    .replace("-----BEGIN CERTIFICATE-----","")
                    .replace("-----END CERTIFICATE-----","")
                    .replaceAll("\\s+","");
            byte[] decode = Base64.getDecoder().decode(key);
            Certificate cert = CertificateFactory.getInstance("X.509").generateCertificate(new ByteArrayInputStream(decode));
            return cert.getPublicKey();
        }catch (Exception e){
            throw new ServiceException(StatusCode.FILE_CONTENT_ERROR);
        }
    }
    public static PublicKey flieToCertificatePublicKey(File file){
        return stringToCertificatePublicKey(FileUtils.fileToString(file));
    }
}
