var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();



Page({
  data: {
    printFile: {},
    pickerRange: [],
    pickerValue: [],
    attrList: [],
    specsList: [],
    selected: [],
    compatibilityMatrix: null // 邻接矩阵，存储属性值之间的兼容关系
  },

  onLoad: function (options) {
    var id = options.id
    this.getTempFileList(id)
    this.loadPrintAttributes()
    this.loadPrintSpecs()
  },
  getTempFileList(id) {
    var that = this
    sender.requestUrl({
      url: api.api_temp_file,
      method: 'GET',
      params: {
        id: id
      }
    }, function (data) {

      var pageArray = []

      for (var i = 1; i <= data.totalPage; i++) {
        pageArray.push(i)
      }

      var pickerValue = [data.pageNum - 1, data.pageSize - 1]
      var pickerRange = [pageArray, pageArray]

      that.setData({
        printFile: data,
        pickerValue: pickerValue,
        pickerRange: pickerRange
      });

    });
  },
  // 加载打印属性
  loadPrintAttributes: function () {
    var that = this
    sender.requestUrl({
      url: api.api_print_attr,
      method: 'GET'
    }, function (data) {
      that.setData({
        attrList: data
      });
      // 属性加载完成后，构建兼容性矩阵
      that.buildCompatibilityMatrix()
    });
  },
  //加载打印规格
  loadPrintSpecs() {
    var that = this
    sender.requestUrl({
      url: api.api_print_specs,
      method: 'GET'
    }, function (data) {
      that.setData({
        specsList: data
      });
      // 规格加载完成后，构建兼容性矩阵
      that.buildCompatibilityMatrix()
    });
  },

  /**
   * 初始化SKU兼容性检查
   * 简化版本：直接使用SKU数据进行兼容性检查
   */
  buildCompatibilityMatrix() {
    var attrList = this.data.attrList
    var specsList = this.data.specsList

    // 确保两个数据都已加载
    if (!attrList || attrList.length === 0 || !specsList || specsList.length === 0) {
      console.log('数据未加载完成，跳过初始化')
      return
    }

    console.log('开始初始化SKU兼容性检查...')
    console.log('属性列表:', attrList)
    console.log('规格列表:', specsList)

    // 初始化禁用状态
    this.updateDisabledStatusWithMatrix()
  },
  // 减少数量
  decreaseQuantity: function () {
    const printFile = this.data.printFile;

    if (printFile.quantity > 1) {
      printFile.quantity--;
      this.setData({
        printFile: printFile
      });
      this.calculateTotal();
    }
  },

  // 增加数量
  increaseQuantity: function () {
    const printFile = this.data.printFile;
    printFile.quantity++;
    this.setData({
      printFile: printFile
    });
    this.calculateTotal();
  },
  calculateTotal() {
    let specsList = this.data.specsList
    let specs = this.data.selected.join("/")
    for(let item of specsList){
      if(item.specs == specs){
        console.log(item)
        let pageNum = this.data.pickerValue[0]
        let pageSize = this.data.pickerValue[1]
        let totalPage = pageSize - pageNum + 1
        let quantity = this.data.printFile.quantity
        let amount = item.amount == null ? 0 : item.amount
        let totalAmount = amount * quantity * totalPage
        let printFile = this.data.printFile
        printFile.skuId = item.id
        this.setData({
          price: totalAmount,
          printFile: printFile
        })
      }
    }
  },
  selectPaperSize(e) {
    var attrIndex = e.currentTarget.dataset.attrIndex
    var valueIndex = e.currentTarget.dataset.valueIndex
    var attrList = this.data.attrList

    // 检查选项是否被禁用
    var selectedValue = attrList[attrIndex].attrValues[valueIndex]
    if (selectedValue.disabled) {
      console.log('选项已禁用，无法选择:', selectedValue.attrValue)
      return
    }

    attrList[attrIndex].selected = selectedValue.attrValue

    this.setData({
      attrList: attrList
    })

    // 使用邻接矩阵更新禁用状态
    this.updateDisabledStatusWithMatrix()

    // 重新计算价格
    let flag = true
    let selected = []
    for(let item of attrList){
      selected.push(item.selected)
      if(!item.selected){
          flag = false
      }
    }
  
    if(flag){
      this.setData({
        selected: selected
      })
      this.calculateTotal()
    }
    
  },



  /**
   * 使用SKU数据更新每个规格值的 disabled 状态
   * 改进算法：直接检查是否存在包含当前选择组合的完整SKU
   */
  updateDisabledStatusWithMatrix() {
    var attrList = this.data.attrList
    var specsList = this.data.specsList

    if (!attrList || !specsList) {
      console.log('数据未准备好，跳过更新禁用状态')
      return
    }

    console.log('更新禁用状态...')

    // 获取当前所有已选择的属性映射
    var selectedMap = {}
    attrList.forEach(function(attr) {
      if (attr.selected) {
        selectedMap[attr.attrName] = attr.selected
      }
    })

    console.log('当前已选择的属性:', selectedMap)

    // 遍历每个属性
    attrList.forEach(function(attr) {
      // 遍历每个属性值
      attr.attrValues.forEach(function(attrValue) {
        // 如果这个属性值已经被选中，则不禁用
        if (attr.selected === attrValue.attrValue) {
          attrValue.disabled = false
          return
        }

        // 创建测试用的选择映射，包含当前测试的属性值
        var testSelectedMap = Object.assign({}, selectedMap)
        testSelectedMap[attr.attrName] = attrValue.attrValue

        // 检查是否存在匹配的SKU
        var hasMatchingSku = this.findMatchingSku(testSelectedMap, specsList)

        // 设置disabled状态
        attrValue.disabled = !hasMatchingSku

        if (attrValue.disabled) {
          console.log('禁用选项:', attr.attrName + ':' + attrValue.attrValue, '原因：无匹配SKU')
        }
      }.bind(this))
    }.bind(this))

    // 更新页面数据
    this.setData({
      attrList: attrList
    })

    console.log('禁用状态更新完成')
  },

  /**
   * 查找是否存在匹配的SKU
   * @param {Object} selectedMap - 当前选择的属性映射
   * @param {Array} specsList - SKU列表
   * @returns {Boolean} 是否存在匹配的SKU
   */
  findMatchingSku(selectedMap, specsList) {
    for (var i = 0; i < specsList.length; i++) {
      var spec = specsList[i]
      var specMap = this.parseSpecStringToMap(spec.specs)

      // 检查当前选择是否与此SKU兼容
      var isCompatible = true
      for (var attrName in selectedMap) {
        if (selectedMap.hasOwnProperty(attrName)) {
          var selectedValue = selectedMap[attrName]
          var specValue = specMap[attrName]

          // 如果SKU中不包含此属性或值不匹配，则不兼容
          if (!specValue || specValue !== selectedValue) {
            isCompatible = false
            break
          }
        }
      }

      if (isCompatible) {
        console.log('找到匹配SKU:', spec.specs, '选择:', selectedMap)
        return true
      }
    }

    console.log('未找到匹配SKU，选择:', selectedMap)
    return false
  },

  /**
   * 解析规格字符串为属性映射
   * @param {String} specsString - 规格字符串
   * @returns {Object} 属性映射 { 属性名: 值 }
   */
  parseSpecStringToMap(specsString) {
    var specMap = {}
    var attrList = this.data.attrList

    if (!specsString || !attrList) {
      return specMap
    }

    if (specsString.indexOf('/') > -1) {
      // 斜杠分隔格式: "A4/黑色/单面/普通纸/不装订"
      var parts = specsString.split('/')

      // 根据属性列表的顺序来匹配
      attrList.forEach(function(attr, index) {
        if (index < parts.length && parts[index].trim()) {
          specMap[attr.attrName] = parts[index].trim()
        }
      })
    } else if (specsString.indexOf(',') > -1) {
      // 逗号分隔格式: "大小:A4,颜色:黑色,打印方式:单面"
      var pairs = specsString.split(',')
      pairs.forEach(function(pair) {
        var keyValue = pair.split(':')
        if (keyValue.length === 2) {
          var key = keyValue[0].trim()
          var value = keyValue[1].trim()
          specMap[key] = value
        }
      })
    } else {
      // 尝试JSON格式
      try {
        specMap = JSON.parse(specsString)
      } catch (e) {
        console.warn('无法解析规格字符串:', specsString)
      }
    }

    return specMap
  },
  submitPrint(){
    console.log(this.data.printFile)
    
    sender.requestUrl({
      url: api.api_temp_file,
      method: 'PUT',
      data: this.data.printFile
    }, function () {
      wx.navigateBack({
        delta: 1
      })
    });
  },

  onPickerChange(e) {
    var pickerValue = e.detail.value
    var printFile = this.data.printFile
    printFile.pageNum = pickerValue[0] + 1
    printFile.pageSize = pickerValue[1] + 1
    this.setData({
      pickerValue: pickerValue,
      printFile: printFile
    })
    this.calculateTotal()
  }
})