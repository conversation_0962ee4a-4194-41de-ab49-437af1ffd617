package com.xh.auth.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.common.security.token.AccessToken;
import com.xh.common.wechat.user.MemberDetail;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class MemberAccessToken {

    private AccessToken accessToken;

    private MemberDetail memberDetail;

    @JsonProperty("session_key")
    private String sessionKey;

}
