package com.xh.common.sms;

import com.xh.common.utils.SmsUtils;
import com.xh.common.web.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.UUID;

@Service
public class SmsService {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    private static final String SMS_CODE_KEY = "sms:code:";

    private String appid = "LTAI5tHBFEMMLEwqtYQ9MpDB";

    private String appSecret = "******************************";

    private String signName = "苏州网信信息科技股份";

    private String templateCode = "SMS_484820177";

    public String smsCode(String phone) {
        String code = generateRandomCode(6);

        String state = SmsUtils.sendSms(appid, appSecret, signName, templateCode, phone, "{\"code\":\"" + code + "\"}");

        if("FAIL".equals(state)){
            throw new ServiceException("获取验证码失败请稍后重试");
        }
        String key = UUID.randomUUID().toString();
        RedisConnection connection = redisConnectionFactory.getConnection();

        connection.set((SMS_CODE_KEY+key).getBytes(), code.getBytes());

        connection.expire((SMS_CODE_KEY+key).getBytes(),5 * 60);

        return key;
    }

    public boolean verify(String key, String code) {

        RedisConnection connection = redisConnectionFactory.getConnection();

        byte[] bytes = connection.get((SMS_CODE_KEY + key).getBytes());
        if(bytes == null){
            return false;
        }
        String sourceCode = new String(bytes);
        return sourceCode.equals(code);
    }

    /**
     * 生成指定位数随机数字
     *
     * @param codeNum 位数
     * @return 随机数字字符串
     */
    private String generateRandomCode(Integer codeNum) {
        String numStr = "0123456789";
        StringBuilder sb = new StringBuilder(codeNum);
        for (int i = 0; i < codeNum; i++) {
            char ch = numStr.charAt(new Random().nextInt(numStr.length()));
            sb.append(ch);
        }
        System.out.println("本次随机数：" + sb);
        return sb.toString();
    }
}
