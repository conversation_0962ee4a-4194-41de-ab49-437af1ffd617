package com.xh.system.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * (Dict)实体类
 *
 * <AUTHOR>
 * @since 2021-12-15 13:11:10
 */
@Data
public class Dict{

    @Schema(description = "字典ID")
    private Long id;

    @Schema(description = "英文名(代码)")
    private String nameEn;

    @Schema(description = "中文名")
    private String nameCn;

    @Schema(description = "样式")
    private String cssClass;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String note;

}

