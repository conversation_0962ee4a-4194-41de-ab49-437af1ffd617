package com.xh.system.service;

import com.github.pagehelper.PageInfo;
import com.xh.system.domain.dto.ImagetextCreateDTO;
import com.xh.system.domain.dto.ImagetextEditDTO;
import com.xh.system.domain.entity.Imagetext;

/**
 * 系统图文表(Imagetext)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-21 08:30:39
 */
public interface ImagetextService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Imagetext queryById(Long id);

    /**
     * 通过分页查询
     *

     * @return 查询结果
     */
    PageInfo<Imagetext> queryByPage(String type);

    /**
     * 新增数据
     *
     * @return 实例对象
     */
    Long insert(ImagetextCreateDTO dto);

    /**
     * 修改数据
     *
     * @return 实例对象
     */
    boolean update(ImagetextEditDTO dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

}
