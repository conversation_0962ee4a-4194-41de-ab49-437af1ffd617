// pages/price/price.js
var api = require('../../utils/api.js')
var sender = require('../../utils/sender.js')
const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    priceList: [], // 价格表列表
    isLoading: false, // 是否正在加载
    statusBarHeight: 20,
    currentImagetext: null, // 当前选中的图文
    showEffectImage: false, // 是否显示效果图
    CDN_IMAGE: api.CDN_IMAGE // CDN图片路径前缀
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取价格表列表
    this.getPriceList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getPriceList();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取价格表列表
  getPriceList: function() {
    let that = this;
    
    sender.requestUrl({
      url: api.api_imagetext_list,
      method: 'GET',
      params: { 
        type: 'price',
        pageNum: 1,
        pageSize: 50
      }
    }, function(data) {
      let list = [];
      if (data && data.list && data.list.length > 0) {
        // 处理图片路径
        list = data.list.map(item => {
          if (item.imageUrl && item.imageUrl.indexOf('http') !== 0) {
            item.imageUrl = that.data.CDN_IMAGE + item.imageUrl;
          }
          return item;
        });
      }
      
      that.setData({
        priceList: list,
        isLoading: false
      });
    });
  },

  // 查看效果图
  viewEffectImage: function(e) {
    const id = e.currentTarget.dataset.id;
    const item = this.data.priceList.find(item => item.id == id);
    
    if (item && item.imageUrl) {
      // 确保图片URL正确
      let imageUrl = item.imageUrl;
      
      // 如果图片地址不是以http开头且没有包含CDN前缀，则添加CDN前缀
      if (imageUrl.indexOf('http') !== 0 && imageUrl.indexOf(this.data.CDN_IMAGE) !== 0) {
        imageUrl = this.data.CDN_IMAGE + imageUrl;
      }
      
      wx.previewImage({
        current: imageUrl,
        urls: [imageUrl]
      });
    }
  }
})