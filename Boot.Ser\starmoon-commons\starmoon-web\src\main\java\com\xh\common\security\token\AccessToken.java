package com.xh.common.security.token;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
public class AccessToken implements Serializable {

    private static final long serialVersionUID = 20250411225701L;
    public final static String API = "api";

    public final static String OPENAPI = "openapi";
    public final static String AUTH = "Authorization";

    public AccessToken(){
        this.accessToken = String.valueOf(UUID.randomUUID()).replaceAll("-","");
        this.refreshToken = String.valueOf(UUID.randomUUID()).replaceAll("-","");
        this.expiresIn = 7200L;
    }

    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("refresh_token")
    private String refreshToken;

    @JsonProperty("expires_in")
    private Long expiresIn;

}
