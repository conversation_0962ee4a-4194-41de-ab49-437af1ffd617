<view class="container">
  <custom-nav title="用户注册" color="#333" showBack="{{true}}"></custom-nav>
  
  <view class="register-form">
    <view class="form-title">
      <text class="title">手机号注册</text>
      <text class="subtitle">请输入您的手机号完成注册</text>
    </view>

    <!-- 手机号输入 -->
    <view class="input-group">
      <view class="input-label">手机号</view>
      <input 
        class="input-field" 
        type="number" 
        placeholder="请输入手机号" 
        value="{{phone}}"
        bindinput="onPhoneInput"
        maxlength="11"
      />
    </view>

    <!-- 验证码输入 -->
    <view class="input-group">
      <view class="input-label">验证码</view>
      <view class="code-input-wrapper">
        <input 
          class="code-input" 
          type="number" 
          placeholder="请输入验证码" 
          value="{{code}}"
          bindinput="onCodeInput"
          maxlength="6"
        />
        <button 
          class="send-code-btn {{canSendCode ? 'active' : 'disabled'}}" 
          bindtap="sendCode"
          disabled="{{!canSendCode}}"
        >
          {{canSendCode ? '发送验证码' : countdown + 's'}}
        </button>
      </view>
    </view>

    <!-- 用户协议 -->
    <view class="agreement-section">
      <checkbox-group bindchange="onAgreementChange">
        <label class="agreement-label">
          <checkbox value="agree" class="agreement-checkbox"/>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-link" bindtap="viewAgreement">《用户协议》</text>
        </label>
      </checkbox-group>
    </view>

    <!-- 注册按钮 -->
    <button 
      class="register-btn {{agreementChecked && phone && code ? 'active' : 'disabled'}}" 
      bindtap="register"
      disabled="{{loading || !agreementChecked || !phone || !code}}"
    >
      {{loading ? '注册中...' : '立即注册'}}
    </button>

    <!-- 登录链接 -->
    <view class="login-link-section">
      <text class="login-text">已有账号？</text>
      <text class="login-link" bindtap="goToLogin">立即登录</text>
    </view>
  </view>
</view>
