package com.xh.auth.controller;

import com.xh.common.sms.SmsService;
import com.xh.common.web.domain.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "公共模块")
@RestController
@RequestMapping("common-api/v1/sms")
public class SmsController {

    @Autowired
    private SmsService smsService;

    @Parameter(name = "phone", description = "手机号")
    @Operation(summary = "通过手机号获取验证码")
    @PostMapping("code")
    public ResponseEntity<String> smsCode(String phone) {
        return ResponseEntity.success(smsService.smsCode(phone));
    }
}
