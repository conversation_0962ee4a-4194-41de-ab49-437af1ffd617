<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.MemberDao">

    <resultMap type="com.xh.system.domain.entity.Member" id="MemberMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="openid" column="openid" jdbcType="VARCHAR"/>
        <result property="unionid" column="unionid" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="avatarUrl" column="avatar_url" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="residentId" column="resident_id" jdbcType="INTEGER"/>
        <result property="role" column="role" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="MemberMap">
        select id,
               openid,
               birthday,
               unionid,
               user_name,
               nick_name,
               avatar_url,
               gender,
               phone,
               create_time,
               update_time,
               resident_id,
               role
        from t_member
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="MemberMap">
        select
        id, openid, unionid,birthday, user_name, nick_name, avatar_url, gender, phone, create_time, update_time, resident_id,role
        from t_member
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="unionid != null and unionid != ''">
                and unionid = #{unionid}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="nickName != null and nickName != ''">
                and nick_name = #{nickName}
            </if>
            <if test="avatarUrl != null and avatarUrl != ''">
                and avatar_url = #{avatarUrl}
            </if>
            <if test="gender != null and gender != ''">
                and gender = #{gender}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="residentId != null">
                and resident_id = #{residentId}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_member(openid, unionid, user_name, nick_name, avatar_url, gender, phone, create_time, update_time,
                             resident_id)
        values (#{openid}, #{unionid}, #{userName}, #{nickName}, #{avatarUrl}, #{gender}, #{phone}, #{createTime},
                #{updateTime}, #{residentId})
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update t_member
        <set>
            <if test="openid != null and openid != ''">
                openid = #{openid},
            </if>
            <if test="unionid != null and unionid != ''">
                unionid = #{unionid},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="avatarUrl != null and avatarUrl != ''">
                avatar_url = #{avatarUrl},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="birthday != null">
                birthday = #{birthday},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="residentId != null">
                resident_id = #{residentId},
            </if>

            <if test="role != null">
                `role` = #{role},
            </if>

        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_member
        where id = #{id}
    </delete>

</mapper>

