<view class="container">
  <custom-nav title="确认订单" color="#333" showBack="{{true}}"></custom-nav>

  <!-- 收货地址 -->
  <view class="address-section">
    <view class="section-title">收货地址</view>
    
    <view class="address-card" bindtap="selectAddress" wx:if="{{selectedAddress}}">
      <view class="address-info">
        <view class="recipient-phone">
          <text class="recipient">{{selectedAddress.recipient}}</text>
          <text class="phone">{{selectedAddress.phone}}</text>
          <text class="default-tag" wx:if="{{selectedAddress.isDefault === true || selectedAddress.isDefault === 1 || selectedAddress.isDefault === '1'}}">默认</text>
        </view>
        <view class="address-detail">
          {{selectedAddress.province}} {{selectedAddress.city}} {{selectedAddress.county}} {{selectedAddress.detail}}
        </view>
      </view>
      <view class="address-arrow">></view>
    </view>
    <view class="no-address" bindtap="addNewAddress" wx:else>
      <text>+ 添加收货地址</text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="product-section">
    <view class="section-title">商品清单 ({{selectedCount}}件)</view>
    <view class="product-list">
      <view class="product-item" wx:for="{{selectedFiles}}" wx:key="id">
        <view class="product-info">
          <view class="product-name">{{item.fileName}}</view>
          <view class="product-specs">{{item.specs}} / 第{{item.pageNum}}-{{item.pageSize}}页</view>
          <view class="product-pages">共{{item.pageSize - item.pageNum + 1}}页</view>
        </view>
        <view class="product-quantity">
          <view class="quantity-controls">
            <view class="quantity-btn minus" bindtap="decreaseQuantity" data-index="{{index}}">-</view>
            <text class="quantity-text">{{item.quantity}}</text>
            <view class="quantity-btn plus" bindtap="increaseQuantity" data-index="{{index}}">+</view>
          </view>
          <view class="product-price">¥{{item.price}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单金额 -->
  <view class="amount-section">
    <view class="amount-item">
      <text class="amount-label">商品金额</text>
      <text class="amount-value">¥{{totalPrice}}</text>
    </view>
    <view class="amount-item">
      <text class="amount-label">运费</text>
      <text class="amount-value">¥0.00</text>
    </view>
    <view class="amount-total">
      <text class="total-label">实付款</text>
      <text class="total-value">¥{{totalPrice}}</text>
    </view>
  </view>

  <!-- 底部提交区域 -->
  <view class="bottom-fixed-empty"></view>
  <view class="bottom-submit">
    <view class="submit-info">
      <text class="submit-text">实付：</text>
      <text class="submit-price">¥{{totalPrice}}</text>
    </view>
    <view class="submit-btn" bindtap="submitOrder">提交订单</view>
  </view>

  <!-- 地址选择弹窗 -->
  <view class="address-modal" wx:if="{{showAddressList}}">
    <view class="modal-mask" bindtap="closeAddressList"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择收货地址</text>
        <view class="modal-close" bindtap="closeAddressList">×</view>
      </view>
      <scroll-view scroll-y class="address-list">
        <view class="address-option" wx:for="{{addresses}}" wx:key="id" bindtap="chooseAddress" data-index="{{index}}">
          <view class="address-info">
            <view class="recipient-phone">
              <text class="recipient">{{item.recipient}}</text>
              <text class="phone">{{item.phone}}</text>
              <text class="default-tag" wx:if="{{item.isDefault === true || item.isDefault === 1 || item.isDefault === '1'}}">默认</text>
            </view>
            <view class="address-detail">
              {{item.province}} {{item.city}} {{item.county}} {{item.detail}}
            </view>
          </view>
          <view class="selected-icon" wx:if="{{selectedAddress && selectedAddress.id === item.id}}">✓</view>
        </view>
        <view class="add-address-option" bindtap="addNewAddress">
          <text>+ 添加新地址</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view> 