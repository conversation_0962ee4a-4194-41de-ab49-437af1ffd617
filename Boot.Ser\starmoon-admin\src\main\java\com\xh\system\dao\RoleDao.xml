<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.RoleDao">

    <resultMap type="com.xh.system.domain.entity.Role" id="RoleMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
        <result property="roleCode" column="role_code" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="detail">
        id, role_name,data_scope, role_code, sort, create_time, update_time, note
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="RoleMap">
        select
            <include refid="detail"></include>
        from sys_role
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="RoleMap">
        select
          *
        from sys_role
        <where>
            <if test="roleName != null and roleName != ''">
                and role_name like CONCAT('%',#{roleName},'%')
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="roleName !=null and '' != roleName">
                role_name,
            </if>
            <if test="roleCode !=null and '' != roleCode">
                role_code,
            </if>
            <if test="dataScope != null">
                data_scope,
            </if>
            <if test="sort !=null">
                sort,
            </if>
            create_time,
            <if test="note !=null and '' != note">
                note,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="roleName !=null and '' != roleName">
                #{roleName},
            </if>
            <if test="roleCode !=null and '' != roleCode">
                #{roleCode},
            </if>
            <if test="dataScope != null">
                #{dataScope},
            </if>
            <if test="sort !=null">
                #{sort},
            </if>
                now(),
            <if test="note !=null and '' != note">
                #{note},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_role
        <set>
            <if test="roleName != null and roleName != ''">
                role_name = #{roleName},
            </if>
            <if test="roleCode != null and roleCode != ''">
                role_code = #{roleCode},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="dataScope != null">
                data_scope = #{dataScope},
            </if>
            <if test="note != null and note != ''">
                note = #{note},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_role where id = #{id}
    </delete>
</mapper>

