package com.xh.auth.controller;

import com.xh.auth.domain.MemberAccessToken;
import com.xh.auth.domain.RealNameForm;
import com.xh.auth.server.MemberAuthService;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.security.token.AccessToken;
import com.xh.common.security.token.TokenService;
import com.xh.common.web.domain.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户端")
@RestController
@RequestMapping("users-api/v1/auth")
public class MemberAuthController {

    @Autowired
    private MemberAuthService memberAuthService;

    @Autowired
    private TokenService tokenService;

    @Parameter(name = "code", description = "微信授权码")
    @Operation(summary = "用户端获取授权令牌")
    @PostMapping("token")
    public ResponseEntity<MemberAccessToken> token(@RequestParam("code") String code) {
        return ResponseEntity.success(memberAuthService.auth(code));
    }

    @Operation(summary = "刷新登录令牌")
    @PostMapping("refresh-token")
    @OperationLog(title = "刷新令牌")
    public ResponseEntity<AccessToken> refresh(@RequestParam("refreshToken") String refreshToken){
        return ResponseEntity.success(tokenService.refreshByRefresh(refreshToken));
    }

    @Operation(summary = "用户端实名认证")
    @PostMapping("real-name")
    public ResponseEntity<Boolean> realNameAuth(@Valid @RequestBody RealNameForm dto) {
        return ResponseEntity.success(memberAuthService.realName(dto));
    }

}
