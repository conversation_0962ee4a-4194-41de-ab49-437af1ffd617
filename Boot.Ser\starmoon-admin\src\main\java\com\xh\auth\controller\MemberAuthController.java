package com.xh.auth.controller;

import com.xh.auth.domain.MemberAccessToken;
import com.xh.auth.domain.RealNameForm;
import com.xh.auth.server.MemberAuthService;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.security.token.AccessToken;
import com.xh.common.security.token.TokenService;
import com.xh.common.web.domain.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Tag(name = "用户端")
@RestController
@RequestMapping("users-api/v1/auth")
public class MemberAuthController {

    @Autowired
    private MemberAuthService memberAuthService;

    @Autowired
    private TokenService tokenService;

    @Parameter(name = "code", description = "微信授权码")
    @Operation(summary = "用户端获取授权令牌")
    @PostMapping("token")
    public ResponseEntity<MemberAccessToken> token(@RequestParam("code") String code) {
        return ResponseEntity.success(memberAuthService.auth(code));
    }

    @Operation(summary = "刷新登录令牌")
    @PostMapping("refresh-token")
    @OperationLog(title = "刷新令牌")
    public ResponseEntity<AccessToken> refresh(@RequestParam("refreshToken") String refreshToken){
        return ResponseEntity.success(tokenService.refreshByRefresh(refreshToken));
    }

    @Operation(summary = "发送短信验证码")
    @PostMapping("send-sms-code")
    public ResponseEntity<Map<String, String>> sendSmsCode(@RequestBody Map<String, String> params) {
        String phone = params.get("phone");
        if (phone == null || phone.trim().isEmpty()) {
            return ResponseEntity.error("手机号不能为空");
        }

        // 验证手机号格式
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            return ResponseEntity.error("手机号格式不正确");
        }

        String codeKey = memberAuthService.sendSmsCode(phone);
        Map<String, String> result = new HashMap<>();
        result.put("codeKey", codeKey);
        return ResponseEntity.success(result);
    }

    @Operation(summary = "用户注册")
    @PostMapping("register")
    public ResponseEntity<MemberAccessToken> register(@RequestBody Map<String, String> params) {
        String code = params.get("code");
        String phone = params.get("phone");
        String smsCode = params.get("smsCode");
        String codeKey = params.get("codeKey");

        if (code == null || code.trim().isEmpty()) {
            return ResponseEntity.error("微信授权码不能为空");
        }
        if (phone == null || phone.trim().isEmpty()) {
            return ResponseEntity.error("手机号不能为空");
        }
        if (smsCode == null || smsCode.trim().isEmpty()) {
            return ResponseEntity.error("验证码不能为空");
        }
        if (codeKey == null || codeKey.trim().isEmpty()) {
            return ResponseEntity.error("验证码key不能为空");
        }

        return ResponseEntity.success(memberAuthService.register(code, phone, smsCode, codeKey));
    }

    @Operation(summary = "用户端实名认证")
    @PostMapping("real-name")
    public ResponseEntity<Boolean> realNameAuth(@Valid @RequestBody RealNameForm dto) {
        return ResponseEntity.success(memberAuthService.realName(dto));
    }

}
