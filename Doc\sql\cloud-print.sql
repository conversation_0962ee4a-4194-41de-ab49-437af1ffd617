/*
 Navicat Premium Data Transfer

 Source Server         : 内网199
 Source Server Type    : MySQL
 Source Server Version : 100603
 Source Host           : ************:3308
 Source Schema         : cloud-print

 Target Server Type    : MySQL
 Target Server Version : 100603
 File Encoding         : 65001

 Date: 24/07/2025 08:10:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`  (
  `id` bigint NOT NULL COMMENT '订单编号',
  `shipping_no` int NULL DEFAULT NULL COMMENT '运单号',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `pay_type` tinyint NULL DEFAULT 0 COMMENT '支付类型',
  `pay_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '总金额',
  `shipping_amount` decimal(10, 2) NOT NULL COMMENT '运输金额',
  `product_amount` decimal(10, 2) NOT NULL COMMENT '产品金额',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '订单状态',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态',
  `cancel_explain` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消说明',
  `refund_explain` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款说明',
  `note` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `refund_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order
-- ----------------------------
INSERT INTO `order` VALUES (7339345590565732352, NULL, NULL, 0, NULL, 0.00, 0.00, 0.00, 'o7nun7eStCo5fseNxeSceVHuz-Q0', NULL, 0, 0, NULL, NULL, NULL, '2025-06-14 01:39:07', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7339347081984737280, NULL, NULL, 0, NULL, 0.00, 0.00, 0.00, 'o7nun7eStCo5fseNxeSceVHuz-Q0', NULL, 0, 0, NULL, NULL, NULL, '2025-06-14 01:45:02', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7339347357307240448, NULL, NULL, 0, NULL, 0.00, 0.00, 0.00, 'o7nun7eStCo5fseNxeSceVHuz-Q0', NULL, 0, 0, NULL, NULL, NULL, '2025-06-14 01:46:08', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7339571969068630016, NULL, NULL, 0, NULL, 0.00, 0.00, 0.00, 'o7nun7eStCo5fseNxeSceVHuz-Q0', NULL, 0, 0, NULL, NULL, NULL, '2025-06-14 16:38:40', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for order_detail
-- ----------------------------
DROP TABLE IF EXISTS `order_detail`;
CREATE TABLE `order_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '订单编号',
  `spu_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `spu_file_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径',
  `spu_amount` decimal(10, 2) NOT NULL COMMENT '商品金额',
  `pay_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `quantity` int NOT NULL COMMENT '购买数量',
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '产品规格',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `sku_id` bigint NULL DEFAULT NULL COMMENT 'skuid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 318 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_detail
-- ----------------------------
INSERT INTO `order_detail` VALUES (306, 7339344732629237760, '女孩 闭眼 唯美古风 梅花 4K动漫壁纸3840x2400_彼岸图网.jpg', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (307, 7339345590565732352, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (308, 7339347081984737280, 'a.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (309, 7339347357307240448, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (310, 7339347628750012416, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (311, 7339347827731988480, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (312, 7339347957193375744, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (313, 7339348035584917504, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (314, 7339348304041345024, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (315, 7339348590109655040, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (316, 7339348745093382144, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);
INSERT INTO `order_detail` VALUES (317, 7339571969068630016, '2023英语中考.pdf', NULL, 0.00, NULL, 1, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for print_sku
-- ----------------------------
DROP TABLE IF EXISTS `print_sku`;
CREATE TABLE `print_sku`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '规格',
  `specs_snapshot` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '规格快照',
  `amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '金额',
  `image` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格图片',
  `note` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格备注',
  `create_time` datetime NOT NULL DEFAULT current_timestamp COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 591 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品交易单元' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of print_sku
-- ----------------------------
INSERT INTO `print_sku` VALUES (580, 'A4/黑色/喷墨', '{\"大小\":\"A4\",\"色彩\":\"黑色\",\"打印方式\":\"喷墨\"}', 1.00, NULL, NULL, '2025-07-22 15:25:43', NULL);
INSERT INTO `print_sku` VALUES (583, 'A5/黑色/激光', '{\"大小\":\"A5\",\"色彩\":\"黑色\",\"打印方式\":\"激光\"}', 1.00, NULL, NULL, '2025-07-22 15:25:43', NULL);
INSERT INTO `print_sku` VALUES (585, 'A5/彩色/激光', '{\"大小\":\"A5\",\"色彩\":\"彩色\",\"打印方式\":\"激光\"}', 1.00, NULL, NULL, '2025-07-22 15:25:43', NULL);
INSERT INTO `print_sku` VALUES (586, 'A5/彩色/喷墨', '{\"大小\":\"A5\",\"色彩\":\"彩色\",\"打印方式\":\"喷墨\"}', 1.00, NULL, NULL, '2025-07-22 15:25:43', NULL);
INSERT INTO `print_sku` VALUES (587, 'A3/黑色/激光', '{\"大小\":\"A3\",\"色彩\":\"黑色\",\"打印方式\":\"激光\"}', 1.00, NULL, NULL, '2025-07-22 15:25:43', NULL);
INSERT INTO `print_sku` VALUES (589, 'A3/彩色/激光', '{\"大小\":\"A3\",\"色彩\":\"彩色\",\"打印方式\":\"激光\"}', 1.00, NULL, NULL, '2025-07-22 15:25:43', NULL);

-- ----------------------------
-- Table structure for print_specs_name
-- ----------------------------
DROP TABLE IF EXISTS `print_specs_name`;
CREATE TABLE `print_specs_name`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '属性名编号',
  `specs_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 186 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格名表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of print_specs_name
-- ----------------------------
INSERT INTO `print_specs_name` VALUES (183, '大小');
INSERT INTO `print_specs_name` VALUES (184, '色彩');
INSERT INTO `print_specs_name` VALUES (185, '打印方式');

-- ----------------------------
-- Table structure for print_specs_value
-- ----------------------------
DROP TABLE IF EXISTS `print_specs_value`;
CREATE TABLE `print_specs_value`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `specs_name_id` bigint NULL DEFAULT NULL COMMENT '规格id',
  `specs_value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格值',
  `description` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of print_specs_value
-- ----------------------------
INSERT INTO `print_specs_value` VALUES (14, 183, 'A4', '');
INSERT INTO `print_specs_value` VALUES (15, 183, 'A5', '');
INSERT INTO `print_specs_value` VALUES (16, 183, 'A3', '');
INSERT INTO `print_specs_value` VALUES (17, 184, '黑色', '');
INSERT INTO `print_specs_value` VALUES (18, 184, '彩色', '');
INSERT INTO `print_specs_value` VALUES (19, 185, '激光', '');
INSERT INTO `print_specs_value` VALUES (20, 185, '喷墨', '');

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name_cn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_en` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `css_class` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `parent_id` int NULL DEFAULT 0,
  `sort` int NULL DEFAULT 0,
  `note` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 159 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据字典' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict` VALUES (123, '通用状态', 'common_status', '#6EFF00', '2025-05-22 09:49:09', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (125, '正常', 'nomal', '#52c41a', '2025-05-22 22:07:32', NULL, 123, 0, NULL);
INSERT INTO `sys_dict` VALUES (126, '异常', 'abnormal', '#f5222d', '2025-05-22 22:08:13', NULL, 123, 1, NULL);
INSERT INTO `sys_dict` VALUES (127, '系统图文', 'sys_imagetext', '#52c41a', '2025-05-22 22:09:35', '2025-05-26 09:17:37', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (128, '轮播图', 'banner', NULL, '2025-05-22 22:10:12', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (133, '关于我们', 'about_us', NULL, '2025-05-22 22:11:54', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (134, '隐私协议', 'privacy_policy', NULL, '2025-05-22 22:12:21', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (143, '组织类型', 'org_type', NULL, '2025-05-26 13:54:21', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (144, '区域', 'region', NULL, '2025-05-26 13:54:45', NULL, 143, 0, NULL);
INSERT INTO `sys_dict` VALUES (145, '部门', 'dept', NULL, '2025-05-26 13:54:56', NULL, 143, 0, NULL);
INSERT INTO `sys_dict` VALUES (146, '房屋类型', 'building_type', NULL, '2025-05-26 13:55:12', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (147, '楼栋', 'building', NULL, '2025-05-26 13:55:30', NULL, 146, 0, NULL);
INSERT INTO `sys_dict` VALUES (148, '分区(单元)', 'unit', NULL, '2025-05-26 13:56:19', NULL, 146, 0, NULL);
INSERT INTO `sys_dict` VALUES (149, '房间', 'room', NULL, '2025-05-26 13:56:32', NULL, 146, 0, NULL);
INSERT INTO `sys_dict` VALUES (153, '菜单类型', 'menu_type', NULL, '2025-05-27 19:34:26', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (154, '菜单', 'menu', NULL, '2025-05-27 19:34:35', NULL, 153, 0, NULL);
INSERT INTO `sys_dict` VALUES (155, '权限', 'permis', NULL, '2025-05-27 19:35:31', NULL, 153, 0, NULL);
INSERT INTO `sys_dict` VALUES (156, '打印教程', 'print_tu', 'info', '2025-06-01 14:05:04', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (157, '试卷打印', 'test_paper', NULL, '2025-06-01 15:22:41', NULL, 127, 0, NULL);
INSERT INTO `sys_dict` VALUES (158, '价格表', 'price', NULL, '2025-06-01 17:02:51', NULL, 127, 0, NULL);

-- ----------------------------
-- Table structure for sys_imagetext
-- ----------------------------
DROP TABLE IF EXISTS `sys_imagetext`;
CREATE TABLE `sys_imagetext`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图文id',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `image_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配图',
  `content` longblob NULL COMMENT '内容',
  `link` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接',
  `sort` bigint NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布者用户id',
  `type` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型（关联字典：system_imagetext，notic_imagetext）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `extend_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展数据',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统图文表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_imagetext
-- ----------------------------
INSERT INTO `sys_imagetext` VALUES (4, 'aaa', 'assets/5484249744444f058077afd8530e5948.jpg', 0x3C703EF09F98873C2F703E, NULL, NULL, 'system', 'banner', '2025-05-31 11:36:42', '2025-05-31 11:57:44', NULL);
INSERT INTO `sys_imagetext` VALUES (5, 'aaaa', 'assets/3d8de31d628c431784394e23a9a733dd.jpg', NULL, NULL, NULL, 'system', 'banner', '2025-05-31 11:56:28', '2025-06-01 14:00:24', NULL);
INSERT INTO `sys_imagetext` VALUES (6, '装订价格', 'assets/95266183be954bfc91b8f1ae7e63b7df.jpg', 0x3C7461626C6520626F726465723D2230222077696474683D2231303025222063656C6C70616464696E673D2230222063656C6C73706163696E673D223022207374796C653D22746578742D616C69676E3A6C6566743B223E3C74626F64793E3C74723E3C74683EE8A385E8AEA23C2F74683E3C74683EE4BBB7E6A0BC3C2F74683E3C2F74723E3C74723E3C74643EE4B88DE8A385E992893C2F74643E3C74643E30E585833C2F74643E3C2F74723E3C74723E3C74643EE8AEA2E4B9A6E992883C2F74643E3C74643E32E58583266E6273703B20266E6273703B20266E6273703B3C2F74643E3C2F74723E3C2F74626F64793E3C2F7461626C653E, NULL, NULL, 'system', 'price', '2025-06-01 14:06:36', '2025-06-14 12:38:52', NULL);
INSERT INTO `sys_imagetext` VALUES (8, '纸张价格表', 'assets/cc8692229bbd46d7b884e078fda86843.jpg', 0x3C707265207374796C653D22746578742D616C69676E3A6C6566743B223E3C703E3C623E3C2F623E3C2F703E3C7072653E3C636F646520636C6173733D224A617661223E3C7461626C6520626F726465723D2230222077696474683D2231303025222063656C6C70616464696E673D2230222063656C6C73706163696E673D2230223E3C74626F64793E3C74723E3C74683EE7BAB8E5BCA03C2F74683E3C74683EE4BBB7E6A0BC3C2F74683E3C2F74723E3C74723E3C74643E4134202020202020203C2F74643E3C74643E3130E5BCA02FE585833C2F74643E3C2F74723E3C74723E3C74643E41333C2F74643E3C74643E35E5BCA02FE585833C2F74643E3C2F74723E3C74723E3C74643E41353C2F74643E3C74643E3230E5BCA02FE585833C2F74643E3C2F74723E3C74723E3C74643EE8BF90E8B4B93C2F74643E3C74643EE4BBA5E4B88AE4B88DE58C85E590ABE8BF90E8B4B93C2F74643E3C2F74723E3C2F74626F64793E3C2F7461626C653E3C703E3C62722F3E3C2F703E3C2F636F64653E3C2F7072653E3C2F7072653E, NULL, NULL, 'system', 'price', '2025-06-01 17:18:19', '2025-06-14 12:37:31', NULL);
INSERT INTO `sys_imagetext` VALUES (9, '隐私协议', NULL, 0x3C7072653E3C703E3C623E3C2F623E3C2F703E3C7072653E3C703EE8BF99E698AFE99A90E7A781E58D8FE8AEAE3C2F703E3C703E3C62722F3E3C2F703E3C703EF09F988B3C2F703E3C2F7072653E3C2F7072653E, NULL, NULL, 'system', 'privacy_policy', '2025-06-01 18:02:37', NULL, NULL);
INSERT INTO `sys_imagetext` VALUES (10, '关于我们', NULL, 0x3C7072653E3C703E3C623E3C2F623E3C2F703E3C7072653E3C703EE8BF99E698AFE585B3E4BA8EE68891E4BBAC0A0A0AF09F98840AF09F988B3C2F703E3C2F7072653E3C2F7072653E, NULL, NULL, 'system', 'about_us', '2025-06-01 18:02:59', NULL, NULL);
INSERT INTO `sys_imagetext` VALUES (11, '打印教程', NULL, 0x3C7072653E3C703E3C623E3C2F623E3C2F703E3C7072653E3C703EE68993E58DB0E69599E7A88B6161616161616161613C2F703E3C703E643C2F703E3C703E3C666F6E7420636F6C6F723D2223663939363362223E6464646464646464646464646464646464646464643C2F666F6E743E3C2F703E3C703E613C2F703E3C703E77777777777777777777773C2F703E3C2F7072653E3C2F7072653E, NULL, NULL, 'system', 'print_tu', '2025-06-01 18:04:01', NULL, NULL);
INSERT INTO `sys_imagetext` VALUES (12, '试卷打印', NULL, 0x3C6F6C3E3C6C693E3C703E3C623E3C2F623E3C2F703E3C7072653E3C703EE8AF95E58DB7E68993E58DB03C2F703E3C2F7072653E3C2F6C693E3C6C693E3C703E613C2F703E3C2F6C693E3C6C693E3C703E623C2F703E3C2F6C693E3C6C693E3C703E633C2F703E3C2F6C693E3C2F6F6C3E, NULL, NULL, 'system', 'test_paper', '2025-06-01 18:04:42', NULL, NULL);

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`  (
  `id` bigint NOT NULL DEFAULT 0 COMMENT 'id',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方法（GET,POST,...）',
  `request_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求地址（/user?id=1）',
  `request_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求体',
  `response_code` tinyint NULL DEFAULT NULL COMMENT '响应状态码',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `org_id` bigint NULL DEFAULT NULL COMMENT '组织id',
  `ip_address` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `consume_time` int NULL DEFAULT NULL COMMENT '耗时',
  `error_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误消息',
  `error_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误码',
  `extra_data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展数据(记录修改之前，和修改之后)',
  `target_id` bigint NULL DEFAULT NULL COMMENT '操作对象',
  `operation_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
  `operation_desc` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `menu_type` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M菜单 P权限）',
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '父菜单ID',
  `sort` int NULL DEFAULT 0 COMMENT '序号',
  `path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `permission` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `component_path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `note` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `extend_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 184 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 'menu', 0, 1, 'system', NULL, NULL, NULL, '2021-10-05 10:40:22', '2025-05-27 08:52:43', '系统管理目录', NULL);
INSERT INTO `sys_menu` VALUES (100, '用户管理', 'menu', 1, 1, 'user', NULL, 'system/userList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:49', '用户管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (101, '角色管理', 'menu', 1, 2, 'role', NULL, 'system/roleList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:52', '角色管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 'menu', 1, 3, 'menu', NULL, 'system/menuList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:55', '菜单管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (105, '数据字典', 'menu', 1, 4, 'dict', NULL, 'system/dictList', NULL, '2021-10-05 10:40:22', '2025-05-27 15:21:59', '字典管理菜单', NULL);
INSERT INTO `sys_menu` VALUES (110, '添加', 'permis', 100, 0, '', 'sys:user:add', NULL, NULL, '2021-12-15 16:58:35', '2025-05-27 08:52:43', NULL, NULL);
INSERT INTO `sys_menu` VALUES (123, '添加', 'permis', 102, 0, '', 'sys:menu:add', NULL, NULL, '2021-12-15 16:58:35', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (124, '编辑', 'permis', 101, 0, '', 'sys:role:edit', NULL, NULL, '2021-12-15 16:58:35', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (126, '编辑', 'permis', 102, 0, '', 'sys:menu:edit', NULL, NULL, '2022-01-22 20:21:42', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (127, '删除', 'permis', 102, 0, '', 'sys:menu:delete', NULL, NULL, '2022-01-22 20:22:04', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (128, '添加', 'permis', 105, 0, '', 'sys:dict:add', NULL, NULL, '2022-02-06 18:03:36', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (129, '添加', 'permis', 101, 0, '', 'sys:role:add', NULL, NULL, '2022-02-06 18:09:09', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (130, '编辑', 'permis', 100, 0, '', 'sys:user:edit', NULL, NULL, '2022-02-06 18:20:21', '2025-05-27 08:52:43', '', NULL);
INSERT INTO `sys_menu` VALUES (166, '图文管理', 'menu', 1, 10, 'imagetext', NULL, 'system/imagetextList', NULL, '2025-05-27 08:52:27', '2025-05-27 15:24:16', '', NULL);
INSERT INTO `sys_menu` VALUES (177, '微信用户', 'menu', 178, 0, 'wecaht', NULL, 'system/memberList', '#', '2025-05-31 12:57:49', '2025-06-07 20:03:46', '', NULL);
INSERT INTO `sys_menu` VALUES (178, '用户管理', 'menu', 0, 3, 'user', NULL, NULL, '#', '2025-06-05 21:18:03', '2025-06-07 20:02:59', '', NULL);
INSERT INTO `sys_menu` VALUES (179, '地址管理', '', 178, 0, 'address', NULL, 'system/addressList', '#', '2025-06-05 21:18:42', NULL, '', NULL);
INSERT INTO `sys_menu` VALUES (180, '打印管理', 'menu', 0, 5, 'print', NULL, NULL, '#', '2025-06-07 14:21:33', '2025-06-07 20:02:46', '', NULL);
INSERT INTO `sys_menu` VALUES (181, '打印规格', 'menu', 180, 0, 'settings', NULL, 'print/printSpecsList', '#', '2025-06-07 14:32:39', '2025-06-13 12:05:41', '', NULL);
INSERT INTO `sys_menu` VALUES (183, '打印订单', 'menu', 180, 0, 'orderList', NULL, 'order/orderList', '#', '2025-06-13 23:29:26', NULL, '', NULL);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `sort` int NULL DEFAULT 0 COMMENT '显示顺序',
  `data_scope` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据范围',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'superAdmin', 1, NULL, '2021-10-05 10:40:22', '2022-02-06 18:07:51', '超级管理员');
INSERT INTO `sys_role` VALUES (2, '管理员', 'admin', 2, NULL, '2021-10-05 10:40:22', '2022-05-16 16:03:23', '普通角色');

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 1);
INSERT INTO `sys_role_menu` VALUES (1, 100);
INSERT INTO `sys_role_menu` VALUES (1, 101);
INSERT INTO `sys_role_menu` VALUES (1, 102);
INSERT INTO `sys_role_menu` VALUES (1, 105);
INSERT INTO `sys_role_menu` VALUES (1, 110);
INSERT INTO `sys_role_menu` VALUES (1, 123);
INSERT INTO `sys_role_menu` VALUES (1, 124);
INSERT INTO `sys_role_menu` VALUES (1, 126);
INSERT INTO `sys_role_menu` VALUES (1, 127);
INSERT INTO `sys_role_menu` VALUES (1, 128);
INSERT INTO `sys_role_menu` VALUES (1, 129);
INSERT INTO `sys_role_menu` VALUES (1, 130);
INSERT INTO `sys_role_menu` VALUES (1, 166);
INSERT INTO `sys_role_menu` VALUES (1, 177);
INSERT INTO `sys_role_menu` VALUES (1, 178);
INSERT INTO `sys_role_menu` VALUES (1, 179);
INSERT INTO `sys_role_menu` VALUES (1, 180);
INSERT INTO `sys_role_menu` VALUES (1, 181);
INSERT INTO `sys_role_menu` VALUES (1, 182);
INSERT INTO `sys_role_menu` VALUES (1, 183);
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 110);
INSERT INTO `sys_role_menu` VALUES (2, 113);
INSERT INTO `sys_role_menu` VALUES (2, 122);
INSERT INTO `sys_role_menu` VALUES (2, 125);
INSERT INTO `sys_role_menu` VALUES (2, 130);
INSERT INTO `sys_role_menu` VALUES (2, 132);
INSERT INTO `sys_role_menu` VALUES (2, 137);
INSERT INTO `sys_role_menu` VALUES (2, 138);
INSERT INTO `sys_role_menu` VALUES (2, 139);
INSERT INTO `sys_role_menu` VALUES (2, 140);
INSERT INTO `sys_role_menu` VALUES (2, 141);
INSERT INTO `sys_role_menu` VALUES (2, 142);
INSERT INTO `sys_role_menu` VALUES (7, 1);
INSERT INTO `sys_role_menu` VALUES (7, 105);
INSERT INTO `sys_role_menu` VALUES (7, 113);
INSERT INTO `sys_role_menu` VALUES (7, 122);
INSERT INTO `sys_role_menu` VALUES (7, 125);
INSERT INTO `sys_role_menu` VALUES (7, 128);
INSERT INTO `sys_role_menu` VALUES (8, 1);
INSERT INTO `sys_role_menu` VALUES (9, 113);
INSERT INTO `sys_role_menu` VALUES (9, 122);
INSERT INTO `sys_role_menu` VALUES (9, 125);
INSERT INTO `sys_role_menu` VALUES (9, 132);
INSERT INTO `sys_role_menu` VALUES (9, 137);

-- ----------------------------
-- Table structure for sys_settings
-- ----------------------------
DROP TABLE IF EXISTS `sys_settings`;
CREATE TABLE `sys_settings`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `settings_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `setting_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_settings
-- ----------------------------
INSERT INTO `sys_settings` VALUES (2, 'auto_print', '1', '自动打印（0：否，1：是）', '2025-06-13 22:05:46', NULL);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `gender` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别',
  `avatar_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `status` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '帐号状态',
  `org_id` bigint NOT NULL COMMENT '组织',
  `role_id` bigint NOT NULL COMMENT '角色',
  `note` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7091961589154185217 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'system', '1', '12121', '$2a$10$mrL/kCDGGSLWBDr0b0JyquBanoZnXoiNrupX42r9AdlGpB.7Ze8Gy', '111111', '1', '', '1', 1, 1, '1212', '2023-07-21 13:38:27', '2025-06-14 11:50:30');

-- ----------------------------
-- Table structure for t_address
-- ----------------------------
DROP TABLE IF EXISTS `t_address`;
CREATE TABLE `t_address`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `recipient` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收件人',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `province` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市',
  `county` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区县',
  `town` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '乡镇',
  `detail` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `is_default` bit(1) NOT NULL COMMENT '默认地址',
  `user_id` int NULL DEFAULT NULL COMMENT '用户id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_address
-- ----------------------------
INSERT INTO `t_address` VALUES (1, '张三', '12345677777', '北京市', '北京市', '东城区', '', '11a', b'0', 1, '2025-06-05 13:58:08', '2025-06-05 14:32:38');
INSERT INTO `t_address` VALUES (3, '李四', '17665322521', '北京市', '北京市', '东城区', '', 'll', b'1', 1, '2025-06-14 16:42:41', NULL);

-- ----------------------------
-- Table structure for t_member
-- ----------------------------
DROP TABLE IF EXISTS `t_member`;
CREATE TABLE `t_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '微信用户ID',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信小程序用户openid',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信用户统一ID',
  `user_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `gender` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别(man：男；woman：女)',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `resident_id` bigint NULL DEFAULT NULL COMMENT '住户id',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `role` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'tourist' COMMENT '用户角色',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户端用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_member
-- ----------------------------
INSERT INTO `t_member` VALUES (1, 'o7nun7eStCo5fseNxeSceVHuz-Q0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_member` VALUES (4, 'o7nun7Vu_NJEk9ycojATCUoSipok', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'tourist');
INSERT INTO `t_member` VALUES (5, 'o7nun7a9uuYlWTOSj6XlTtnz5M4U', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'tourist');

-- ----------------------------
-- Table structure for temp_file
-- ----------------------------
DROP TABLE IF EXISTS `temp_file`;
CREATE TABLE `temp_file`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `file_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sku_id` bigint NULL DEFAULT 0,
  `page_num` int NULL DEFAULT NULL,
  `page_size` int NULL DEFAULT NULL,
  `quantity` int NULL DEFAULT 1,
  `specs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of temp_file
-- ----------------------------
INSERT INTO `temp_file` VALUES (16, '山东省淄博市2023年中考化学真题（原卷版）.pdf', 'assets/2f87cf7eb95f424db70532acdee6690b.pdf', 'pdf', 586, 1, 8, 5, 'A5/彩色/喷墨', NULL, NULL, 'o7nun7eStCo5fseNxeSceVHuz-Q0');
INSERT INTO `temp_file` VALUES (17, '3838732f-befb-4af3-9b45-07c082c898cb_1752665615889927043_origin~tplv-a9rns2rl98-image-dark-watermark.png', 'assets/21205b03811a4a54992993d6c913710f.png', 'png', 587, 1, 1, 1, 'A3/黑色/激光', NULL, NULL, 'o7nun7eStCo5fseNxeSceVHuz-Q0');
INSERT INTO `temp_file` VALUES (24, '图片802645', 'assets/1698787f68f7496980334a3d9f6c6a25.jpg', 'jpeg', 583, 1, 1, 1, 'A5/黑色/激光', NULL, NULL, 'o7nun7eStCo5fseNxeSceVHuz-Q0');

SET FOREIGN_KEY_CHECKS = 1;
