package com.xh.common.security.user;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户信息表(User)实体类
 *
 * <AUTHOR>
 * @since 2025-05-20 09:50:27
 */
@Data
public class UserDetail  implements Serializable {
    private static final long serialVersionUID = 3336621747650666231L;

    private Long id;

    private String userName;

    private String nickName;

    private String password;

    private String status;

    private Integer sex;

    private Long orgId;

    private String avatarUrl;

    private String roleCode;

    private String phone;

    private Long roleId;

    private String permissions;

}

