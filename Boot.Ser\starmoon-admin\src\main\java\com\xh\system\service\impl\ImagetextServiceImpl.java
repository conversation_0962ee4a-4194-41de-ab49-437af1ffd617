package com.xh.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.xh.common.security.user.UserDetail;
import com.xh.common.utils.SecurityUtils;
import com.xh.system.dao.ImagetextDao;
import com.xh.system.domain.dto.ImagetextCreateDTO;
import com.xh.system.domain.dto.ImagetextEditDTO;
import com.xh.system.domain.entity.Imagetext;
import com.xh.system.service.ImagetextService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 系统图文表(Imagetext)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21 08:30:39
 */
@Service
public class ImagetextServiceImpl implements ImagetextService {

    @Autowired
    private ImagetextDao imagetextDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public Imagetext queryById(Long id) {
        return imagetextDao.queryById(id);
    }

    /**
     * 通过分页查询
     *
     * @return 查询结果
     */
    @Override
    public PageInfo<Imagetext> queryByPage(String type) {
        return new PageInfo<>(imagetextDao.queryByList(type));
    }

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Override
    public Long insert(ImagetextCreateDTO dto) {
        Imagetext imagetext = new Imagetext();
        BeanUtils.copyProperties(dto,imagetext);
        UserDetail userDetail = SecurityUtils.currentPrincipal(UserDetail.class);
        imagetext.setCreateBy(userDetail.getUserName());
        imagetextDao.insert(imagetext);
        return imagetext.getId();
    }

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean update(ImagetextEditDTO dto) {
        Imagetext imagetext = new Imagetext();
        BeanUtils.copyProperties(dto,  imagetext);
        return imagetextDao.update(imagetext) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return imagetextDao.deleteById(id) > 0;
    }
}
