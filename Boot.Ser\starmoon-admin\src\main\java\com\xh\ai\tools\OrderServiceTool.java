package com.xh.ai.tools;

import com.xh.common.web.domain.ResponseEntity;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;



/**
 * <AUTHOR>
 */
@Service
public class OrderServiceTool {


    @Tool(description = "查询订单",returnDirect = true)
    public ResponseEntity<String> queryOrderByList(
            @ToolParam(description = "用户名",required = false) String userName,
            @ToolParam(description = "状态码",required = false) Integer status,
            ToolContext toolContext
    ) {
        return ResponseEntity.success("success");
    }
}
