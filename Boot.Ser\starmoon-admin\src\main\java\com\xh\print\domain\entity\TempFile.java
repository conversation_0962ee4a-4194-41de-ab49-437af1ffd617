package com.xh.print.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (TempFile)实体类
 *
 * <AUTHOR>
 * @since 2025-07-21 15:50:29
 */
@Data
public class TempFile implements Serializable {

    private static final long serialVersionUID = 583041718260140517L;

    private Long id;

    private String fileName;

    private String filePath;

    private String type;

    private Long skuId;

    private Double unitPrice;

    private Integer pageNum;

    private Integer pageSize;

    private Integer quantity;

    private String specs;

    private Date createTime;

    private Date updateTime;

    private String openid;

    /**
     * 文件是否存在，用于标记文件是否已被删除
     */
    private Boolean fileExists;

}

