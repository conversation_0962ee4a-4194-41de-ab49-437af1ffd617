package com.xh.common.wechat.user;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ResidentDetail implements Serializable {

    private static final long serialVersionUID = 202505249780001L;
    /**
     * 住户id
     */
    private Long id;
    /**
     * 住户姓名
     */
    private String residentName;
    /**
     * 生日
     */
    private Date birthday;
    /**
     * 性别
     */
    private String gender;
    /**
     * 证件类型
     */
    private String certificateType;
    /**
     * 证件号码
     */
    private String idCardNumber;
    /**
     * 籍贯
     */
    private String nativePlace;
    /**
     * 联系电话
     */
    private String phone;

}
