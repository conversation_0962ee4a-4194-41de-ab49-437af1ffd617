package com.xh.common.web.exception;

import com.xh.common.web.statecode.StatusCode;

public class ServiceException extends RuntimeException{
    private static final long serialVersionUID = 2359767895161832954L;
    private Integer code;
    private String message;

    public ServiceException(StatusCode statusCode) {
        this.code = statusCode.getCode();
        this.message = statusCode.getMessage();
    }

    public ServiceException(Integer code,String message) {
        this.code = code;
        this.message = message;
    }

    public ServiceException(String message) {
        this.code = StatusCode.FAIL.getCode();
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
