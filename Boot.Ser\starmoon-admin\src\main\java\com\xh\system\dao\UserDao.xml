<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.system.dao.UserDao">

    <resultMap type="com.xh.system.domain.entity.User" id="UserMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="avatarUrl" column="avatar_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="roleId" column="role_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="note" column="note" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="detail">
        id, user_name,password, nick_name, email, phone, gender, avatar_url, status, create_time, update_time, note,org_id,role_id
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="UserMap">
        select
          <include refid="detail"></include>
        from sys_user
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByList" resultMap="UserMap">
        SELECT
        <include refid="detail"></include>
        FROM sys_user
        <where>
            <if test="userName != null and '' != userName">
                and user_name = #{userName}
            </if>
            <if test="keyword != null and '' != keyword">
                and (user_name like CONCAT('%',#{keyword},'%') or nick_name like CONCAT('%',#{keyword},'%'))
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="orgId != null">
                and org_id in (select id from sys_org where id = #{orgId} or find_in_set(#{orgId},ancestors))
            </if>
            <if test="phone != null and '' != phone">
                and phone like CONCAT('%',#{phone},'%')
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userName !=null and '' != userName">
                user_name,
            </if>
            <if test="nickName !=null and '' != nickName">
                nick_name,
            </if>
            <if test="email !=null and '' != email">
                email,
            </if>
            <if test="password !=null and '' != password">
                password,
            </if>
            <if test="phone !=null and '' != phone">
                phone,
            </if>
            <if test="gender !=null and '' != gender">
                gender,
            </if>
            <if test="avatarUrl !=null and '' != avatarUrl">
                avatar_url,
            </if>
            <if test="status !=null">
                status,
            </if>
            create_time,
            <if test="note !=null and '' != note">
                note,
            </if>
            <if test="orgId !=null">
                org_id,
            </if>
            <if test="roleId !=null">
                role_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userName !=null and '' != userName">
                #{userName},
            </if>
            <if test="nickName !=null and '' != nickName">
                #{nickName},
            </if>
            <if test="email !=null and '' != email">
                #{email},
            </if>
            <if test="password !=null and '' != password">
                #{password},
            </if>
            <if test="phone !=null and '' != phone">
                #{phone},
            </if>
            <if test="gender !=null and '' != gender">
                #{gender},
            </if>
            <if test="avatarUrl !=null and '' != avatarUrl">
                #{avatarUrl},
            </if>
            <if test="status !=null">
                #{status},
            </if>
                now(),
            <if test="note !=null and '' != note">
                #{note},
            </if>
            <if test="orgId !=null">
                #{orgId},
            </if>
            <if test="roleId !=null">
                #{roleId},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_user
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="gender != null">
                gender = #{gender},
            </if>
            <if test="avatarUrl != null and avatarUrl != ''">
                avatar_url = #{avatarUrl},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="note != null and note != ''">
                note = #{note},
            </if>
            <if test="orgId !=null">
                org_id = #{orgId},
            </if>
            <if test="password !=null and '' != password">
                password = #{password},
            </if>
            <if test="roleId !=null">
                role_id = #{roleId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_user where id = #{id}
    </delete>

</mapper>

