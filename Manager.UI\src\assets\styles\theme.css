/* 主题变量 */
:root {
  /* 主色调 */
  --primary-color: #007bff;
  --primary-light: #4da3ff;
  --primary-dark: #0056b3;

  /* 辅助色 */
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;

  /* 中性色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-disabled: #999999;
  --border-color: #e0e0e0;
  --divider-color: #f0f0f0;
  --background-color: #f5f7fa;
  --card-background: #ffffff;

  /* 布局 */
  --header-height: 60px;
  --sidebar-width: 240px;
  --content-padding: 20px;
  --border-radius: 4px;

  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  --font-size-small: 12px;
  --font-size-base: 14px;
  --font-size-medium: 16px;
  --font-size-large: 18px;
  --font-size-xlarge: 20px;

  /* 阴影 */
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);

  /* 过渡 */
  --transition-duration: 0.3s;
}

/* 暗色主题 */
.dark-theme {
  --primary-color: #3a8ee6;
  --primary-light: #66b1ff;
  --primary-dark: #2c6cb0;

  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-disabled: #707070;
  --border-color: #444444;
  --divider-color: #333333;
  --background-color: #1a1a1a;
  --card-background: #2a2a2a;

  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  --box-shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.4);
}
