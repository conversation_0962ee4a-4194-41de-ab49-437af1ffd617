package com.xh.swagger;


import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI openapi() {

        return new OpenAPI()
                .components(new Components()
                        .addSecuritySchemes("Authorization",
                                new SecurityScheme().in(SecurityScheme.In.HEADER)
                                        .type(SecurityScheme.Type.APIKEY)
                                        .name("Authorization"))).security(List.of(new SecurityRequirement().addList("Authorization")))
                .info(new Info()
                .title("智慧物业API文档")
                .description("管理端，客户端API接口文档")
                .version("1.0"));
    }

}
