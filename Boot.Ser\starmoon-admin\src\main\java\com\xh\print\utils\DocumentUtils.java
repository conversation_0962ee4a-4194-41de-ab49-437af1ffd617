package com.xh.print.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.IOException;
import java.io.InputStream;

public class DocumentUtils {


    public static int getDocPageCount(InputStream is) throws IOException {
        try (XWPFDocument doc = new XWPFDocument(is)) {
            Integer pages = doc.getProperties().getExtendedProperties().getUnderlyingProperties().getPages();
            return pages != null && pages > 0 ? pages : Math.max(1, doc.getParagraphs().size() / 30);
        }
    }

    public static int getPptPageCount(InputStream is) throws IOException {
        try (POIFSFileSystem fs = new POIFSFileSystem(is);
             HSLFSlideShow ppt = new HSLFSlideShow(fs)) {
            return ppt.getSlides().size();
        }
    }

    public static int getPptxPageCount(InputStream is) throws IOException {
        try (XMLSlideShow ppt = new XMLSlideShow(is)) {
            return ppt.getSlides().size();
        }
    }

    public static int getPdfPageCountFromStream(InputStream is) throws IOException {
        try (PDDocument document = PDDocument.load(is)) {
            return document.getNumberOfPages();
        }
    }

}
