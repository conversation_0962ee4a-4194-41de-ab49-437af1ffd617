package com.xh.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MenuVO {

    @Schema(description = "菜单ID")
    private Long id;

    @Schema(description = "菜单名称")
    private String menuName;

    @Schema(description = "菜单类型")
    private String menuType;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "组件路径")
    private String componentPath;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "备注")
    private String note;

}
