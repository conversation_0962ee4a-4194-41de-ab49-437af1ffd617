package com.xh.common.monitor;

import lombok.Data;

import java.lang.management.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class JvmInfo {
    //堆内存
    private MemoryInfo heapMemory;
    //栈内存
    private MemoryInfo stackMemory;
    //所有线程集合
    private List<Map<String,Object>> threadInfoList;

    public static JvmInfo create(){
        JvmInfo jvm = new JvmInfo();
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heap = memoryMXBean.getHeapMemoryUsage();
        MemoryUsage nonHeap = memoryMXBean.getNonHeapMemoryUsage();

        MemoryInfo heapMemory = new MemoryInfo(heap.getInit(),heap.getUsed(),heap.getCommitted(),heap.getMax(),MemoryInfo.UNIT_MB);
        MemoryInfo nonHeapMemory = new MemoryInfo(nonHeap.getInit(),nonHeap.getUsed(),nonHeap.getCommitted(),nonHeap.getMax(),MemoryInfo.UNIT_MB);

        jvm.setHeapMemory(heapMemory);
        jvm.setStackMemory(nonHeapMemory);

        ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();

        List<Map<String,Object>> threadInfoList = new ArrayList<>();

        long[] threadIds = threadMXBean.getAllThreadIds();
        if(threadIds != null && threadIds.length > 0){
            for(ThreadInfo threadInfo : threadMXBean.getThreadInfo(threadIds)){
                Map<String, Object> map = new HashMap<>();
                map.put("threadId",threadInfo.getThreadId());
                map.put("threadName",threadInfo.getThreadName());
                map.put("status",threadInfo.getThreadState());
                threadInfoList.add(map);
            }
        }
        jvm.setThreadInfoList(threadInfoList);
        return jvm;
    }
}
