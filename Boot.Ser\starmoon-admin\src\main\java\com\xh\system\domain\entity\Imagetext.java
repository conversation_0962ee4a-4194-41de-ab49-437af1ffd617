package com.xh.system.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统图文表(Imagetext)实体类
 *
 * <AUTHOR>
 * @since 2025-05-21 08:30:39
 */
@Data
public class Imagetext implements Serializable {
    private static final long serialVersionUID = 302281498797034495L;

    @Schema(description = "图文ID")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "标题")
    private String imageUrl;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "跳转链接")
    private String link;

    @Schema(description = "排序")
    private Long sort;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "类型（banner：轮播图,notice：通知,about_us：关于我们,privacy_policy：隐私政策）")
    private String type;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

}

