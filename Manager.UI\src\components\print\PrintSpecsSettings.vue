<template>
    <el-dialog width="40%" v-model="dialog.show" destroy-on-close :title="dialog.title">
        <div class="page-content">
            <div>
                <el-button type="primary" @click="addAttr()" style="margin-bottom: 10px">添加规格项</el-button>
                <el-table max-height="800px" :data="attributeList">
                    <el-table-column prop="specsName" label="名称" width="100">
                        <template #default="scope">
                            <el-input :ref="'inputName' + scope.$index" v-model="scope.row.specsName"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="values" label="规格值">
                        <template #default="scope">
                            <el-tooltip v-for="item in scope.row.values" :key="item.specsValue" class="box-item"
                                effect="dark" :content="item.description" placement="top-start">
                                <el-tag closable style="margin-right: 5px; display: inline-flex; align-items: center;">
                                    {{ item.specsValue }}
                                </el-tag>
                            </el-tooltip>

                            <span v-if="scope.row.inputVisible" style="display:inline-flex; align-items:center;">

                                <el-input v-show="scope.row.inputShow" size="small" v-model="scope.row.newValue"
                                    placeholder="规格值" style="width: 100px"
                                    @keyup.enter="handleInputConfirm(scope.$index)"
                                    @blur="handleInputConfirm(scope.$index)">
                                </el-input>
                                <el-input v-show="scope.row.noteShow" size="small" v-model="scope.row.newRemark"
                                    placeholder="备注" style="width: 100px; margin-left:5px"
                                    @keyup.enter="focusRemarkInput(scope.$index)"
                                    @blur="focusRemarkInput(scope.$index)">
                                </el-input>

                            </span>
                            <el-button v-else size="small" @click="showInput(scope.$index)">+ 添加值</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                        <template #default="scope">
                            <el-button type="text" @click="deleteAttr(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="onSubmit" :loading="loading">提交</el-button>
      </span>
    </template>
    </el-dialog>
</template>

<script>
import { InfoFilled } from '@element-plus/icons-vue'
import {
    addSku,
    editSku,
    listSku,
    getSku
} from '@/api/print/printSettings'
import mitt from '@/utils/mitt'
export default {
    data() {
        return {
            loading: false,
            uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
            imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
            headers: {
                'Authorization': JSON.parse(localStorage.getItem("token")).access_token
            },
            searchModel: {
                pageNum: 1,
                pageSize: 10
            },
            dialog:{
                show:false
            },
            tempSpecsList: [],
            specsList: [],
            attributeList: [],
            isEdit: false,
            total: 0
        }
    },
    methods: {
        uploadSuccess(res, index) {
            console.log("res", res, index)
            if (res.code == 0) {
                const specs = this.tempSpecsList[index];
                specs.imageShow = true
                specs.imageUrl = res.data
            } else {
                this.$message.error(res.message)
            }
        },
        addAttr() {
            this.attributeList.push({ specsName: '', values: [], specs: [], inputVisible: false, newValue: '', newRemark: '' });
        },

        showInput(index) {
            const attr = this.attributeList[index];
            attr.inputVisible = true
            attr.inputShow = true
            attr.newValue = '';
            attr.newRemark = '';
            console.log("attr", attr)
        },

        focusRemarkInput(index) {
            const attr = this.attributeList[index];

            attr.values.push({ specsValue: attr.newValue, description: attr.newRemark });

            attr.specs.push(attr.newValue);

            attr.inputVisible = false;
            attr.inputShow = false
            attr.noteShow = false
            this.changeSkuList()
        },
        handleInputConfirm(index) {
            const attr = this.attributeList[index];
            attr.inputShow = false
            attr.noteShow = true
        },

        deleteAttr(index) {
            this.attributeList.splice(index, 1)
        },
   
        onSubmit() {
            this.loading = true
            var data = {
                specsList: this.attributeList
            }
            addSku(data).then(res => {
                if (res.data.code == 0) {
                    this.$emit('search')
                    this.$message.success('添加成功')
                    this.loading = false
                } else {
                    this.loading = false
                    this.$message.error(res.message)
                }
            })
        },
        changeSkuList() {
            const specs = []
            this.attributeList.forEach(item => {
                if (item.specs.length > 0) specs.push(item.specs)
            })
            const data = this.calcDescartes(specs)
            this.tempSpecsList = []
            data.forEach(item => {
                if (!Array.isArray(item)) item = [].concat(item)
                this.tempSpecsList.push({
                    specs: item,
                    amount: 0,
                    stock: 0
                })
            })
        },
        calcDescartes(array) {
            if (array.length < 2) return array[0] || [];

            return [].reduce.call(array, function (col, set) {
                const res = [];

                col.forEach(function (c) {
                    set.forEach(function (s) {
                        var t = [].concat(Array.isArray(c) ? c : [c]);
                        t.push(s);
                        res.push(t);
                    })
                });

                return res;
            })
        },
        deleted(id) {
            this.$confirm('删除地址, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteAddress(id)
                    .then(() => {
                        this.search()
                        this.$message.success('删除成功')
                    })
                    .catch(err => {
                        this.$message.error(err.data.errorMessage)
                    })
            }).catch(() => { })
        }
    },
    mounted() {
        mitt.on('openEditSpecs', () => {
            console.log("openEditSpecs")
            this.attributeList = []
            this.dialog.show = true
            this.dialog.title = '规格设置'
        })

    },
    beforeDestroy() {
        mitt.off('openEditSpecs')
        this.imageShow = false
        this.imageUrl = ''
    }
}
</script>

<style scoped>
.upload-cell {
    width: 80px;
    height: 80px;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>