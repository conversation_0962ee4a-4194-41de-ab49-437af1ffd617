/*
 Navicat Premium Data Transfer

 Source Server         : 内网199
 Source Server Type    : MySQL
 Source Server Version : 100603
 Source Host           : ************:3308
 Source Schema         : starmoon-empty

 Target Server Type    : MySQL
 Target Server Version : 100603
 File Encoding         : 65001

 Date: 16/05/2025 18:24:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`  (
  `id` bigint NOT NULL COMMENT '订单编号',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `pay_type` tinyint NOT NULL DEFAULT 0 COMMENT '支付类型',
  `pay_amount` decimal(10, 2) NOT NULL COMMENT '支付金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '总金额',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '订单状态',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态',
  `cancel_explain` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消说明',
  `refund_explain` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款说明',
  `spu_description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品描述',
  `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `org_id` bigint NULL DEFAULT NULL COMMENT '组织id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `refund_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `image` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第一个商品的图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order
-- ----------------------------
INSERT INTO `order` VALUES (1753619580061745152, NULL, 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2022-12-30 14:32:34', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1753621612730515456, NULL, 1, 0.03, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2022-12-30 15:04:52', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1753621622072279040, NULL, 1, 0.03, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, '哈哈', NULL, '2022-12-30 15:05:01', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757406722984509440, NULL, 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '张三f', NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2023-02-10 09:47:35', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757684843050696704, '4200001800202302136278647331', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 11:28:11', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757685731239657472, '4200001777202302137262458189', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 11:42:18', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757699719377715200, '4200001791202302137820344854', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 5, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 15:24:38', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757702784200736768, '4200001786202302130393613132', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 16:13:21', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757702873932627968, '4200001708202302136438448170', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 16:14:46', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1757702924141592576, '4200001784202302138020135894', 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 3, 1, NULL, NULL, NULL, NULL, NULL, '2023-02-13 16:15:34', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (1759215067580071936, NULL, 1, 0.01, NULL, 'oPxHX4s405aqZA6CdWJy6Z4QlECA', '毛东杰', NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2023-03-02 08:50:27', '2025-05-14 18:24:52', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306262733459030016, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:39:39', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306262930381602816, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:40:26', '2025-03-14 18:40:26', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306262980964909056, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:40:38', '2025-03-14 18:40:38', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263013302992896, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:40:46', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263126574366720, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:41:13', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263256077697024, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:41:44', '2025-03-14 18:41:44', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263352303419392, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:42:07', '2025-03-14 18:42:07', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306263466015195136, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 18:42:34', '2025-03-14 18:42:34', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306272696617140224, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 19:19:14', NULL, NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306272926284644352, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 19:20:09', '2025-03-14 19:20:19', NULL, NULL, NULL);
INSERT INTO `order` VALUES (7306273083868839936, NULL, 2, 0.10, NULL, '1', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, '2025-03-14 19:20:47', '2025-03-14 19:21:17', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for order_detail
-- ----------------------------
DROP TABLE IF EXISTS `order_detail`;
CREATE TABLE `order_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '订单编号',
  `spu_id` bigint NOT NULL COMMENT '产品编号',
  `spu_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `spu_amount` decimal(10, 2) NOT NULL COMMENT '商品金额',
  `pay_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `quantity` int NOT NULL COMMENT '购买数量',
  `sku_id` bigint NULL DEFAULT NULL COMMENT '规格ID',
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '产品规格',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `image` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 306 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of order_detail
-- ----------------------------
INSERT INTO `order_detail` VALUES (277, 1753619580061745152, 142, '农夫山泉', 0.01, NULL, 1, 548, '[\"200ML\", \"北京\"]', '2022-12-30 14:32:34', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (278, 1753621612730515456, 142, '农夫山泉', 0.01, NULL, 1, 548, '[\"200ML\", \"北京\"]', '2022-12-30 15:04:52', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (279, 1753621612730515456, 142, '农夫山泉', 0.01, NULL, 1, 549, '[\"200ML\", \"云南\"]', '2022-12-30 15:04:52', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (280, 1753621612730515456, 142, '农夫山泉', 0.01, NULL, 1, 551, '[\"300ML\", \"云南\"]', '2022-12-30 15:04:52', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (281, 1753621622072279040, 142, '农夫山泉', 0.01, NULL, 1, 548, '[\"200ML\", \"北京\"]', '2022-12-30 15:05:01', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (282, 1753621622072279040, 142, '农夫山泉', 0.01, NULL, 1, 549, '[\"200ML\", \"云南\"]', '2022-12-30 15:05:01', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (283, 1753621622072279040, 142, '农夫山泉', 0.01, NULL, 1, 551, '[\"300ML\", \"云南\"]', '2022-12-30 15:05:01', 'public/20221230/1672370724272.png');
INSERT INTO `order_detail` VALUES (285, 1757406722984509440, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-10 09:47:35', NULL);
INSERT INTO `order_detail` VALUES (287, 1757684843050696704, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 11:28:11', NULL);
INSERT INTO `order_detail` VALUES (288, 1757685731239657472, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 11:42:18', NULL);
INSERT INTO `order_detail` VALUES (289, 1757699719377715200, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 15:24:38', NULL);
INSERT INTO `order_detail` VALUES (290, 1757702784200736768, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-02-13 16:13:21', NULL);
INSERT INTO `order_detail` VALUES (291, 1757702873932627968, 38, '农夫三拳饮用水', 0.01, NULL, 1, 522, '[\"100ML\", \"农夫山泉\", \"国外\"]', '2023-02-13 16:14:46', NULL);
INSERT INTO `order_detail` VALUES (292, 1757702924141592576, 38, '农夫三拳饮用水', 0.01, NULL, 1, 522, '[\"100ML\", \"农夫山泉\", \"国外\"]', '2023-02-13 16:15:34', NULL);
INSERT INTO `order_detail` VALUES (293, 1759215067580071936, 38, '农夫三拳饮用水', 0.01, NULL, 1, 521, '[\"100ML\", \"农夫山泉\", \"北京\"]', '2023-03-02 08:50:27', NULL);
INSERT INTO `order_detail` VALUES (295, 7306262733459030016, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:39:39', NULL);
INSERT INTO `order_detail` VALUES (296, 7306262930381602816, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:40:26', NULL);
INSERT INTO `order_detail` VALUES (297, 7306262980964909056, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:40:38', NULL);
INSERT INTO `order_detail` VALUES (298, 7306263013302992896, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:40:46', NULL);
INSERT INTO `order_detail` VALUES (299, 7306263126574366720, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:41:13', NULL);
INSERT INTO `order_detail` VALUES (300, 7306263256077697024, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:41:44', NULL);
INSERT INTO `order_detail` VALUES (301, 7306263352303419392, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:42:07', NULL);
INSERT INTO `order_detail` VALUES (302, 7306263466015195136, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 18:42:34', NULL);
INSERT INTO `order_detail` VALUES (303, 7306272696617140224, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 19:19:14', NULL);
INSERT INTO `order_detail` VALUES (304, 7306272926284644352, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 19:20:09', NULL);
INSERT INTO `order_detail` VALUES (305, 7306273083868839936, 38, '农夫山泉', 0.01, NULL, 10, 520, '[\"100ML\", \"农夫山泉\", \"河南\"]', '2025-03-14 19:20:47', NULL);

-- ----------------------------
-- Table structure for spu
-- ----------------------------
DROP TABLE IF EXISTS `spu`;
CREATE TABLE `spu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '产品编号',
  `spu_name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `category_id` bigint NULL DEFAULT NULL COMMENT '分类id',
  `description` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '产品描述',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '产品状态',
  `details` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '产品详细信息，富文本',
  `media_url` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '用于展示的图片或视频',
  `create_time` datetime NULL DEFAULT current_timestamp COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更改时间',
  `org_id` bigint NOT NULL COMMENT '组织id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 144 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '商品基本信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu
-- ----------------------------
INSERT INTO `spu` VALUES (36, '好东西', 4, '好勒痕', 0, NULL, NULL, '2022-05-08 11:47:57', '2025-03-08 18:58:47', 1);
INSERT INTO `spu` VALUES (38, '农夫三拳饮用水', 4, '好喝嘞很aaa', 0, NULL, NULL, '2022-05-08 13:19:25', '2025-03-08 18:58:41', 1);
INSERT INTO `spu` VALUES (140, '121', 3, '1212', 0, NULL, NULL, '2022-07-08 10:42:43', '2025-03-08 16:46:07', 1);
INSERT INTO `spu` VALUES (141, '121', 3, '2121', 0, NULL, NULL, '2022-09-22 15:37:44', '2025-03-08 16:46:03', 1);
INSERT INTO `spu` VALUES (142, '农夫山泉', 4, '我们不生产水', 0, NULL, NULL, '2022-10-10 11:07:36', '2025-03-08 18:58:35', 2);
INSERT INTO `spu` VALUES (143, '测试a', 6, '无uuuuu阿达啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊', 0, NULL, NULL, '2022-12-30 11:26:44', '2025-03-08 16:26:59', 2);

-- ----------------------------
-- Table structure for spu_category
-- ----------------------------
DROP TABLE IF EXISTS `spu_category`;
CREATE TABLE `spu_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `category_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `category_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `org_id` bigint NOT NULL COMMENT '组织id',
  `parend_id` bigint NULL DEFAULT 0 COMMENT '父id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_category
-- ----------------------------
INSERT INTO `spu_category` VALUES (3, '蔬菜', 'vege', '2022-04-01 13:12:53', 1, NULL);
INSERT INTO `spu_category` VALUES (4, '水', 'water', '2022-06-06 18:52:57', 1, NULL);
INSERT INTO `spu_category` VALUES (6, '食品', '1', '2022-12-30 11:06:46', 2, NULL);

-- ----------------------------
-- Table structure for spu_sku
-- ----------------------------
DROP TABLE IF EXISTS `spu_sku`;
CREATE TABLE `spu_sku`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `specs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '规格',
  `spu_id` bigint NOT NULL COMMENT '产品编号',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `stock` bigint NOT NULL DEFAULT 0 COMMENT '库存',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `image` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 566 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品交易单元' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_sku
-- ----------------------------
INSERT INTO `spu_sku` VALUES (520, '[\"100ML\", \"农夫山泉\", \"河南\"]', 38, 0.01, 960, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (521, '[\"100ML\", \"农夫山泉\", \"北京\"]', 38, 0.01, 15, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (522, '[\"100ML\", \"农夫山泉\", \"国外\"]', 38, 0.01, 3, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (523, '[\"100ML\", \"清泉\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (524, '[\"100ML\", \"清泉\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (525, '[\"100ML\", \"清泉\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (526, '[\"100ML\", \"流响\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (527, '[\"100ML\", \"流响\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (528, '[\"100ML\", \"流响\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (529, '[\"500ML\", \"农夫山泉\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:52.000', NULL);
INSERT INTO `spu_sku` VALUES (530, '[\"500ML\", \"农夫山泉\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (531, '[\"500ML\", \"农夫山泉\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (532, '[\"500ML\", \"清泉\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (533, '[\"500ML\", \"清泉\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (534, '[\"500ML\", \"清泉\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (535, '[\"500ML\", \"流响\", \"河南\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (536, '[\"500ML\", \"流响\", \"北京\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);
INSERT INTO `spu_sku` VALUES (537, '[\"500ML\", \"流响\", \"国外\"]', 38, 0.01, 20, '2022-07-07 15:03:53.000', NULL);

-- ----------------------------
-- Table structure for spu_specs_name
-- ----------------------------
DROP TABLE IF EXISTS `spu_specs_name`;
CREATE TABLE `spu_specs_name`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '属性名编号',
  `spu_id` bigint NOT NULL COMMENT '产品编号',
  `specs_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名',
  `description` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 174 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格名表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_specs_name
-- ----------------------------
INSERT INTO `spu_specs_name` VALUES (148, 36, '11', NULL, '2022-07-06 17:22:08');
INSERT INTO `spu_specs_name` VALUES (149, 36, '22', NULL, '2022-07-06 17:22:08');
INSERT INTO `spu_specs_name` VALUES (164, 38, '容量', NULL, '2022-07-07 14:57:04');
INSERT INTO `spu_specs_name` VALUES (168, 38, '品牌', NULL, '2022-07-07 15:03:52');
INSERT INTO `spu_specs_name` VALUES (169, 38, '产地', NULL, '2022-07-07 15:03:52');
INSERT INTO `spu_specs_name` VALUES (170, 141, '123', NULL, '2022-09-22 15:37:44');
INSERT INTO `spu_specs_name` VALUES (171, 141, '12', NULL, '2022-09-22 15:37:44');
INSERT INTO `spu_specs_name` VALUES (172, 142, '容量', NULL, '2022-10-10 11:07:36');
INSERT INTO `spu_specs_name` VALUES (173, 142, '产地', NULL, '2022-10-10 11:07:36');

-- ----------------------------
-- Table structure for spu_specs_value
-- ----------------------------
DROP TABLE IF EXISTS `spu_specs_value`;
CREATE TABLE `spu_specs_value`  (
  `id` bigint NOT NULL COMMENT 'id',
  `specs_name_id` bigint NULL DEFAULT NULL COMMENT '规格id',
  `specs_value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格值',
  `description` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of spu_specs_value
-- ----------------------------

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name_cn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_en` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `css_class` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 0,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `parent_id` int NULL DEFAULT 0,
  `sort` int NULL DEFAULT 0,
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 123 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据字典' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict` VALUES (1, '字典状态', 'dict_status', NULL, 0, '2021-11-02 23:39:58', '2022-02-06 16:22:00', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (2, '正常', '0', NULL, 0, '2021-11-02 23:40:01', '2022-02-06 16:22:00', 1, 0, NULL);
INSERT INTO `sys_dict` VALUES (3, '禁用', '1', NULL, 0, '2021-11-02 23:40:04', '2022-02-06 16:22:00', 1, 0, NULL);
INSERT INTO `sys_dict` VALUES (15, '角色状态', 'role_status', NULL, 0, '2021-12-15 14:49:47', '2022-02-06 16:22:00', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (16, '正常', '0', NULL, 0, '2021-12-15 14:49:58', '2022-02-06 16:22:00', 15, 0, NULL);
INSERT INTO `sys_dict` VALUES (17, '禁用', '1', NULL, 0, '2021-12-15 14:53:54', '2022-02-06 16:22:00', 15, 0, NULL);
INSERT INTO `sys_dict` VALUES (18, '菜单类型', 'menu_type', NULL, 0, '2021-12-15 15:51:35', '2022-02-06 16:22:00', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (19, '菜单', 'M', NULL, 0, '2021-12-15 15:51:54', '2022-02-06 16:22:00', 18, 0, NULL);
INSERT INTO `sys_dict` VALUES (20, '按钮', 'B', NULL, 0, '2021-12-15 15:52:01', '2022-02-06 16:22:00', 18, 0, NULL);
INSERT INTO `sys_dict` VALUES (21, '用户状态', 'user_status', NULL, 0, '2021-12-15 15:55:09', '2022-02-06 16:22:00', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (22, '正常', '1', NULL, 0, '2021-12-15 15:55:22', '2022-06-27 10:22:12', 21, 0, NULL);
INSERT INTO `sys_dict` VALUES (23, '停用', '2', NULL, 0, '2021-12-15 15:57:23', '2022-06-27 10:22:18', 21, 0, NULL);
INSERT INTO `sys_dict` VALUES (24, '菜单状态', 'menu_status', NULL, 0, '2021-12-15 16:06:09', '2022-02-06 16:22:00', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (25, '正常', '0', NULL, 0, '2021-12-15 16:06:18', '2022-02-06 16:22:01', 24, 0, NULL);
INSERT INTO `sys_dict` VALUES (26, '禁用', '1', NULL, 0, '2021-12-15 16:06:27', '2022-02-06 16:22:01', 24, 0, NULL);
INSERT INTO `sys_dict` VALUES (28, '正常', '0', NULL, 0, '2021-12-15 16:07:03', '2022-02-06 16:22:01', 27, 0, NULL);
INSERT INTO `sys_dict` VALUES (29, '禁用', '1', NULL, 0, '2021-12-15 16:07:13', '2022-02-06 16:22:01', 27, 0, NULL);
INSERT INTO `sys_dict` VALUES (30, '性别', 'sex', NULL, 0, '2021-12-16 08:53:28', '2022-02-06 16:22:01', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (31, '未知', '0', NULL, 0, '2021-12-16 08:53:41', '2022-02-06 16:22:01', 30, 0, NULL);
INSERT INTO `sys_dict` VALUES (32, '男', '1', NULL, 0, '2021-12-16 08:53:51', '2022-02-06 16:22:01', 30, 0, NULL);
INSERT INTO `sys_dict` VALUES (33, '女', '2', NULL, 0, '2021-12-16 08:54:01', '2022-02-06 16:22:01', 30, 0, NULL);
INSERT INTO `sys_dict` VALUES (53, '产品状态', 'spu_status', NULL, 0, '2022-02-07 15:59:35', '2022-04-27 14:35:19', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (54, '上架', '0', NULL, 0, '2022-02-07 15:59:54', NULL, 53, 0, NULL);
INSERT INTO `sys_dict` VALUES (55, '下架', '1', NULL, 0, '2022-02-07 16:00:04', NULL, 53, 0, NULL);
INSERT INTO `sys_dict` VALUES (64, '资源模块名', 'resource_name', NULL, 0, '2022-05-13 15:51:15', '2022-05-13 15:52:53', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (65, '系统', 'admin', NULL, 0, '2022-05-13 15:51:30', '2022-05-13 16:09:40', 64, 0, NULL);
INSERT INTO `sys_dict` VALUES (66, 'ERP-H', 'erp', NULL, 0, '2022-05-13 15:51:50', '2022-05-13 16:09:53', 64, 0, NULL);
INSERT INTO `sys_dict` VALUES (67, 'ERP-A', 'erp-wechat', NULL, 0, '2022-05-13 15:52:12', '2022-05-13 16:10:04', 64, 0, NULL);
INSERT INTO `sys_dict` VALUES (68, '应用账户状态', 'client_status', NULL, 0, '2022-05-13 16:13:53', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (69, '正常', '0', NULL, 0, '2022-05-13 16:14:02', NULL, 68, 0, NULL);
INSERT INTO `sys_dict` VALUES (71, '禁用', '1', NULL, 0, '2022-05-13 16:14:22', NULL, 68, 0, NULL);
INSERT INTO `sys_dict` VALUES (72, '客户状态', 'customer_status', NULL, 0, '2022-05-14 17:51:51', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (73, '正常', '0', NULL, 0, '2022-05-14 17:56:49', NULL, 72, 0, NULL);
INSERT INTO `sys_dict` VALUES (74, '禁用', '1', NULL, 0, '2022-05-14 17:56:57', NULL, 72, 0, NULL);
INSERT INTO `sys_dict` VALUES (75, '商户状态', 'shop_status', NULL, 0, '2022-05-14 22:15:33', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (76, '待审核', '0', NULL, 0, '2022-05-14 22:15:44', NULL, 75, 0, NULL);
INSERT INTO `sys_dict` VALUES (77, '正常', '1', NULL, 0, '2022-05-14 22:15:52', NULL, 75, 0, NULL);
INSERT INTO `sys_dict` VALUES (78, '审核不通过', '2', NULL, 0, '2022-05-14 22:16:03', '2022-06-14 13:00:03', 75, 0, NULL);
INSERT INTO `sys_dict` VALUES (84, '微信用户角色', 'wechat_user_role', NULL, 0, '2022-05-17 14:57:29', NULL, 0, 10, NULL);
INSERT INTO `sys_dict` VALUES (85, '用户', '1', NULL, 0, '2022-05-17 15:04:41', '2022-06-06 15:19:13', 84, 0, NULL);
INSERT INTO `sys_dict` VALUES (86, '配送员', '2', NULL, 0, '2022-05-17 15:05:15', '2022-05-17 15:07:30', 84, 1, NULL);
INSERT INTO `sys_dict` VALUES (87, '游客', '0', NULL, 0, '2022-05-17 15:07:42', '2022-12-29 11:07:28', 84, 0, NULL);
INSERT INTO `sys_dict` VALUES (88, '订单状态', 'order_status', NULL, 0, '2022-06-21 20:22:42', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (89, '已取消', '0', NULL, 0, '2022-06-21 20:22:56', '2022-12-26 14:28:56', 88, 0, NULL);
INSERT INTO `sys_dict` VALUES (90, '待付款', '1', NULL, 0, '2022-06-21 20:23:11', '2022-12-26 14:29:53', 88, 3, NULL);
INSERT INTO `sys_dict` VALUES (91, '待配送', '3', NULL, 0, '2022-06-21 20:24:48', '2022-12-26 14:30:02', 88, 5, NULL);
INSERT INTO `sys_dict` VALUES (92, '配送中', '5', NULL, 0, '2022-06-21 20:25:15', '2022-12-26 14:30:18', 88, 10, NULL);
INSERT INTO `sys_dict` VALUES (93, '售后中', '8', NULL, 0, '2022-06-21 20:26:35', '2022-12-26 14:30:29', 88, 4, NULL);
INSERT INTO `sys_dict` VALUES (94, '文件', 'file', NULL, 0, '2022-07-01 14:33:34', NULL, 64, 0, NULL);
INSERT INTO `sys_dict` VALUES (95, '客户类型', 'customer_type', NULL, 0, '2022-07-01 14:36:19', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (96, '个人', '0', NULL, 0, '2022-07-01 14:36:43', NULL, 95, 0, NULL);
INSERT INTO `sys_dict` VALUES (97, '公司', '1', NULL, 0, '2022-07-01 14:36:50', NULL, 95, 0, NULL);
INSERT INTO `sys_dict` VALUES (98, '活动状态', 'shop_activity_status', NULL, 0, '2022-07-02 12:24:24', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (99, '正常', '1', NULL, 0, '2022-07-02 12:24:36', NULL, 98, 0, NULL);
INSERT INTO `sys_dict` VALUES (100, '关闭', '2', NULL, 0, '2022-07-02 12:24:56', NULL, 98, 0, NULL);
INSERT INTO `sys_dict` VALUES (101, '活动代号', 'shop_activity_code', NULL, 0, '2022-07-02 12:25:22', '2022-07-02 12:27:06', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (102, '新用户立减', '1', NULL, 0, '2022-07-02 12:26:11', NULL, 101, 0, NULL);
INSERT INTO `sys_dict` VALUES (103, '满减', '2', NULL, 0, '2022-07-02 12:26:18', NULL, 101, 0, NULL);
INSERT INTO `sys_dict` VALUES (104, '折扣', '3', NULL, 0, '2022-07-02 12:26:28', NULL, 101, 0, NULL);
INSERT INTO `sys_dict` VALUES (105, '支付状态', 'order_pay_status', NULL, 0, '2022-07-06 15:40:17', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (106, '待支付', '0', NULL, 0, '2022-07-06 15:40:26', NULL, 105, 0, NULL);
INSERT INTO `sys_dict` VALUES (107, '已支付', '1', NULL, 0, '2022-07-06 15:40:46', NULL, 105, 0, NULL);
INSERT INTO `sys_dict` VALUES (108, '已退款', '2', NULL, 0, '2022-07-06 15:40:56', NULL, 105, 0, NULL);
INSERT INTO `sys_dict` VALUES (109, '支付类型', 'order_pay_type', NULL, 0, '2022-07-07 13:32:11', '2022-07-07 13:33:08', 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (110, '微信支付', '1', NULL, 0, '2022-07-07 13:32:24', '2022-07-22 10:27:57', 109, 0, NULL);
INSERT INTO `sys_dict` VALUES (111, '银联支付', '2', NULL, 0, '2022-07-07 13:32:34', '2022-07-22 10:27:59', 109, 0, NULL);
INSERT INTO `sys_dict` VALUES (113, '登录', 'login', NULL, 0, '2022-07-07 16:43:23', NULL, 112, 0, NULL);
INSERT INTO `sys_dict` VALUES (114, '新增数据', 'insert', NULL, 0, '2022-07-07 16:43:45', NULL, 112, 0, NULL);
INSERT INTO `sys_dict` VALUES (115, '修改数据', 'update', NULL, 0, '2022-07-07 16:43:56', NULL, 112, 0, NULL);
INSERT INTO `sys_dict` VALUES (116, '查询数据', 'select', NULL, 0, '2022-07-07 16:44:11', NULL, 112, 0, NULL);
INSERT INTO `sys_dict` VALUES (117, '删除数据', 'delete', NULL, 0, '2022-07-07 16:44:20', NULL, 112, 0, NULL);
INSERT INTO `sys_dict` VALUES (118, '微信用户状态', 'wechat_user_status', NULL, 0, '2022-07-19 09:16:12', NULL, 0, 0, NULL);
INSERT INTO `sys_dict` VALUES (119, '正常', '1', NULL, 0, '2022-07-19 09:17:00', NULL, 118, 0, NULL);
INSERT INTO `sys_dict` VALUES (120, '保存', 'save', NULL, 0, '2022-07-19 09:46:16', NULL, 112, 0, NULL);
INSERT INTO `sys_dict` VALUES (122, '已完成', '10', NULL, 0, '2022-06-21 20:26:35', '2022-07-22 10:27:29', 88, 4, NULL);

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`  (
  `id` bigint NOT NULL DEFAULT 0 COMMENT 'id',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方法（GET,POST,...）',
  `request_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求地址（/user?id=1）',
  `request_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求体',
  `response_code` tinyint NULL DEFAULT NULL COMMENT '响应状态码',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `org_id` bigint NULL DEFAULT NULL COMMENT '组织id',
  `ip_address` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `consume_time` int NULL DEFAULT NULL COMMENT '耗时',
  `error_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误消息',
  `error_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误码',
  `extra_data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展数据(记录修改之前，和修改之后)',
  `target_id` bigint NULL DEFAULT NULL COMMENT '操作对象',
  `operation_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
  `operation_desc` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M菜单 P权限）',
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '父菜单ID',
  `sort` int NULL DEFAULT 0 COMMENT '序号',
  `path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `permission` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `component_path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `status` int NULL DEFAULT 0 COMMENT '菜单状态（0正常 1停用）',
  `icon` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 151 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 'M', 0, 1, 'system', NULL, NULL, 0, NULL, '2021-10-05 10:40:22', '2022-01-22 19:50:44', '系统管理目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 'M', 1, 1, 'user', NULL, 'admin/user/userList', 0, NULL, '2021-10-05 10:40:22', '2025-03-08 12:41:10', '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 'M', 1, 2, 'role', NULL, 'admin/role/roleList', 0, NULL, '2021-10-05 10:40:22', '2025-03-08 12:41:16', '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 'M', 1, 5, 'menu', NULL, 'admin/menu/menuList', 0, NULL, '2021-10-05 10:40:22', '2025-03-08 12:41:22', '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (105, '数据字典', 'M', 1, 8, 'dict', NULL, 'admin/dict/dictList', 0, NULL, '2021-10-05 10:40:22', '2025-03-08 12:41:30', '字典管理菜单');
INSERT INTO `sys_menu` VALUES (110, '添加', 'P', 100, 0, '', 'sys:user:add', NULL, 0, NULL, '2021-12-15 16:58:35', '2023-02-13 17:06:18', NULL);
INSERT INTO `sys_menu` VALUES (113, '商品管理', 'M', 0, 3, 'product', NULL, NULL, 0, NULL, '2021-12-16 17:09:02', '2025-03-08 15:08:14', NULL);
INSERT INTO `sys_menu` VALUES (122, '订单管理', 'M', 0, 10, 'order', 'base:order:query', 'order/orderList', 0, NULL, '2021-12-15 16:58:35', '2025-03-08 13:09:55', '');
INSERT INTO `sys_menu` VALUES (123, '添加', 'P', 102, 0, '', 'sys:menu:add', NULL, 0, NULL, '2021-12-15 16:58:35', '2023-02-13 17:06:19', '');
INSERT INTO `sys_menu` VALUES (124, '编辑', 'P', 101, 0, '', 'sys:role:edit', NULL, 0, NULL, '2021-12-15 16:58:35', '2023-02-13 17:06:21', '');
INSERT INTO `sys_menu` VALUES (125, '产品管理', 'M', 113, 0, 'product', '', 'product/productList', 0, '#', '2022-01-22 20:18:31', '2025-03-08 12:41:41', '');
INSERT INTO `sys_menu` VALUES (126, '编辑', 'P', 102, 0, '', 'sys:menu:edit', NULL, 0, '#', '2022-01-22 20:21:42', '2023-02-13 17:06:25', '');
INSERT INTO `sys_menu` VALUES (127, '删除', 'P', 102, 0, '', 'sys:menu:delete', NULL, 0, '#', '2022-01-22 20:22:04', '2023-02-13 17:06:25', '');
INSERT INTO `sys_menu` VALUES (128, '添加', 'P', 105, 0, '', 'sys:dict:add', NULL, 0, '#', '2022-02-06 18:03:36', '2023-02-13 17:06:26', '');
INSERT INTO `sys_menu` VALUES (129, '添加', 'P', 101, 0, '', 'sys:role:add', NULL, 0, '#', '2022-02-06 18:09:09', '2023-02-13 17:06:26', '');
INSERT INTO `sys_menu` VALUES (130, '编辑', 'P', 100, 0, '', 'sys:user:edit', NULL, 0, '#', '2022-02-06 18:20:21', '2023-02-13 17:06:26', '');
INSERT INTO `sys_menu` VALUES (132, '产品类型', 'M', 113, 0, 'productType', NULL, 'product/productTypeList', 0, '#', '2022-02-09 15:36:48', '2025-03-08 12:41:51', '');
INSERT INTO `sys_menu` VALUES (145, '采购管理', 'M', 0, 12, 'purchase', NULL, NULL, 0, '#', '2025-03-08 16:32:07', '2025-03-08 16:37:59', '');
INSERT INTO `sys_menu` VALUES (146, '销售管理', 'M', 0, 15, 'sale', NULL, NULL, 0, '#', '2025-03-08 16:33:23', '2025-03-08 16:38:04', '');
INSERT INTO `sys_menu` VALUES (147, '仓库管理', 'M', 0, 20, 'warehouse', NULL, NULL, 0, '#', '2025-03-08 16:33:36', '2025-03-08 16:38:17', '');
INSERT INTO `sys_menu` VALUES (148, '财务管理', 'M', 0, 25, 'finance', NULL, NULL, 0, '#', '2025-03-08 16:33:51', '2025-03-08 16:38:21', '');
INSERT INTO `sys_menu` VALUES (149, '零售管理', 'M', 0, 13, 'retail', NULL, NULL, 0, '#', '2025-03-08 16:34:22', '2025-03-08 16:38:11', '');
INSERT INTO `sys_menu` VALUES (150, '基本信息', 'M', 0, 2, 'basic', NULL, NULL, 0, '#', '2025-03-08 16:45:02', '2025-03-08 16:45:11', '');

-- ----------------------------
-- Table structure for sys_org
-- ----------------------------
DROP TABLE IF EXISTS `sys_org`;
CREATE TABLE `sys_org`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组名',
  `sort` tinyint NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父ID',
  `ancestors` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组级ID列表，逗号分隔',
  `expand_params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7091961583521234945 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组织（区域）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_org
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `sort` int NULL DEFAULT 0 COMMENT '显示顺序',
  `scope` tinyint(1) NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'superAdmin', 1, NULL, '2021-10-05 10:40:22', '2022-02-06 18:07:51', '超级管理员');
INSERT INTO `sys_role` VALUES (2, '管理员', 'admin', 2, NULL, '2021-10-05 10:40:22', '2022-05-16 16:03:23', '普通角色');
INSERT INTO `sys_role` VALUES (4, 'test', '111', 1111, NULL, '2025-03-06 20:43:24', NULL, NULL);
INSERT INTO `sys_role` VALUES (5, '1212', '121', 21212, NULL, '2025-03-10 17:39:14', NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 1);
INSERT INTO `sys_role_menu` VALUES (1, 100);
INSERT INTO `sys_role_menu` VALUES (1, 101);
INSERT INTO `sys_role_menu` VALUES (1, 102);
INSERT INTO `sys_role_menu` VALUES (1, 105);
INSERT INTO `sys_role_menu` VALUES (1, 110);
INSERT INTO `sys_role_menu` VALUES (1, 113);
INSERT INTO `sys_role_menu` VALUES (1, 122);
INSERT INTO `sys_role_menu` VALUES (1, 123);
INSERT INTO `sys_role_menu` VALUES (1, 124);
INSERT INTO `sys_role_menu` VALUES (1, 125);
INSERT INTO `sys_role_menu` VALUES (1, 126);
INSERT INTO `sys_role_menu` VALUES (1, 127);
INSERT INTO `sys_role_menu` VALUES (1, 128);
INSERT INTO `sys_role_menu` VALUES (1, 129);
INSERT INTO `sys_role_menu` VALUES (1, 130);
INSERT INTO `sys_role_menu` VALUES (1, 132);
INSERT INTO `sys_role_menu` VALUES (1, 145);
INSERT INTO `sys_role_menu` VALUES (1, 146);
INSERT INTO `sys_role_menu` VALUES (1, 147);
INSERT INTO `sys_role_menu` VALUES (1, 148);
INSERT INTO `sys_role_menu` VALUES (1, 149);
INSERT INTO `sys_role_menu` VALUES (1, 150);
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 110);
INSERT INTO `sys_role_menu` VALUES (2, 113);
INSERT INTO `sys_role_menu` VALUES (2, 122);
INSERT INTO `sys_role_menu` VALUES (2, 125);
INSERT INTO `sys_role_menu` VALUES (2, 130);
INSERT INTO `sys_role_menu` VALUES (2, 132);
INSERT INTO `sys_role_menu` VALUES (2, 137);
INSERT INTO `sys_role_menu` VALUES (2, 138);
INSERT INTO `sys_role_menu` VALUES (2, 139);
INSERT INTO `sys_role_menu` VALUES (2, 140);
INSERT INTO `sys_role_menu` VALUES (2, 141);
INSERT INTO `sys_role_menu` VALUES (2, 142);
INSERT INTO `sys_role_menu` VALUES (4, 113);
INSERT INTO `sys_role_menu` VALUES (4, 125);
INSERT INTO `sys_role_menu` VALUES (7, 1);
INSERT INTO `sys_role_menu` VALUES (7, 105);
INSERT INTO `sys_role_menu` VALUES (7, 113);
INSERT INTO `sys_role_menu` VALUES (7, 122);
INSERT INTO `sys_role_menu` VALUES (7, 125);
INSERT INTO `sys_role_menu` VALUES (7, 128);
INSERT INTO `sys_role_menu` VALUES (8, 1);
INSERT INTO `sys_role_menu` VALUES (9, 113);
INSERT INTO `sys_role_menu` VALUES (9, 122);
INSERT INTO `sys_role_menu` VALUES (9, 125);
INSERT INTO `sys_role_menu` VALUES (9, 132);
INSERT INTO `sys_role_menu` VALUES (9, 137);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` tinyint(1) NULL DEFAULT 0 COMMENT '用户性别（0未知 1男 2女）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '帐号状态（0禁用 1启用）',
  `org_id` bigint NOT NULL COMMENT '组织',
  `role_id` bigint NOT NULL COMMENT '角色',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7091961589154185217 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'system', NULL, '1212', '$2a$10$mrL/kCDGGSLWBDr0b0JyquBanoZnXoiNrupX42r9AdlGpB.7Ze8Gy', '111111', 1, '', 1, 1, 1, '1212', '2023-07-21 13:38:27', '2025-03-06 18:55:36');

SET FOREIGN_KEY_CHECKS = 1;
