/*
 Navicat Premium Data Transfer

 Source Server         : 内网199
 Source Server Type    : MySQL
 Source Server Version : 100603
 Source Host           : ************:3308
 Source Schema         : wx_wdcy

 Target Server Type    : MySQL
 Target Server Version : 100603
 File Encoding         : 65001

 Date: 24/07/2025 11:01:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for device_info
-- ----------------------------
DROP TABLE IF EXISTS `device_info`;
CREATE TABLE `device_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `community_id` bigint NOT NULL COMMENT '小区ID',
  `dev_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备标识',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
  `dev_type_id` bigint NOT NULL COMMENT '设备类型ID',
  `dev_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备型号',
  `dev_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备IP',
  `dev_mac` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备Mac',
  `lng` double(11, 8) NULL DEFAULT NULL COMMENT '经度',
  `lat` double(11, 8) NULL DEFAULT NULL COMMENT '纬度',
  `alt` double(11, 8) NULL DEFAULT NULL COMMENT '海拔',
  `address` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `install_type` tinyint NOT NULL COMMENT '安装类型（0：地面；1：高空；2：地下室；3：水中；4：土中；5：建筑中；）',
  `note` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态（0：正常；1：离线；2：异常；）',
  `poly_coords` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地理围栏',
  `expand_params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展参数',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `open_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开放Id',
  `gb_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国标编码',
  `device_tag` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备标签',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `index_1`(`dev_no` ASC, `community_id` ASC, `dev_type_id` ASC) USING BTREE,
  INDEX `fk_reference_device_info_to_base_community`(`community_id` ASC) USING BTREE,
  INDEX `fk_reference_device_info_to_device_type`(`dev_type_id` ASC) USING BTREE,
  CONSTRAINT `fk_reference_device_info_to_base_community` FOREIGN KEY (`community_id`) REFERENCES `base_community` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_reference_device_info_to_device_type` FOREIGN KEY (`dev_type_id`) REFERENCES `device_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 433216495409991681 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for device_type
-- ----------------------------
DROP TABLE IF EXISTS `device_type`;
CREATE TABLE `device_type`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设备类型ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型代号',
  `alert_type` int NULL DEFAULT 0 COMMENT '警报类别（ 0：普通；1：条件；2：动作；）',
  `extend_params_tpl` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '运行参数模板',
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '父级ID',
  `ancestors` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '祖级列表(ansi逗号分隔）',
  `is_leaf` bit(1) NULL DEFAULT b'1' COMMENT '是否叶子节点（0否；1是）',
  `level` tinyint NULL DEFAULT 1 COMMENT '节点层级',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `community_id` bigint NOT NULL COMMENT '小区ID',
  `enable_show` bit(1) NOT NULL DEFAULT b'0' COMMENT '启用大屏展示',
  `ico_info_id` bigint NULL DEFAULT NULL COMMENT '图标ID（关联图标表ID）',
  `type_photo` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型图片（上传图片）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 425348773355474945 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备类型表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
