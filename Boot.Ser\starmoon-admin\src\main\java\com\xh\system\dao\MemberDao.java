package com.xh.system.dao;

import com.xh.system.domain.entity.Member;

import java.util.List;

/**
 * 客户端用户表(Member)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-21 20:29:36
 */
public interface MemberDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Member queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<Member> queryByList();


    /**
     * 新增数据
     *
     * @param member 实例对象
     * @return 影响行数
     */
    int insert(Member member);


    /**
     * 修改数据
     *
     * @param member 实例对象
     * @return 影响行数
     */
    int update(Member member);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

