package com.xh.print.service;

import com.xh.print.domain.dto.OrderDTO;
import com.xh.print.domain.entity.Order;
import com.xh.print.domain.form.OrderSubmitForm;
import com.xh.print.domain.form.PrintFile;
import com.xh.print.domain.search.OrderSearch;

import java.util.List;
import java.util.Map;

/**
 * 订单(Order)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13 23:09:27
 */
public interface OrderService {

    OrderDTO queryDetailById(Long id);

    List<OrderDTO> queryByList(OrderSearch search);

    Long uploadFile(PrintFile printFile);

    boolean update(Order order);

    boolean deleteById(Long id);

    /**
     * 提交订单
     * @param form 订单提交表单
     * @return 订单信息
     */
    Map<String, Object> submitOrder(OrderSubmitForm form);

    /**
     * 取消订单
     * @param orderId 订单ID
     * @return 取消结果
     */
    boolean cancelOrder(Long orderId);

    /**
     * 订单支付
     * @param orderId 订单ID
     * @return 支付参数
     */
    Map<String, Object> payOrder(Long orderId);
    
    boolean updateOrderAddress(Long orderId, Long addressId);
}
