package com.xh.ai.controller;


import com.xh.common.web.domain.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.File;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/3/30
 */
@Tag(name = "管理端，系统模块")
@Slf4j
@RestController
@RequestMapping("manage-api/v1/chat")
public class ChatController {

    @Autowired
    private VectorStore vectorStore;

    @Autowired
    private ChatClient chatClient;

    //TODO 上传知识库 通过文件上传 待完成 目前是临时本地上传
    public ResponseEntity<List<Document>> uploadKnowledgeBase(MultipartFile multipartFile) {
        log.info("开始加载本地知识库文档");
        File files = new File("D:\\Run\\Repository");
        if(files.exists()){
            for(File file: files.listFiles()){
                Resource resource = new FileSystemResource(file);
                TikaDocumentReader reader = new TikaDocumentReader(resource);
                // 读取文档
                List<Document> documents = reader.read();
                TokenTextSplitter splitter = TokenTextSplitter.builder().build();

                // 拆分文档
                List<Document> transform = splitter.transform(documents);
                // 向量化到数据库
                vectorStore.accept(transform);

                log.info("知识库文件加载完成：{}",file.getName());
            }
        }
        return ResponseEntity.success();
    }

    @Operation(summary = "语意检索")
    @GetMapping("search")
    public ResponseEntity<List<Document>> search(String message) {
        List<Document> documents = vectorStore.similaritySearch(SearchRequest.builder().query(message).similarityThreshold(0.5).topK(4).build());
        return ResponseEntity.success(documents);
    }

    @Operation(summary = "智能客服")
    @GetMapping(produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chat(String message) {

//        List<Document> documents = vectorStore.similaritySearch(SearchRequest.builder().query(message).similarityThreshold(0.5).topK(4).build());
//
//        StringBuffer prompt = new StringBuffer();
//        prompt.append("------------------------------------");
//        prompt.append("\n");
//        for(int i = 0; i < documents.size(); i++) {
//            prompt.append("第"+(i+1)+"条：") .append(documents.get(i).getText()).append("\n");
//        }
//        prompt.append("------------------------------------");
//        log.info("使用提示词：{}",prompt.toString());
        SecurityContext context = SecurityContextHolder.getContext();
        return chatClient.prompt()
                .user(message)
                .advisors(advisor -> advisor.param(AbstractChatMemoryAdvisor.CHAT_MEMORY_RETRIEVE_SIZE_KEY, 10))
                .stream().content().contextWrite(ReactiveSecurityContextHolder.withAuthentication(context.getAuthentication()));
    }
}
