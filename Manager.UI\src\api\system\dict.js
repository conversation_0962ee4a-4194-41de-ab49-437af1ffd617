import request from '@/utils/request'

export const listDict = (data) =>
	request({
		url: '/manage-api/v1/dict/page',
		method: 'get',
		params: data
	})
export const getDict = (id) =>
	request({
		url: '/manage-api/v1/dict',
		method: 'get',
		params: { id: id }
	})
export const listDictByNameEn = (nameEn) =>
	request({
		url: '/manage-api/v1/dict/search',
		method: 'get',
		params: { nameEn: nameEn }
	})
export const addDict = (data) =>
	request({
		url: '/manage-api/v1/dict',
		method: 'post',
		data: data
	})

export const editDict = (data) =>
	request({
		url: '/manage-api/v1/dict',
		method: 'put',
		data: data
	})



export const deleteDict = (id) =>
	request({
		url: '/manage-api/v1/dict',
		method: 'delete',
		params: { id: id }
	}) 