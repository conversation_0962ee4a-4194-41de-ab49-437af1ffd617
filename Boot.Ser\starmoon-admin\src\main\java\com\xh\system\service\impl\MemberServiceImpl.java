package com.xh.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.xh.system.dao.MemberDao;
import com.xh.system.domain.dto.MemberEditDTO;
import com.xh.system.domain.entity.Member;
import com.xh.system.service.MemberService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 客户端用户表(Member)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21 20:29:36
 */
@Service
public class MemberServiceImpl implements MemberService {

    @Autowired
    private MemberDao memberDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public Member queryById(Long id) {
        return memberDao.queryById(id);
    }

    /**
     * 通过分页查询
     *
     * @return 查询结果
     */
    @Override
    public PageInfo<Member> queryByPage() {
        return new PageInfo<>(memberDao.queryByList());
    }


    /**
     * 修改数据
     *
     * @return 实例对象
     */
    @Override
    public boolean update(MemberEditDTO dto) {
        Member member = new Member();
        BeanUtils.copyProperties(dto, member);
        return memberDao.update(member) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return memberDao.deleteById(id) > 0;
    }


}
