package com.xh.common.security.oauth2;

import com.xh.common.utils.ObjectUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.security.core.Authentication;

import java.util.UUID;

public class RedisAuthorizationCodeService implements AuthorizationCodeService{
    private static String AUTH_CODE = "authCode:";
    private RedisConnection redisConnection;

    public RedisAuthorizationCodeService(RedisConnection redisConnection){
        this.redisConnection = redisConnection;
    }

    @Override
    public String generateAuthorizationCode(Authentication authentication) {
        String code = String.valueOf(UUID.randomUUID()).replaceAll("-", "");
        byte[] authCodeKey = (AUTH_CODE+code).getBytes();
        redisConnection.set(authCodeKey, ObjectUtils.objectToByteArray(authentication));
        redisConnection.expire(authC<PERSON><PERSON><PERSON>,120);
        return code;
    }

    @Override
    public Authentication authorizationCode(String code) {
        byte[] authCodeKey = (AUTH_CODE+code).getBytes();
        byte[] bytes = redisConnection.get(authCodeKey);
        try {
            Authentication authentication = ObjectUtils.byteAryToObject(bytes, Authentication.class);
            redisConnection.del(authCodeKey);
            return authentication;
        }catch (Exception e){
            return null;
        }

    }

}
