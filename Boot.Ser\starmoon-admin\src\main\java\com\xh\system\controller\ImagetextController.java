package com.xh.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.system.domain.dto.ImagetextCreateDTO;
import com.xh.system.domain.dto.ImagetextEditDTO;
import com.xh.system.domain.entity.Imagetext;
import com.xh.system.service.ImagetextService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统图文表(Imagetext)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-21 08:30:39
 */
@Tag(name = "管理端，系统模块")
@RestController
@RequestMapping("manage-api/v1/imagetext")
public class ImagetextController {
   
    @Autowired
    private ImagetextService imagetextService;

    @Operation(summary = "通过分页查询图文")
    @GetMapping("page")
    public ResponseEntity<PageInfo<Imagetext>> queryByPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "type", required = false) String type
    ) {
        PageHelper.startPage(pageNum, pageSize);
        PageHelper.orderBy("id desc");

        return ResponseEntity.success(imagetextService.queryByPage(type));
    }

  
    @Operation(summary = "通过ID查询图文")
    @GetMapping
    public ResponseEntity<Imagetext> queryById(Long id) {
        return ResponseEntity.success(imagetextService.queryById(id));
    }

    @Operation(summary = "新增图文")
    @PostMapping
    public ResponseEntity<Long> insert(@Valid @RequestBody ImagetextCreateDTO dto) {
        return ResponseEntity.success(imagetextService.insert(dto));
    }

    @Operation(summary = "修改图文")
    @PutMapping
    public ResponseEntity<Boolean> update(@Valid @RequestBody ImagetextEditDTO dto) {
        return ResponseEntity.success(imagetextService.update(dto));
    }


    @Operation(summary = "删除图文")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.success(imagetextService.deleteById(id));
    }

}

