<template>
    <div class="page-content" >
        <print-specs-edit @search="search"></print-specs-edit>
        <print-specs-settings @search="search"></print-specs-settings>
        <div class="card card--search">
            <div class="search-container">
                <div class="search-form">
                    <el-input v-model="searchModel.keyword" placeholder="规格" clearable style="width: 200px;" />
                </div>
                <div class="search-buttons">
                    <el-button type="primary" @click="search" class="search-btn">搜索</el-button>
                    <el-button type="primary" @click="edit" class="add-btn">规格设置</el-button>
                </div>
            </div>
        </div>
        <div class="card card--table">
            <div class="table-col">
                <el-table :data="specsList" style="width: 100%">
                    <el-table-column prop="id" align="center" label="ID" />
                    <el-table-column prop="specs" align="center" label="规格" />
                    <el-table-column prop="amount" align="center" label="金额"  />
                    <el-table-column prop="note" align="center" label="备注" />
                    <el-table-column fixed="right" align="center" label="操作">
                        <template #default="scope">
                            <el-button link type="primary" size="small" @click="editsku(scope.row.id)">
                                编辑
                            </el-button>
                            <el-button link type="primary" size="small" @click="deleted(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagination-col">
                <el-pagination background layout="prev, pager, next" @current-change="currentChange"
                    @prev-click="prevClick" @next-click="nextClick" :total="total" />
            </div>
        </div>
    </div>
</template>

<script>
import { InfoFilled } from '@element-plus/icons-vue'
import {
	addSku,
	editSku,
	listSku,
	getSku
} from '@/api/print/printSettings'

import PrintSpecsSettings from '@/components/print/PrintSpecsSettings.vue'
import PrintSpecsEdit from '@/components/print/PrintSpecsEdit.vue'
import mitt from '@/utils/mitt'
export default {
    components:{ PrintSpecsSettings,PrintSpecsEdit },
    data() {
        return {
            uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
            imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
            headers: {
                'Authorization': JSON.parse(localStorage.getItem("token")).access_token
            },
            searchModel: {
                pageNum: 1,
                pageSize: 10
            },
            tempSpecsList: [],
            specsList: [],
            attributeList: [],
            isEdit: false,
            total: 0
        }
    },
    methods: {
        uploadSuccess(res,index) {
            console.log("res",res,index)
            if (res.code == 0) {
                const specs = this.tempSpecsList[index];
                specs.imageShow = true
                specs.imageUrl = res.data
            } else {
                this.$message.error(res.message)
            }
        },

        search() {
            try {
                listSku(this.searchModel)
                    .then(res => {
                        console.log("res",res)
                        this.specsList = res.data.data.list
                        this.total = res.data.data.total
                    })
                    .catch(err => {
                        this.$message.error(err.data.errorMessage)
                    })
            } catch (err) {
                this.$message.error(err.data.errorMessage)
            }
        },
        editsku(id){
            getSku(id)  
                .then(res => {
                    mitt.emit('openPrintSpecsEdit',res.data)
                }).catch(err => {
                    this.$message.error(err.data.errorMessage)
                })
        },
        edit() {
            mitt.emit('openEditSpecs')
        },
   
        deleted(id) {
            this.$confirm('删除地址, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteAddress(id)
                    .then(() => {
                        this.search()
                        this.$message.success('删除成功')
                    })
                    .catch(err => {
                        this.$message.error(err.data.errorMessage)
                    })
            }).catch(() => { })
        },
        async init() {
			try {
				const [ sku_res] = await Promise.all([
                    listSku(this.searchModel)
				])
				this.specsList = sku_res.data.data.list
				this.total = sku_res.data.data.total
			}catch (err) {
				this.$message.error(err.data.errorMessage)
			}
		}
    },
    created() {
        this.init()
    }
}
</script>

<style scoped>
.upload-cell {
  width: 80px;
  height: 80px;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>