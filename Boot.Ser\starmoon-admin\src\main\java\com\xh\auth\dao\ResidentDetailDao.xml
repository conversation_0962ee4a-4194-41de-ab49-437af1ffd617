<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.auth.dao.ResidentDetailDao">

    <resultMap type="com.xh.common.wechat.user.ResidentDetail" id="ResidentMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="residentName" column="resident_name" jdbcType="VARCHAR"/>
        <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="certificateType" column="certificate_type" jdbcType="VARCHAR"/>
        <result property="idCardNumber" column="id_card_number" jdbcType="VARCHAR"/>
        <result property="nativePlace" column="native_place" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="detail">
        id,resident_name,birthday,gender,certificate_type,id_card_number,native_place,phone,create_time,update_time
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="ResidentMap">
        select <include refid="detail"></include> from t_resident where id = #{id}
    </select>

    <select id="queryByIdCardNumber" resultMap="com.xh.auth.dao.MemberDetailDao.MemberDetailMap">
        select <include refid="detail"></include> from t_resident where id_card_number = #{idCardNumber}
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_resident(resident_name, birthday, gender, certificate_type, id_card_number, native_place, phone, create_time)
        values (#{residentName}, #{birthday}, #{gender}, #{certificateType}, #{idCardNumber}, #{nativePlace}, #{phone}, now())
    </insert>

    <update id="update">
        update t_resident
        <set>
            <if test="residentName != null and residentName != ''">
                resident_name = #{residentName},
            </if>
            <if test="birthday != null">
                birthday = #{birthday},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="certificateType != null and certificateType != ''">
                certificate_type = #{certificateType},
            </if>
            <if test="idCardNumber != null and idCardNumber != ''">
                id_card_number = #{idCardNumber},
            </if>
            <if test="nativePlace != null and nativePlace != ''">
                native_place = #{nativePlace},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from t_resident where id = #{id}
    </delete>

</mapper>

