package com.xh.print.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xh.print.dao.PrintSkuDao;
import com.xh.print.dao.PrintSpecsNameDao;
import com.xh.print.dao.PrintSpecsValueDao;
import com.xh.print.domain.entity.PrintAttr;
import com.xh.print.domain.entity.PrintSku;
import com.xh.print.domain.entity.PrintSpecsName;
import com.xh.print.domain.entity.PrintSpecsValue;
import com.xh.print.domain.form.PrintSettingsCreateForm;
import com.xh.print.domain.form.PrintSkuEditForm;
import com.xh.print.domain.form.PrintSpecsNameCreateForm;
import com.xh.print.domain.form.PrintSpecsValueCreateForm;
import com.xh.print.service.PrintSpecsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PrintSpecsServiceImpl implements PrintSpecsService {

    @Autowired
    private PrintSkuDao printSkuDao;

    @Autowired
    private PrintSpecsNameDao printSpecsNameDao;

    @Autowired
    private PrintSpecsValueDao printSpecsValueDao;

    @Override
    public PageInfo<PrintSku> queryByPage() {
        return new PageInfo<>(printSkuDao.queryByList());
    }

    @Override
    public List<PrintSku> queryByList() {
        return printSkuDao.queryByList();
    }

    @Override
    public List<PrintAttr> queryAttrByList() {
        return printSkuDao.queryAttrByList();
    }

    @Override
    public PrintSku queryById(Long id) {
        return printSkuDao.queryById(id);
    }

    /**
     * 根据参数生成规格快照，和规格
     * 规格：[黑色/M号,白色/M号]
     * 规快照：[{"颜色"："黑色","大小"："M号"},{"颜色"："白色","大小"："M号"}]
     * @param form
     * @return
     */
    @Override
    public boolean generate(PrintSettingsCreateForm form) {
        // 生成 PrintSku 列表
        List<PrintSpecsNameCreateForm> specsList = form.getSpecsList();
        if (specsList == null || specsList.isEmpty()) {
            return false;
        }
        // 规格名称集合
        List<String> specNames = new ArrayList<>();
        // 规格值表单集合
        List<List<PrintSpecsValueCreateForm>> specValuesLists = new ArrayList<>();
        for (PrintSpecsNameCreateForm spec : specsList) {
            specNames.add(spec.getSpecsName());
            specValuesLists.add(spec.getValues());
        }
        // 生成规格组合
        List<List<PrintSpecsValueCreateForm>> combinations = new ArrayList<>();
        buildCombinations(specValuesLists, 0, new ArrayList<>(), combinations);
        // 转换为 PrintSku 对象并插入
        List<PrintSku> skuList = new ArrayList<>();
        for (List<PrintSpecsValueCreateForm> combo : combinations) {
            PrintSku sku = new PrintSku();
            // specs: 值用 "/" 拼接
            String specs = combo.stream()
                    .map(PrintSpecsValueCreateForm::getSpecsValue)
                    .collect(Collectors.joining("/"));
            sku.setSpecs(specs);
            // specsSnapshot: 生成 JSON 格式的键值映射
            Map<String, String> snapshot = new LinkedHashMap<>();
            for (int i = 0; i < specNames.size(); i++) {
                snapshot.put(specNames.get(i), combo.get(i).getSpecsValue());
            }
            sku.setSpecsSnapshot(JSON.toJSONString(snapshot));
            skuList.add(sku);
        }
        printSkuDao.delete();
        printSpecsNameDao.delete();
        printSpecsValueDao.delete();
        // 批量插入
        for(PrintSku printSku: skuList){
            printSkuDao.insert(printSku);
        }
        for (PrintSpecsNameCreateForm spec : specsList) {
            PrintSpecsName printSpecsName = new PrintSpecsName();
            printSpecsName.setSpecsName(spec.getSpecsName());

            printSpecsNameDao.insert(printSpecsName);
            Long specsNameId = printSpecsName.getId();
            List<PrintSpecsValueCreateForm> values = spec.getValues();
            for (PrintSpecsValueCreateForm value : values) {
                PrintSpecsValue printSpecsValue = new PrintSpecsValue();
                printSpecsValue.setSpecsNameId(specsNameId);
                printSpecsValue.setSpecsValue(value.getSpecsValue());
                printSpecsValue.setDescription(value.getDescription());
                printSpecsValueDao.insert(printSpecsValue);
            }
        }
        return true;
    }

    @Override
    public boolean update(PrintSkuEditForm form) {

        PrintSku printSku = new PrintSku();
        BeanUtils.copyProperties(form,printSku);

        return update(printSku);
    }

    @Override
    public boolean update(PrintSku printSku) {
        return printSkuDao.update(printSku) > 0;
    }

    /**
     * 递归构建规格值组合
     */
    private void buildCombinations(List<List<PrintSpecsValueCreateForm>> lists, int depth,
                                   List<PrintSpecsValueCreateForm> current,
                                   List<List<PrintSpecsValueCreateForm>> results) {
        if (depth == lists.size()) {
            results.add(new ArrayList<>(current));
            return;
        }
        for (PrintSpecsValueCreateForm value : lists.get(depth)) {
            current.add(value);
            buildCombinations(lists, depth + 1, current, results);
            current.remove(current.size() - 1);
        }
    }
}
