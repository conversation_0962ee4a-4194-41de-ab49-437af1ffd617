<template>
  <el-dialog title="订单" width="40%" v-model="dialog.show" destroy-on-close>
    <el-form :model="orderModel" ref="form" label-width="100px">
      <el-form-item label="订单号" prop="orderNumber">
        <el-input v-model="orderModel.id" placeholder="请输入订单号"></el-input>
      </el-form-item>

      <el-form-item label="总金额" prop="totalAmount">
        <el-input v-model="orderModel.totalAmount" :min="0" :precision="2"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="orderModel.status" placeholder="请选择状态">
          <el-option label="待支付" value="pending"></el-option>
          <el-option label="已支付" value="paid"></el-option>
          <el-option label="已取消" value="cancelled"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-divider />
    <div class="detail-section">
      <h4>订单明细</h4>
      <el-table :data="detailList" style="width: 100%" stripe>
        <el-table-column prop="id" label="明细ID" width="100" />
        <el-table-column prop="spuName" label="商品名称" />
        <el-table-column prop="quantity" label="数量" />
        <el-table-column prop="amount" label="金额" />
      </el-table>
    </div>
    <template #footer>
      <el-button @click="dialog.show = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addOrder, editOrder } from '@/api/order/order'
import mitt from '@/utils/mitt'
export default {
  name: 'orderEdit',
  data() {
    return {
      loading: false,
      orderModel: { id: 0, orderNumber: '', customerId: '', totalAmount: 0, status: '' },
      detailList: [],
      dialog: { show: false, title: '' }
    }
  },
  methods: {
    onSubmit() {
      this.loading = true
      const operation = this.orderModel.id === 0 ? addOrder : editOrder
      operation(this.orderModel)
        .then(() => {
          this.$message.success('操作成功')
          this.dialog.show = false
          this.$emit('search')
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || err.message)
        })
        .finally(() => {
          this.loading = false
        })
    }
  },
  mounted() {
    this.$nextTick(() => {
      mitt.on('openOrderAdd', () => {
        this.orderModel = { id: 0, orderNumber: '', customerId: '', totalAmount: 0, status: '' }
        this.dialog.title = '添加订单'
        this.dialog.show = true
      })
      mitt.on('openOrderEdit', data => {
        this.orderModel = { ...data }
        this.detailList = data.detailList || data.items || []
        this.dialog.title = '订单详情'
        this.dialog.show = true
      })
    })
  },
  beforeUnmount() {
    mitt.off('openOrderAdd')
    mitt.off('openOrderEdit')
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
.detail-section h4 {
  margin: 10px 0;
}
</style> 