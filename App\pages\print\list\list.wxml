<view class="container">
  <custom-nav title="打印列表" color="#333" showBack="{{true}}"></custom-nav>
  <!-- 打印文件列表 -->
  <scroll-view scroll-y="true" class="print-list">
    <view class="print-item" wx:for="{{printList}}" wx:key="id">
        <!-- 文件信息区域 -->
        <view class="file-header">
          <view class="select-box" bindtap="toggleSelect" data-index="{{index}}">
            <view class="{{item.selected ? 'select-circle selected' : 'select-circle'}}">
              <view class="select-inner" wx:if="{{item.selected}}"></view>
            </view>
          </view>

          <!-- 根据文件类型显示对应图标 -->
          <view class="file-icon-wrapper">
            <image wx:if="{{item.type === 'pdf'}}" class="file-icon" src="/images/icon/pdf_icon.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'doc' || item.type === 'docx'}}" class="file-icon" src="/images/icon/word_icon.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'xls' || item.type === 'xlsx'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'ppt' || item.type === 'pptx'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'txt'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
            <image wx:elif="{{item.type === 'jpg' || item.type === 'jpeg' || item.type === 'png' || item.type === 'gif'}}" class="file-icon" src="/images/icon/image_icon.png" mode="aspectFit"></image>
            <image wx:else class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
          </view>

          <view class="file-info">
            <text class="file-name">{{item.fileName}}</text>
            <view class="file-specs" wx:if="{{item.skuId != null && item.skuId != 0}}">{{item.specs}}/第{{item.pageNum}}-{{item.pageNum + item.pageSize - 1}}页</view>
            <view class="file-specs" wx:else="">未设置</view>
          </view>

          <view class="delete-btn" bindtap="deleteFile" data-id="{{item.id}}">×</view>
        </view>

        <!-- 价格、数量和操作区域 -->
        <view class="file-footer">
          <view class="price-section">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
            <text class="price-unit">元</text>
          </view>

          <view class="quantity-controls">
            <view class="quantity-btn minus" bindtap="decreaseQuantity" data-index="{{index}}">-</view>
            <input class="quantity-input" type="number" value="{{item.quantity}}" bindinput="onQuantityInput" data-index="{{index}}" />
            <view class="quantity-btn plus" bindtap="increaseQuantity" data-index="{{index}}">+</view>
            <text class="quantity-unit">份</text>
          </view>

          <view class="settings-btn" bindtap="openSettings" data-id="{{item.id}}">打印设置</view>
        </view>
      </view>
    <!-- 提交打印前请先预览提示 -->
    <view class="preview-tip">
      提交打印前请先预览文件内容、检查打印设置
      若预览与您电脑手机查看不一致，可将文件转成PDF上传
      工厂实行自动化打印，无人工检查或处理文件
    </view>
  </scroll-view>

  <!-- 悬浮添加按钮 -->
  <view class="floating-add-btn" bindtap="continueUpload">
    <text class="add-icon">+</text>
  </view>

  <!-- 底部固定操作区 -->
  <view class="bottom-fixed-empty"></view>
  <view class="bottom-fixed-area">

    <!-- 结算栏 -->
    <view class="checkout-bar">
      <!-- 左侧选择区 -->
      <view class="left-selection" bindtap="selectAll">
        <view class="select-circle {{isAllSelected ? 'selected' : ''}}">
          <view class="select-inner" wx:if="{{isAllSelected}}"></view>
        </view>
        <text class="select-text">全选</text>
      </view>

      <!-- 右侧结算区 -->
      <view class="right-checkout">
        <view class="order-summary">
          <text>已选{{selectedCount}}件，合计：</text>
          <text class="price-symbol">¥</text>
          <text class="price-value">{{totalPrice}}</text>
        </view>
        <view class="checkout-btn" bindtap="checkout">结算</view>
      </view>
    </view>
  </view>
</view>