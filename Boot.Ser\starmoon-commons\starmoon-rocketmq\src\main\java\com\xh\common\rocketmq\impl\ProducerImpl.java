package com.xh.common.rocketmq.impl;

import com.xh.common.rocketmq.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProducerImpl implements Producer {

    @Autowired
    private RocketMQTemplate rocketmqTemplate;

    @Override
    public void syncSend(String topic, String message) {
        rocketmqTemplate.syncSend(topic, message);
    }

    @Override
    public void asyncSend(String topic, String message,SendCallback sendCallback) {
        rocketmqTemplate.asyncSend(topic, message, sendCallback);
    }

    @Override
    public void asyncSend(String topic, String message, SendCallback sendCallback,int delayLevel) {
        rocketmqTemplate.asyncSend(topic, MessageBuilder.withPayload(message).build(), sendCallback,3000,delayLevel);
    }


}
