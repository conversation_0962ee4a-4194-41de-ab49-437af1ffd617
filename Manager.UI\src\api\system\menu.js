import request from '@/utils/request'

export const listMenu = (data) =>
	request({
		url: '/manage-api/v1/menu/page',
		method: 'get',
		params: data
	})
export const getMenu = (id) =>
	request({
		url: '/manage-api/v1/menu',
		method: 'get',
		params: { id: id }
	})
export const loadMenu = () =>
	request({
		url: '/manage-api/v1/menu/load-menu',
		method: 'get'
	})
export const addMenu = (data) =>
	request({
		url: '/manage-api/v1/menu',
		method: 'post',
		data: data
	})
export const editMenu = (data) =>
	request({
		url: '/manage-api/v1/menu',
		method: 'put',
		data: data
	})
export const deleteMenu = (id) =>
	request({
		url: '/manage-api/v1/menu',
		method: 'delete',
		params: { id: id }
	}) 