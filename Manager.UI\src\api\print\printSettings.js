import request from '@/utils/request'

// 分页查询地址列表
export function listSku(params) {
  return request({
    url: '/manage-api/v1/print/sku/page',
    method: 'get',
    params: params
  })
}

// 获取地址详情
export function getSku(id) {
  return request({
    url: '/manage-api/v1/print/sku',
    method: 'get',
    params: { id: id }
  })
}

// 添加地址
export function addSku(data) {
  return request({
    url: '/manage-api/v1/print/sku',
    method: 'post',
    data: data
  })
}

// 编辑地址
export function editSku(data) {
  return request({
    url: '/manage-api/v1/print/sku',
    method: 'put',
    data: data
  })
}

// 删除地址
export function deleteSku(id) {
  return request({
    url: '/manage-api/v1/print/sku',
    method: 'delete',
    params: { id: id }
  })
} 