package com.xh.system.dao;

import com.xh.system.domain.entity.Address;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 地址表(Address)数据访问层
 */
public interface AddressDao {

    Address queryById(@Param("id") Long id);

    List<Address> queryByList(@Param("userId") Long userId);

    int insert(Address address);

    int update(Address address);

    int deleteById(@Param("id") Long id);
} 