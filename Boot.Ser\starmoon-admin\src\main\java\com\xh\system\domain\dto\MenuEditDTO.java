package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class MenuEditDTO {

    @Schema(description = "菜单ID")
    private Long id;

    @Schema(description = "菜单名")
    @NotBlank(message = "菜单名不能为空")
    private String menuName;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "菜单类型")
    private String menuType;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "菜单路径（显示路径如/index）")
    private String path;

    @Schema(description = "组件路径")
    private String componentPath;

    @Schema(description = "权限")
    private String icon;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "扩展数据")
    private String expandData;

}
