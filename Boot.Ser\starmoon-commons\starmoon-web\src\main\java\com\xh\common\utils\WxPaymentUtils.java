package com.xh.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xh.common.web.exception.ServiceException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 微信支付工具类
 * <AUTHOR>
 */
@Slf4j
public class WxPaymentUtils {

    @Getter
    private String appid;
    @Getter
    private String appSecret;

    private String mchid;

    private String serialNo;

    private String callBackUrl;

    @Getter
    private String secretKey;

    private PrivateKey privateKey;

    /**
     *
     * @param appid 应用id
     * @param mchid 商户号
     * @param serialNo 证书序号
     * @param callBackUrl 回调地址
     * @param secretKey v3密钥
     * @param privateKey 证书私钥
     */
    public WxPaymentUtils(String appid, String mchid, String serialNo , String callBackUrl, String secretKey, PrivateKey privateKey){
        this.appid = appid;
        this.mchid = mchid;
        this.serialNo = serialNo;
        this.callBackUrl = callBackUrl;
        this.secretKey = secretKey;
        this.privateKey = privateKey;
    }

    /**
     * 支付下单
     * @param description 商品描述
     * @param orderId 订单id
     * @param payAmount 支付金额
     * @param openid 用户id
     * @return
     */
    public Map<String, String> preOrder(String description,String orderId,double payAmount,String openid){

        callBackUrl = callBackUrl+"/order/pay-notify";

        Map<String, Object> params = makePreOrderParams(description,orderId,payAmount,openid);
        String body = JSON.toJSONString(params);
        long timestamp = System.currentTimeMillis() / 1000;
        String uri = "/v3/pay/transactions/jsapi";
        String nonceStr = String.valueOf(UUID.randomUUID()).replaceAll("-","");

        Map<String,String> header = new HashMap<>();

        header.put("Authorization","WECHATPAY2-SHA256-RSA2048 "+wechatAuthorization(uri,"POST",body,timestamp,nonceStr,privateKey));
        header.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        header.put("Accept", "application/json");

        String result = HttpClientUtils.post("https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi", body,header);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if(jsonObject.containsKey("prepay_id")){
            return makePaySignature(jsonObject.getString("prepay_id"),timestamp,nonceStr,privateKey);
        }
        throw new ServiceException("调用微信统一下单接口异常");

    }

    /**
     * 验证签名是否合法
     * @param body 验证的数据
     * @return
     */
    public boolean verify(String body){

        HttpServletRequest request = HttpClientUtils.getHttpServletRequest();

        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonceStr = request.getHeader("Wechatpay-Nonce");
        String serial = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");
        StringBuffer signatureData = new StringBuffer();

        String str = getCertificates();
        JSONObject jsonObject = JSONObject.parseObject(str, JSONObject.class);
        List<JSONObject> jsonObjectList = JSONArray.parseArray(jsonObject.getString("data"), JSONObject.class);
        PublicKey publicKey = null;
        for (JSONObject obj :jsonObjectList){
            if(serial.equals(obj.getString("serial_no"))){
                String encrypt_certificate = obj.getString("encrypt_certificate");
                JSONObject resource = JSONObject.parseObject(encrypt_certificate);
                String notifyData = SecretUtils.decryptByAesGcm(resource.getString("ciphertext"), resource.getString("nonce"), resource.getString("associated_data"), secretKey);
                publicKey = SecretUtils.stringToPublicKey(notifyData);
            }
        }

        signatureData.append(timestamp+"\n").append(nonceStr+"\n").append(body.trim()+"\n");

        return SecretUtils.verifySha256withRSA(signatureData.toString(),signature,publicKey);
    }

    private String getCertificates(){

        String uri = "/v3/certificates";
        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = String.valueOf(UUID.randomUUID()).replaceAll("-","");

        Map<String,String> header = new HashMap<>();
        String authorization = wechatAuthorization(uri, "GET", "", timestamp, nonceStr, privateKey);
        header.put("Authorization","WECHATPAY2-SHA256-RSA2048 "+authorization);
        header.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        header.put("Accept", "application/json");
        return HttpClientUtils.get("https://api.mch.weixin.qq.com/v3/certificates",null,header);
    }

    /**
     *  获取微信商户支付 授权
     * @param uri
     * @param method
     * @param body
     * @param timestamp
     * @param nonceStr
     * @param privateKey
     * @return
     */
    private String wechatAuthorization(String uri,String method,String body,long timestamp,String nonceStr, PrivateKey privateKey) {

        StringBuffer signatureData = new StringBuffer();

        signatureData
                .append(method+"\n")
                .append(uri+"\n")
                .append(timestamp+"\n")
                .append(nonceStr+"\n")
                .append(body+"\n");

        String signature = SecretUtils.signatureSha256withRSA(signatureData.toString(),privateKey);

        StringBuffer authorization = new StringBuffer();

        authorization
                .append("mchid=\""+appid+"\",")
                .append("nonce_str=\""+nonceStr+"\",")
                .append("timestamp=\""+timestamp+"\",")
                .append("serial_no=\""+serialNo+"\",")
                .append("signature=\""+signature+"\"");

        return authorization.toString();
    }


    /**
     *  创建支付签名
     * @param prepayId
     * @param timeStamp
     * @param nonceStr
     * @param privateKey
     * @return
     */
    private Map<String, String> makePaySignature(String prepayId,long timeStamp,String nonceStr,PrivateKey privateKey) {
        String packageStr = "prepay_id=" + prepayId;
        StringBuffer sb = new StringBuffer();
        sb.append(appid+"\n").append(timeStamp+"\n").append(nonceStr+"\n").append(packageStr+"\n");
        String sign = SecretUtils.signatureSha256withRSA(sb.toString(),privateKey);
        Map<String, String> miniPayParams = new HashMap<>();
        miniPayParams.put("timeStamp", String.valueOf(timeStamp));
        miniPayParams.put("nonceStr", nonceStr);
        miniPayParams.put("package", packageStr);
        miniPayParams.put("signType", "RSA");
        miniPayParams.put("paySign", sign);
        return miniPayParams;
    }

    /**
     * 封装下单参数
     * @param description
     * @param orderId
     * @param payAmount
     * @param openid
     * @return
     */
    private Map<String, Object> makePreOrderParams(String description,String orderId,double payAmount,String openid) {
        Map<String, Object> data = new HashMap<>(8);
        data.put("appid", appid);
        data.put("mchid", mchid);
        data.put("notify_url", callBackUrl);

        data.put("description", description);
        data.put("out_trade_no", orderId);

        Map<String,Object> amount = new HashMap<>(2);
        amount.put("total",(int)(payAmount*100));
        amount.put("currency","CNY");
        data.put("amount",amount);

        Map<String,Object> payer = new HashMap<>(2);
        payer.put("openid",openid);
        data.put("payer",payer);

        return data;
    }
}
