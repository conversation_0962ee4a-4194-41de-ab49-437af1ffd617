package com.xh.auth.server.impl;

import com.xh.auth.dao.UserDetailDao;
import com.xh.auth.server.AuthServer;
import com.xh.common.security.token.AccessToken;
import com.xh.common.security.token.TokenService;
import com.xh.common.security.user.UserDetail;
import com.xh.common.utils.HttpClientUtils;
import com.xh.common.utils.SecretUtils;
import com.xh.common.web.exception.ServiceException;
import com.xh.common.web.statecode.StatusCode;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.security.PrivateKey;
import java.util.Collection;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class AuthServerImpl implements AuthServer {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private UserDetailDao userDetailDao;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public AccessToken auth(String username, String password, PrivateKey privateKey) {
        if(privateKey != null){
            try {
                username = SecretUtils.decryptByPrivateKey(username, privateKey);

                password = SecretUtils.decryptByPrivateKey(password, privateKey);

            }catch (Exception e){
                throw new ServiceException(StatusCode.DATA_FORMAT_ERROR);
            }
        }

        UserDetail userDetail = userDetailDao.queryByUsername(username);

        if (userDetail == null) {
            throw new ServiceException(StatusCode.USERNAME_PASSWORD_ERROR);
        }
        if (!passwordEncoder.matches(password, userDetail.getPassword())) {
            throw new ServiceException(StatusCode.USERNAME_PASSWORD_ERROR);
        }


        String permissions = userDetail.getPermissions();
        Collection<? extends GrantedAuthority> authorities = permissions!=null? Stream.of(permissions.split(",")).filter(item -> item != null && item.length()>0).map(SimpleGrantedAuthority::new).collect(Collectors.toList()):null;

        userDetail.setPermissions(null);
        userDetail.setPassword(null);

        Authentication authentication = new UsernamePasswordAuthenticationToken(userDetail, null, authorities);

        AccessToken accessToken = new AccessToken();
        tokenService.storeAccessToken(accessToken,authentication);
        return accessToken;
    }

    @Override
    public Boolean logout() {
        HttpServletRequest request = HttpClientUtils.getHttpServletRequest();
        String authorization = request.getHeader(AccessToken.AUTH);
        tokenService.destroy(authorization,null);
        return true;
    }

}
