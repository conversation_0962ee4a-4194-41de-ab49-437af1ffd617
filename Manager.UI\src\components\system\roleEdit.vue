<template>
	<el-dialog width="25%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="roleModel" label-width="80px">
			<el-row>
				<el-col>
					<el-form-item label="角色名" prop="roleName">
						<el-input v-model="roleModel.roleName" placeholder="用户名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="角色码" prop="roleCode">
						<el-input v-model="roleModel.roleCode" placeholder="角色码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="排序" prop="sort">
						<el-input v-model="roleModel.sort" placeholder="排序"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" :rows="2" v-model="roleModel.remark" placeholder="备注内容"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="权限" prop="permissions">
						<el-tree :check-strictly="true" check-strictly @check="menuCheck"
							:default-checked-keys="roleModel.permissions" :data="menuList" show-checkbox node-key="id"
							:props="permissionProps" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;"
				@click="onSubmit">提交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { addRole, editRole } from "@/api/system/role"
import mitt from "@/utils/mitt"
export default {
	props: [ 'menuList' ],
	data() {
		return {
			loading: false,
			roleModel: {},
			dialog: {},
			permissionProps: {
				children: 'children',
				label: 'menuName',
			},
			rules: {
				roleName: [{
					required: true,
					message: '请输入角色名',
					trigger: 'blur',
				}],
				roleCode: [{
					required: true,
					message: '请输入角色码',
					trigger: 'blur',
				}],
			}
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.roleModel.id == 0) {
						addRole(this.roleModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							}).catch((err) => {
								this.$message.error(err.data.errorMessage)

							})
					} else {
						editRole(this.roleModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							}).catch((err) => {
								this.$message.error(err.data.errorMessage)

							})
					}

				}
			})
		},
		menuCheck(node, checked) {
			this.roleModel.permissions = checked.checkedKeys
		}
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openRoleEdit', (role) => {
				this.roleModel = role
				this.dialog.show = true
				this.dialog.title = "修改信息"
			})
			mitt.on('openRoleAdd', () => {
				this.roleModel = {
					id: 0
				}
				this.dialog.show = true
				this.dialog.title = "添加角色"
			})
		})
	}
}
</script>

<style scoped>
.role-edit-form :deep(.el-form-item) {
	margin-bottom: 12px !important;
}
</style> 