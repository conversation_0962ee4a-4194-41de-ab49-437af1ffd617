package com.xh.system.service;


import com.xh.system.domain.entity.ImagetextReadstatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

/**
 * 图文读取状态管理表(ImagetextReadstatus)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-21 08:50:39
 */
public interface ImagetextReadstatusService {

    /**
     * 通过ID查询单条数据
     *
     * @param imagetextId 主键
     * @return 实例对象
     */
    ImagetextReadstatus queryById(Long imagetextId);

    /**
     * 通过分页查询
     *
     * @param imagetextReadstatus 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<ImagetextReadstatus> queryByPage(ImagetextReadstatus imagetextReadstatus, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param imagetextReadstatus 实例对象
     * @return 实例对象
     */
    ImagetextReadstatus insert(ImagetextReadstatus imagetextReadstatus);

    /**
     * 修改数据
     *
     * @param imagetextReadstatus 实例对象
     * @return 实例对象
     */
    ImagetextReadstatus update(ImagetextReadstatus imagetextReadstatus);

    /**
     * 通过主键删除数据
     *
     * @param imagetextId 主键
     * @return 是否成功
     */
    boolean deleteById(Long imagetextId);

}
