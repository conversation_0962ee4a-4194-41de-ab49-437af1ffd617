/**
 * 主题管理模块
 *
 * 负责管理应用的主题设置，包括：
 * - 主题切换（亮色/暗色）
 * - 主题持久化存储
 * - 响应系统主题偏好
 * - 更新移动设备浏览器标题栏颜色
 */

// 主题配置
const THEME_CONFIG = {
  // 主题存储键名
  STORAGE_KEY: 'theme',

  // 主题类型
  TYPES: {
    LIGHT: 'light',
    DARK: 'dark'
  },

  // CSS类名
  CLASS_NAMES: {
    DARK: 'dark-theme'
  },

  // 主题颜色
  COLORS: {
    LIGHT: {
      BACKGROUND: '#ffffff',
      PRIMARY: '#409EFF',
      SECONDARY: '#67C23A',
      SUCCESS: '#67C23A',
      WARNING: '#E6A23C',
      DANGER: '#F56C6C',
      INFO: '#909399',
      TEXT_PRIMARY: '#303133',
      TEXT_REGULAR: '#606266',
      TEXT_SECONDARY: '#909399',
      BORDER: '#DCDFE6',
      THEME_COLOR: '#ffffff'
    },
    DARK: {
      BACKGROUND: '#1a1a1a',
      PRIMARY: '#409EFF',
      SECONDARY: '#67C23A',
      SUCCESS: '#67C23A',
      WARNING: '#E6A23C',
      DANGER: '#F56C6C',
      INFO: '#909399',
      TEXT_PRIMARY: '#E0E0E0',
      TEXT_REGULAR: '#CCCCCC',
      TEXT_SECONDARY: '#A0A0A0',
      BORDER: '#444444',
      THEME_COLOR: '#333333'
    }
  }
};

// 主题模块
const theme = {
  // 当前主题
  currentTheme: localStorage.getItem(THEME_CONFIG.STORAGE_KEY) || THEME_CONFIG.TYPES.LIGHT,

  /**
   * 切换主题
   * 在亮色和暗色主题之间切换
   */
  toggleTheme() {
    this.currentTheme = this.currentTheme === THEME_CONFIG.TYPES.LIGHT
      ? THEME_CONFIG.TYPES.DARK
      : THEME_CONFIG.TYPES.LIGHT;

    this.applyTheme();
  },

  /**
   * 设置特定主题
   * @param {string} theme - 要设置的主题 ('light' 或 'dark')
   */
  setTheme(theme) {
    if (theme !== THEME_CONFIG.TYPES.LIGHT && theme !== THEME_CONFIG.TYPES.DARK) {
      console.warn(`无效的主题类型: ${theme}，使用默认主题: ${THEME_CONFIG.TYPES.LIGHT}`);
      theme = THEME_CONFIG.TYPES.LIGHT;
    }

    this.currentTheme = theme;
    this.applyTheme();
  },

  /**
   * 应用主题到文档
   * 更新DOM元素的类和样式以反映当前主题
   */
  applyTheme() {
    // 移除所有主题类
    document.documentElement.classList.remove(THEME_CONFIG.CLASS_NAMES.DARK);
    document.body.classList.remove(THEME_CONFIG.CLASS_NAMES.DARK);

    const isDarkTheme = this.currentTheme === THEME_CONFIG.TYPES.DARK;
    const themeColors = isDarkTheme
      ? THEME_CONFIG.COLORS.DARK
      : THEME_CONFIG.COLORS.LIGHT;

    // 如果是暗色主题，添加类
    if (isDarkTheme) {
      document.documentElement.classList.add(THEME_CONFIG.CLASS_NAMES.DARK);
      document.body.classList.add(THEME_CONFIG.CLASS_NAMES.DARK);
    }

    // 设置 meta theme-color，改变移动设备浏览器的标题栏颜色
    this.updateMetaThemeColor(themeColors.THEME_COLOR);

    // 保存到本地存储
    localStorage.setItem(THEME_CONFIG.STORAGE_KEY, this.currentTheme);

    // 触发自定义事件，通知其他组件主题已更改
    document.dispatchEvent(new CustomEvent('themechange', {
      detail: {
        theme: this.currentTheme,
        colors: themeColors
      }
    }));
  },

  /**
   * 更新 meta theme-color
   * @param {string} color - 颜色值，例如 '#ffffff'
   */
  updateMetaThemeColor(color) {
    let meta = document.querySelector('meta[name="theme-color"]');
    if (!meta) {
      meta = document.createElement('meta');
      meta.name = 'theme-color';
      document.head.appendChild(meta);
    }
    meta.content = color;
  },

  /**
   * 初始化主题
   * 在应用启动时调用，设置初始主题并添加系统主题变化监听器
   */
  initTheme() {
    // 检查系统偏好
    if (!localStorage.getItem(THEME_CONFIG.STORAGE_KEY)) {
      const prefersDark = window.matchMedia &&
        window.matchMedia('(prefers-color-scheme: dark)').matches;

      if (prefersDark) {
        this.currentTheme = THEME_CONFIG.TYPES.DARK;
      }
    }

    // 应用主题
    this.applyTheme();

    // 监听系统主题变化
    if (window.matchMedia) {
      const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      // 使用适当的事件监听方法（兼容性处理）
      const mediaQueryListener = (e) => {
        // 只有当用户没有手动设置主题时，才跟随系统主题
        if (!localStorage.getItem(THEME_CONFIG.STORAGE_KEY)) {
          this.setTheme(e.matches ? THEME_CONFIG.TYPES.DARK : THEME_CONFIG.TYPES.LIGHT);
        }
      };

      // 添加事件监听器
      if (darkModeMediaQuery.addEventListener) {
        darkModeMediaQuery.addEventListener('change', mediaQueryListener);
      } else if (darkModeMediaQuery.addListener) {
        // 旧版浏览器兼容
        darkModeMediaQuery.addListener(mediaQueryListener);
      }
    }
  },

  /**
   * 获取当前主题的颜色配置
   * @returns {Object} 当前主题的颜色配置对象
   */
  getThemeColors() {
    return this.currentTheme === THEME_CONFIG.TYPES.DARK
      ? THEME_CONFIG.COLORS.DARK
      : THEME_CONFIG.COLORS.LIGHT;
  },

  /**
   * 检查是否为暗色主题
   * @returns {boolean} 如果当前是暗色主题则返回true
   */
  isDarkTheme() {
    return this.currentTheme === THEME_CONFIG.TYPES.DARK;
  }
};

export default theme;
