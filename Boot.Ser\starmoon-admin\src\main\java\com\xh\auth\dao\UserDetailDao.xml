<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.auth.dao.UserDetailDao">

    <resultMap type="com.xh.common.security.user.UserDetail" id="UserMap">
        <id property="id" column="id"/>
        <result property="userName" column="user_name" />
        <result property="nickName" column="nick_name" />
        <result property="mobile" column="mobile" />
        <result property="avatarUrl" column="avatar" />
        <result property="password" column="password" />
        <result property="status" column="status" />
        <result property="permissions" column="permissions"/>
        <result property="roleId" column="role_id"/>
        <result property="roleCode" column="role_code"/>
    </resultMap>


    <update id="updateStatus">
        update sys_user
        set status = #{status}
        where id = #{id}
    </update>

    <select id="queryByUsername" resultMap="UserMap">
        SELECT
        a.*,
        b.role_code role_code
        FROM
        sys_user a LEFT JOIN sys_role b ON a.role_id = b.id
        LEFT JOIN sys_role_menu c ON a.role_id = c.role_id
        LEFT JOIN sys_menu d ON d.id = c.menu_id
        WHERE
        a.user_name = #{username}
        GROUP BY
        a.id
    </select>

</mapper>

