.scroll-container {
  height: 100vh;
  box-sizing: border-box;
}

.container {
  padding: 0 0 20rpx 0;
  background-color: #f7f7f7;
}

/* 订单状态 */
.status-section {
  background: linear-gradient(to right, #ff9933, #ff6633);
  color: #fff;
  padding: 60rpx 30rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(255, 102, 51, 0.2);
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
}

.status-section:after {
  content: "";
  position: absolute;
  bottom: -30rpx;
  right: -30rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.status-content {
  position: relative;
  z-index: 2;
}

.status-text {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.status-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 信息卡片通用样式 */
.info-card {
  background-color: #fff;
  margin: 20rpx 20rpx 0;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modify-address-btn {
  font-size: 26rpx;
  color: #ff6633;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff6633;
  border-radius: 20rpx;
  background-color: rgba(255, 102, 51, 0.1);
}

.card-title:before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #ff6633;
  border-radius: 3rpx;
}

/* 订单信息 */
.info-item {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
  font-size: 28rpx;
  color: #333;
}

.info-label {
  color: #999;
}

.info-value {
  color: #333;
}

/* 配送信息 */
.address-info {
  padding: 10rpx 0;
}

.user-info {
  display: flex;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
}

.user-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 订单内容 */
.goods-list {
  padding: 10rpx 0;
}

.goods-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-params {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
  line-height: 1.4;
}

.goods-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 120rpx;
}

.goods-price {
  font-size: 28rpx;
  color: #ff6633;
  margin-bottom: 10rpx;
}

.goods-count {
  font-size: 24rpx;
  color: #999;
}

/* 价格明细 */
.price-detail {
  padding-top: 20rpx;
  border-top: 1rpx dashed #eee;
  margin-top: 20rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
  font-size: 28rpx;
  color: #666;
}

.price-item.discount {
  color: #ff6633;
}

.price-item.total {
  margin-top: 10rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f5f5f5;
  font-weight: 500;
  color: #333;
}

.total-price {
  color: #ff6633;
  font-size: 34rpx;
  font-weight: 600;
}

/* 底部按钮占位，防止内容被固定按钮遮挡 */
.bottom-placeholder {
  height: 120rpx;
}

/* 底部按钮 */
.bottom-btns {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 30rpx;
  border-radius: 45rpx;
  margin: 0 15rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  border: none;
}

.pay-btn {
  background: linear-gradient(to right, #ff9933, #ff6633);
  color: #fff;
  border: none;
  box-shadow: 0 6rpx 10rpx rgba(255, 102, 51, 0.2);
} 