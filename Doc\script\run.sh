jars=(eureka.jar gateway.jar file.jar oauth.jar admin.jar erp.jar wechat-erp.jar)
ports=(8880 8080 8000 8001 8002 8003 8004)

start(){

	for item in ${jars[@]}
	do 
		nohup java -jar $item >./../logs/xinyue.log &
	done
	sleep 2
}

stop(){

	for item in ${ports[@]}
	do 
		lsof -i :$item | grep java | awk '{print $2}' | xargs -r kill -9
	done
	
}

status(){

	for item in ${ports[@]}
	do 
		if test $(lsof -i :$item | grep java | awk '{print $2}' | xargs -r | awk '{print $1}')
		then
			echo $item'运行中'
		else
			echo $item'未运行'
		fi
	done
	
}


#根据输入参数，选择执行对应方法
case "$1" in
  "start")
    start;;
  "stop")
    stop;;
  "status")
    status;;
esac

