package com.xh.common.security.oauth2;

import org.springframework.security.core.Authentication;

public interface AuthorizationCodeService {
    /**
     * 获取授权码
     * 1 标准方式为 表单登录，扫码，和本地应用授权方式获取授权码。
     * 但是本项目为web登录,在自己的系统内获取授权码，则根据当前登录用户的方式获取
     * @param authentication 身份
     * @return 授权码
     */
    String generateAuthorizationCode(Authentication authentication);

    /**
     * 授权码获取身份
     * @param code 授权码
     * @return 身份
     */
    Authentication authorizationCode(String code);
}
