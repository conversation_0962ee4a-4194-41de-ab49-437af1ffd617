package com.xh.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.xh.system.dao.MenuDao;
import com.xh.system.domain.dto.MenuCreateDTO;
import com.xh.system.domain.dto.MenuDTO;
import com.xh.system.domain.dto.MenuEditDTO;
import com.xh.system.domain.entity.Menu;
import com.xh.system.service.MenuService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单权限表(Menu)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 09:41:04
 */
@Service
public class MenuServiceImpl implements MenuService {
    @Autowired
    private MenuDao menuDao;

    @Override
    public Menu queryById(Long id) {
        return menuDao.queryById(id);
    }

    @Override
    public List<Menu> queryByList(String menuName, Long parentId, Long roleId) {
        return menuDao.queryByList(menuName, parentId, roleId);
    }

    @Override
    public PageInfo<MenuDTO> queryByPage(String menuName, Long roleId) {
        List<Menu> rootList = menuDao.queryByList(menuName, 0L, roleId);
        PageInfo<Menu> pageInfo = new PageInfo<>(rootList);

        List<Menu> menuList = menuDao.queryByList(menuName, null, roleId);

        List<MenuDTO> collect = rootList.stream().map(menu -> {
            MenuDTO dto = new MenuDTO();
            BeanUtils.copyProperties(menu, dto);
            dto.setChildren(getChildren(menu.getId(), menuList));
            return dto;
        }).collect(Collectors.toList());

        PageInfo<MenuDTO> info = new PageInfo<>(collect);
        info.setTotal(pageInfo.getTotal());

        return info;

    }


    @Override
    public Long insert(MenuCreateDTO dto) {
        Menu menu = new Menu();
        BeanUtils.copyProperties(dto,menu);
        menuDao.insert(menu);
        return menu.getId();
    }

    @Override
    public boolean update(MenuEditDTO dto) {
        Menu menu = new Menu();
        BeanUtils.copyProperties(dto,menu);
        return menuDao.update(menu) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return menuDao.deleteById(id) > 0;
    }

    private List<MenuDTO> getChildren(Long parentId, List<Menu> menuAll) {
        List<Menu> menuList = menuAll.stream().filter(temp -> temp.getParentId().longValue() == parentId.longValue()).toList();

        return menuList.stream().map(item ->{
            MenuDTO dto = new MenuDTO();
            BeanUtils.copyProperties(item,dto);
            dto.setChildren(getChildren(item.getId(),menuAll));
            return dto;
        }).collect(Collectors.toList());
    }
}
