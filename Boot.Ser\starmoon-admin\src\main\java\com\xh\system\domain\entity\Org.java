package com.xh.system.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * (UserGroup)实体类
 *
 * <AUTHOR>
 * @since 2025-05-20 09:45:34
 */
@Data
public class Org{

    @Schema(description = "组织ID")
    private Long id;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "祖级列表/分割")
    private String ancestors;
}

