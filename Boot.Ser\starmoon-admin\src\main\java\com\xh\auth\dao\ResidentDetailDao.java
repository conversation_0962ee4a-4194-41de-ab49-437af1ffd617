package com.xh.auth.dao;

import com.xh.common.wechat.user.ResidentDetail;

/**
 * 住户(Resident)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-21 20:37:36
 */
public interface ResidentDetailDao {

    ResidentDetail queryById(Long id);

    /**
     * 通过身份证号查询住户
     *
     * @return ResidentDetail
     */
    ResidentDetail queryByIdCardNumber(String idCardNumber);
    /**
     * 新增数据
     *
     * @return 影响行数
     */
    int insert(ResidentDetail resident);

    /**
     * 修改数据
     *
     * @param resident 实例对象
     * @return 影响行数
     */
    int update(ResidentDetail resident);

}

