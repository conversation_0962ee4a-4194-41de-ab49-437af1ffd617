.container {
  padding-bottom: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.upload-methods {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  margin-top: 20rpx;
}

.upload-item {
  width: 33.33%;
  box-sizing: border-box;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.upload-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
}

.upload-icon image {
  width: 70%;
  height: 70%;
}

.upload-info {
  width: 100%;
  text-align: center;
}

.upload-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.upload-desc {
  font-size: 22rpx;
  color: #999;
}

.upload-tips {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx;
  position: fixed;
  bottom: 60rpx;
  left: 0;
  right: 0;
} 