package com.xh.system.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户编辑DTO
 */
@Data
public class UserEditDTO {

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long id;

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "头像地址")
    private String avatarUrl;

    @Schema(description = "组织ID")
    @NotNull(message = "所属组织不能为空")
    private Long orgId;

    @Schema(description = "角色ID")
    @NotNull(message = "角色不能为空")
    private Long roleId;

    @Schema(description = "备注")
    private String note;
}
