package com.xh.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xh.common.log.annotation.OperationLog;
import com.xh.common.web.domain.ResponseEntity;
import com.xh.system.domain.dto.DictCreateDTO;
import com.xh.system.domain.dto.DictDTO;
import com.xh.system.domain.dto.DictEditDTO;
import com.xh.system.domain.entity.Dict;
import com.xh.system.domain.vo.DictVO;
import com.xh.system.service.DictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * (Dict)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-20 13:11:10
 */
@Tag(name = "管理端，系统模块")
@RestController
@RequestMapping("manage-api/v1/dict")
public class DictController {

    @Autowired
    private DictService dictService;

    @Operation(summary = "根据代码查询字典")
    @GetMapping("/search")
    @PreAuthorize("hasAuthority('sys:dict:query')")
    public ResponseEntity<List<Dict>> queryDict(@Parameter(name = "nameEn", description = "字典代码", required = true) String nameEn) {
        return ResponseEntity.success(dictService.queryDictByNameEn(nameEn));
    }

    @Operation(summary = "根据ID查询字典")
    @GetMapping
    @PreAuthorize("hasAuthority('sys:dict:query')")
    public ResponseEntity<Dict> queryById(@Parameter(name = "id", description = "字典ID", required = true) Long id) {
        return ResponseEntity.success(dictService.queryById(id));
    }

    @Parameters(value = {
            @Parameter(name = "pageNum", description = "页码"),
            @Parameter(name = "pageSize", description = "每页数量"),
            @Parameter(name = "nameEn", description = "字典代码"),
            @Parameter(name = "nameCn", description = "字典名称"),
            @Parameter(name = "status", description = "状态")
    })
    @Operation(summary = "通过分页查询")
    @GetMapping("page")
    @PreAuthorize("hasAuthority('sys:dict:query')")
    public ResponseEntity<PageInfo<DictVO>> queryByPage(@RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                        @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                                        @RequestParam(value = "nameEn", required = false) String nameEn,
                                                        @RequestParam(value = "nameCn", required = false) String nameCn,
                                                        @RequestParam(value = "status", required = false) Integer status
    ) {
        PageHelper.startPage(pageNum, pageSize);
        PageHelper.orderBy("sort asc");
        PageInfo<DictDTO> pageInfo = dictService.queryByPage(nameEn, nameCn, status, 0L);
        return ResponseEntity.success(pageInfo, DictVO.class);
    }

    @Operation(summary = "字典添加")
    @OperationLog(title = "字典添加")
    @PreAuthorize("hasAuthority('sys:dict:add')")
    @PostMapping
    public ResponseEntity<Long> insert(@Valid @RequestBody DictCreateDTO dto) {
        return ResponseEntity.success(dictService.insert(dto));
    }

    @Operation(summary = "字典编辑")
    @OperationLog(title = "字典编辑")
    @PreAuthorize("hasAuthority('sys:dict:edit')")
    @PutMapping
    public ResponseEntity<Boolean> update(@Valid @RequestBody DictEditDTO dto) {
        return ResponseEntity.success(dictService.update(dto));
    }

    @Operation(summary = "字典删除")
    @OperationLog(title = "字典删除")
    @PreAuthorize("hasAuthority('sys:dict:delete')")
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(@Parameter(name = "id", description = "字典ID", required = true) Long id) {
        return ResponseEntity.success(dictService.deleteById(id));
    }

}