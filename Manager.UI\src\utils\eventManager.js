/**
 * 事件监听器管理工具
 *
 * 解决mitt事件监听器重复注册的问题
 * 提供安全的事件监听器注册和清理机制
 */
import mitt from '@/utils/mitt'

/**
 * 事件监听器管理器
 */
class EventManager {
  constructor() {
    // 存储组件实例和其监听器的映射
    this.componentListeners = new WeakMap()
    // 存储全局事件监听器，用于去重
    this.globalEventHandlers = new Map()
  }

  /**
   * 为组件注册事件监听器
   * @param {Object} component - Vue组件实例
   * @param {string} eventName - 事件名称
   * @param {Function} handler - 事件处理函数
   */
  on(component, eventName, handler) {
    // 获取组件的监听器列表
    let listeners = this.componentListeners.get(component)
    if (!listeners) {
      listeners = new Map()
      this.componentListeners.set(component, listeners)
    }

    // 如果已经有同名事件的监听器，先清理
    if (listeners.has(eventName)) {
      const oldHandler = listeners.get(eventName)
      mitt.off(eventName, oldHandler)
      console.log(`[EventManager] 清理组件重复监听器: ${eventName}`)
    }

    // 检查全局是否已有相同事件的处理器
    if (this.globalEventHandlers.has(eventName)) {
      const existingHandlers = this.globalEventHandlers.get(eventName)
      // 清理所有已存在的处理器
      existingHandlers.forEach(existingHandler => {
        mitt.off(eventName, existingHandler)
      })
      console.log(`[EventManager] 清理全局重复监听器: ${eventName}, 数量: ${existingHandlers.length}`)
    }

    // 注册新的监听器
    mitt.on(eventName, handler)
    listeners.set(eventName, handler)

    // 记录到全局处理器列表
    if (!this.globalEventHandlers.has(eventName)) {
      this.globalEventHandlers.set(eventName, new Set())
    }
    this.globalEventHandlers.get(eventName).add(handler)

    console.log(`[EventManager] 注册事件监听器: ${eventName}`)
  }

  /**
   * 移除组件的特定事件监听器
   * @param {Object} component - Vue组件实例
   * @param {string} eventName - 事件名称
   */
  off(component, eventName) {
    const listeners = this.componentListeners.get(component)
    if (!listeners) return

    const handler = listeners.get(eventName)
    if (handler) {
      mitt.off(eventName, handler)
      listeners.delete(eventName)

      // 从全局处理器列表中移除
      if (this.globalEventHandlers.has(eventName)) {
        this.globalEventHandlers.get(eventName).delete(handler)
        if (this.globalEventHandlers.get(eventName).size === 0) {
          this.globalEventHandlers.delete(eventName)
        }
      }

      console.log(`[EventManager] 移除事件监听器: ${eventName}`)
    }
  }

  /**
   * 清理组件的所有事件监听器
   * @param {Object} component - Vue组件实例
   */
  cleanup(component) {
    const listeners = this.componentListeners.get(component)
    if (!listeners) return

    // 清理所有监听器
    for (const [eventName, handler] of listeners) {
      mitt.off(eventName, handler)

      // 从全局处理器列表中移除
      if (this.globalEventHandlers.has(eventName)) {
        this.globalEventHandlers.get(eventName).delete(handler)
        if (this.globalEventHandlers.get(eventName).size === 0) {
          this.globalEventHandlers.delete(eventName)
        }
      }

      console.log(`[EventManager] 清理事件监听器: ${eventName}`)
    }

    // 清空组件的监听器记录
    this.componentListeners.delete(component)
  }

  /**
   * 触发事件
   * @param {string} eventName - 事件名称
   * @param {*} data - 事件数据
   */
  emit(eventName, data) {
    console.log(`[EventManager] 触发事件: ${eventName}`)
    mitt.emit(eventName, data)
  }

  /**
   * 强制清理所有指定事件的监听器
   * @param {string} eventName - 事件名称
   */
  forceCleanEvent(eventName) {
    if (this.globalEventHandlers.has(eventName)) {
      const handlers = this.globalEventHandlers.get(eventName)
      handlers.forEach(handler => {
        mitt.off(eventName, handler)
      })
      this.globalEventHandlers.delete(eventName)
      console.log(`[EventManager] 强制清理事件: ${eventName}, 清理数量: ${handlers.size}`)
    }
  }
}

// 创建全局事件管理器实例
const eventManager = new EventManager()

/**
 * Vue组件混入，自动管理事件监听器
 */
export const eventManagerMixin = {
  beforeUnmount() {
    // Vue 3 生命周期钩子
    eventManager.cleanup(this)
  },

  beforeDestroy() {
    // Vue 2 生命周期钩子（向后兼容）
    eventManager.cleanup(this)
  },

  methods: {
    /**
     * 安全地注册事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     */
    $safeOn(eventName, handler) {
      // 先强制清理该事件的所有监听器
      eventManager.forceCleanEvent(eventName)
      // 然后注册新的监听器
      eventManager.on(this, eventName, handler)
    },

    /**
     * 移除事件监听器
     * @param {string} eventName - 事件名称
     */
    $safeOff(eventName) {
      eventManager.off(this, eventName)
    },

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {*} data - 事件数据
     */
    $safeEmit(eventName, data) {
      eventManager.emit(eventName, data)
    },

    /**
     * 强制清理指定事件的所有监听器
     * @param {string} eventName - 事件名称
     */
    $forceCleanEvent(eventName) {
      eventManager.forceCleanEvent(eventName)
    }
  }
}

export default eventManager
